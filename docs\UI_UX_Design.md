# HomeReno UI/UX 设计文档

## 1. 设计系统概述

### 1.1 设计原则
- **简洁明了**: 界面干净整洁，信息层次清晰
- **用户友好**: 降低学习成本，提供直观的操作体验
- **多语言适配**: 支持英语和中文，考虑文本长度差异
- **响应式设计**: 适配不同设备和屏幕尺寸
- **可访问性**: 符合WCAG 2.1 AA级标准

### 1.2 设计语言
- **品牌色彩**: 主色调采用信任蓝(#2563EB)，辅助色为温暖橙(#F59E0B)
- **字体系统**: 英文使用Inter，中文使用思源黑体
- **间距系统**: 基于8px网格系统
- **圆角规范**: 按钮4px，卡片8px，模态框12px
- **阴影系统**: 三级阴影深度

## 2. 色彩系统

### 2.1 主色调
```css
/* 主色调 - 信任蓝 */
--primary-50: #EFF6FF;
--primary-100: #DBEAFE;
--primary-200: #BFDBFE;
--primary-300: #93C5FD;
--primary-400: #60A5FA;
--primary-500: #3B82F6; /* 主色 */
--primary-600: #2563EB;
--primary-700: #1D4ED8;
--primary-800: #1E40AF;
--primary-900: #1E3A8A;

/* 辅助色 - 温暖橙 */
--secondary-50: #FFFBEB;
--secondary-100: #FEF3C7;
--secondary-200: #FDE68A;
--secondary-300: #FCD34D;
--secondary-400: #FBBF24;
--secondary-500: #F59E0B; /* 辅助色 */
--secondary-600: #D97706;
--secondary-700: #B45309;
--secondary-800: #92400E;
--secondary-900: #78350F;
```

### 2.2 功能色彩
```css
/* 成功色 */
--success-500: #10B981;
--success-100: #D1FAE5;

/* 警告色 */
--warning-500: #F59E0B;
--warning-100: #FEF3C7;

/* 错误色 */
--error-500: #EF4444;
--error-100: #FEE2E2;

/* 中性色 */
--gray-50: #F9FAFB;
--gray-100: #F3F4F6;
--gray-200: #E5E7EB;
--gray-300: #D1D5DB;
--gray-400: #9CA3AF;
--gray-500: #6B7280;
--gray-600: #4B5563;
--gray-700: #374151;
--gray-800: #1F2937;
--gray-900: #111827;
```

## 3. 字体系统

### 3.1 字体规范
```css
/* 标题字体 */
.text-h1 { font-size: 2.25rem; font-weight: 700; line-height: 1.2; }
.text-h2 { font-size: 1.875rem; font-weight: 600; line-height: 1.3; }
.text-h3 { font-size: 1.5rem; font-weight: 600; line-height: 1.4; }
.text-h4 { font-size: 1.25rem; font-weight: 500; line-height: 1.4; }

/* 正文字体 */
.text-body-lg { font-size: 1.125rem; font-weight: 400; line-height: 1.6; }
.text-body { font-size: 1rem; font-weight: 400; line-height: 1.6; }
.text-body-sm { font-size: 0.875rem; font-weight: 400; line-height: 1.5; }

/* 标签字体 */
.text-label { font-size: 0.875rem; font-weight: 500; line-height: 1.4; }
.text-caption { font-size: 0.75rem; font-weight: 400; line-height: 1.4; }
```

## 4. 组件设计规范

### 4.1 按钮组件
```css
/* 主要按钮 */
.btn-primary {
  background: var(--primary-600);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-primary:hover {
  background: var(--primary-700);
  transform: translateY(-1px);
}

/* 次要按钮 */
.btn-secondary {
  background: white;
  color: var(--primary-600);
  border: 1px solid var(--primary-600);
  padding: 12px 24px;
  border-radius: 8px;
}

/* 文本按钮 */
.btn-text {
  background: transparent;
  color: var(--primary-600);
  padding: 8px 16px;
  font-weight: 500;
}
```

### 4.2 输入框组件
```css
.input-field {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--gray-300);
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.input-field:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-field.error {
  border-color: var(--error-500);
}
```

### 4.3 卡片组件
```css
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 24px;
  transition: box-shadow 0.2s;
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-header {
  border-bottom: 1px solid var(--gray-200);
  padding-bottom: 16px;
  margin-bottom: 16px;
}
```

## 5. 关键页面设计

### 5.1 首页设计

#### 5.1.1 页面结构
```
┌─────────────────────────────────────┐
│ Header (导航栏)                      │
├─────────────────────────────────────┤
│ Hero Section (主横幅)                │
├─────────────────────────────────────┤
│ Quick Actions (快速操作)             │
├─────────────────────────────────────┤
│ Featured Services (精选服务)         │
├─────────────────────────────────────┤
│ Success Stories (成功案例)           │
├─────────────────────────────────────┤
│ How It Works (工作流程)              │
├─────────────────────────────────────┤
│ Footer (页脚)                       │
└─────────────────────────────────────┘
```

#### 5.1.2 Header 导航栏
- Logo (左侧)
- 主导航菜单 (中间): 找服务商 | 发布需求 | 灵感案例 | 帮助中心
- 用户操作 (右侧): 语言切换 | 登录 | 注册

#### 5.1.3 Hero Section 设计要点
- 主标题: "连接可靠的装修服务商，实现理想家居"
- 副标题: "透明报价 · 质量保障 · 全程管理"
- CTA按钮: "免费发布需求" (主要) + "浏览服务商" (次要)
- 背景: 高质量装修效果图轮播

### 5.2 需求发布页面

#### 5.2.1 多步骤表单设计
```
步骤1: 项目基本信息
├─ 项目类型选择 (卡片式选择)
├─ 项目位置 (地址输入 + 地图)
└─ 项目面积

步骤2: 预算与时间
├─ 预算范围 (滑块选择)
├─ 时间安排 (日期选择器)
└─ 紧急程度

步骤3: 详细需求
├─ 项目描述 (文本输入)
├─ 风格偏好 (图片选择)
├─ 特殊要求 (多选框)
└─ 参考图片上传

步骤4: 确认发布
├─ 信息预览
├─ 联系方式确认
└─ 发布按钮
```

#### 5.2.2 交互设计要点
- 进度指示器显示当前步骤
- 每步验证，错误提示清晰
- 支持保存草稿和返回修改
- 移动端优化，单列布局

### 5.3 服务商列表页面

#### 5.3.1 筛选器设计
```
┌─ 筛选器 (左侧边栏) ─┐  ┌─ 结果列表 (主区域) ─┐
│ 服务类型            │  │ 排序选项             │
│ 地理位置            │  │ ┌─ 服务商卡片 ─┐    │
│ 评分范围            │  │ │ 头像 + 基本信息 │    │
│ 价格区间            │  │ │ 评分 + 评价数   │    │
│ 认证状态            │  │ │ 服务类型标签    │    │
│ 可用时间            │  │ │ 作品集预览      │    │
└────────────────────┘  │ │ 联系按钮        │    │
                        │ └─────────────────┘    │
                        │ 分页控件               │
                        └─────────────────────────┘
```

#### 5.3.2 服务商卡片设计
- 头像 + 公司名称 + 认证标识
- 星级评分 + 评价数量
- 服务类型标签
- 价格区间指示
- 响应时间显示
- 作品集缩略图 (3-4张)
- "查看详情" 和 "立即咨询" 按钮

### 5.4 服务商详情页面

#### 5.4.1 页面布局
```
┌─────────────────────────────────────┐
│ 服务商头部信息                       │
├─────────────────────────────────────┤
│ Tab导航: 概览|作品集|评价|资质        │
├─────────────────────────────────────┤
│ Tab内容区域                         │
├─────────────────────────────────────┤
│ 固定底部操作栏                       │
└─────────────────────────────────────┘
```

#### 5.4.2 头部信息设计
- 公司Logo + 名称
- 评分 + 评价数 + 完成项目数
- 服务区域 + 成立时间
- 认证标识 + 保险信息
- 联系方式 (点击展开)

### 5.5 项目管理页面

#### 5.5.1 项目概览
```
┌─ 项目信息卡片 ─┐  ┌─ 进度时间线 ─┐
│ 项目名称        │  │ 里程碑1 ✓    │
│ 服务商信息      │  │ 里程碑2 🔄   │
│ 预算 | 工期     │  │ 里程碑3 ⏳   │
│ 状态标识        │  │ 里程碑4 ⏳   │
└─────────────────┘  └─────────────────┘

┌─ 最新动态 ─┐      ┌─ 快速操作 ─┐
│ 动态列表    │      │ 查看报价    │
│ 时间排序    │      │ 上传文件    │
│ 图片预览    │      │ 发送消息    │
└─────────────┘      │ 申请变更    │
                     └─────────────┘
```

#### 5.5.2 任务详情视图
- 甘特图显示任务时间线
- 任务状态和负责人
- 进度照片和更新记录
- 质量检查清单
- 问题标记和解决状态

## 6. 移动端适配

### 6.1 响应式断点
```css
/* 移动端 */
@media (max-width: 768px) {
  .container { padding: 16px; }
  .card { padding: 16px; }
  .btn { width: 100%; }
}

/* 平板端 */
@media (min-width: 769px) and (max-width: 1024px) {
  .grid { grid-template-columns: repeat(2, 1fr); }
}

/* 桌面端 */
@media (min-width: 1025px) {
  .grid { grid-template-columns: repeat(3, 1fr); }
}
```

### 6.2 移动端特殊考虑
- 底部导航栏 (Tab Bar)
- 手势操作支持
- 触摸友好的按钮尺寸 (最小44px)
- 简化的表单输入
- 优化的图片加载

## 7. 多语言界面适配

### 7.1 文本长度处理
- 英文文本通常比中文长20-30%
- 按钮和标签预留足够空间
- 使用弹性布局适应文本变化
- 长文本自动换行或截断

### 7.2 RTL语言支持预留
- 布局使用逻辑属性 (margin-inline-start)
- 图标和箭头方向适配
- 文本对齐方式调整

## 8. 可访问性设计

### 8.1 颜色对比度
- 文本与背景对比度 ≥ 4.5:1
- 大文本对比度 ≥ 3:1
- 非文本元素对比度 ≥ 3:1

### 8.2 键盘导航
- 所有交互元素支持Tab导航
- 焦点指示器清晰可见
- 跳过链接支持
- 快捷键支持

### 8.3 屏幕阅读器支持
- 语义化HTML标签
- ARIA标签和属性
- 图片alt文本
- 表单标签关联

## 9. 动效设计

### 9.1 过渡动画
```css
/* 基础过渡 */
.transition-base {
  transition: all 0.2s ease-in-out;
}

/* 悬停效果 */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 加载动画 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.loading {
  animation: pulse 2s infinite;
}
```

### 9.2 页面切换动画
- 淡入淡出 (Fade)
- 滑动切换 (Slide)
- 缩放效果 (Scale)
- 避免过度动画影响性能

## 10. 设计交付规范

### 10.1 设计文件组织
```
Design Files/
├── Design System/
│   ├── Colors.sketch
│   ├── Typography.sketch
│   └── Components.sketch
├── Mobile/
│   ├── iOS/
│   └── Android/
├── Web/
│   ├── Desktop/
│   └── Tablet/
└── Assets/
    ├── Icons/
    ├── Images/
    └── Illustrations/
```

### 10.2 标注规范
- 尺寸标注 (px)
- 间距标注 (margin/padding)
- 颜色值 (HEX/RGB)
- 字体规格 (family/size/weight)
- 交互状态说明

### 10.3 切图规范
- 图标: SVG格式，支持多尺寸
- 图片: WebP格式，提供2x/3x版本
- 背景图: 优化压缩，响应式尺寸
- 命名规范: 模块_功能_状态.格式
