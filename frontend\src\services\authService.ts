import { api } from './api';
import type { ApiResponse } from './api';
import type { User } from '../types';

// 认证相关的请求和响应类型
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  userType: 'homeowner' | 'contractor';
}

export interface AuthResponse {
  user: User;
  access_token: string;
  refresh_token: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  password: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

// 认证服务
export const authService = {
  // 用户登录
  login: async (credentials: LoginRequest): Promise<ApiResponse<AuthResponse>> => {
    const response = await api.post<AuthResponse>('/auth/login', credentials);
    
    // 登录成功后保存token
    if (response.success && response.data) {
      localStorage.setItem('access_token', response.data.access_token);
      localStorage.setItem('refresh_token', response.data.refresh_token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
    }
    
    return response;
  },

  // 用户注册
  register: async (userData: RegisterRequest): Promise<ApiResponse<AuthResponse>> => {
    const response = await api.post<AuthResponse>('/auth/register', userData);
    
    // 注册成功后保存token
    if (response.success && response.data) {
      localStorage.setItem('access_token', response.data.access_token);
      localStorage.setItem('refresh_token', response.data.refresh_token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
    }
    
    return response;
  },

  // 用户登出
  logout: async (): Promise<void> => {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // 清除本地存储
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user');
    }
  },

  // 刷新token
  refreshToken: async (): Promise<ApiResponse<{ access_token: string }>> => {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await api.post<{ access_token: string }>('/auth/refresh', {
      refresh_token: refreshToken,
    });

    if (response.success && response.data) {
      localStorage.setItem('access_token', response.data.access_token);
    }

    return response;
  },

  // 获取当前用户信息
  getCurrentUser: async (): Promise<ApiResponse<User>> => {
    return api.get<User>('/auth/me');
  },

  // 更新用户资料
  updateProfile: async (userData: Partial<User>): Promise<ApiResponse<User>> => {
    const response = await api.put<User>('/auth/profile', userData);
    
    // 更新本地存储的用户信息
    if (response.success && response.data) {
      localStorage.setItem('user', JSON.stringify(response.data));
    }
    
    return response;
  },

  // 忘记密码
  forgotPassword: async (data: ForgotPasswordRequest): Promise<ApiResponse<{ message: string }>> => {
    return api.post<{ message: string }>('/auth/forgot-password', data);
  },

  // 重置密码
  resetPassword: async (data: ResetPasswordRequest): Promise<ApiResponse<{ message: string }>> => {
    return api.post<{ message: string }>('/auth/reset-password', data);
  },

  // 修改密码
  changePassword: async (data: ChangePasswordRequest): Promise<ApiResponse<{ message: string }>> => {
    return api.put<{ message: string }>('/auth/change-password', data);
  },

  // 验证邮箱
  verifyEmail: async (token: string): Promise<ApiResponse<{ message: string }>> => {
    return api.post<{ message: string }>('/auth/verify-email', { token });
  },

  // 重新发送验证邮件
  resendVerificationEmail: async (): Promise<ApiResponse<{ message: string }>> => {
    return api.post<{ message: string }>('/auth/resend-verification');
  },

  // 检查token是否有效
  isAuthenticated: (): boolean => {
    const token = localStorage.getItem('access_token');
    return !!token;
  },

  // 获取本地存储的用户信息
  getStoredUser: (): User | null => {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        return JSON.parse(userStr);
      } catch (error) {
        console.error('Error parsing stored user:', error);
        return null;
      }
    }
    return null;
  },

  // 获取访问token
  getAccessToken: (): string | null => {
    return localStorage.getItem('access_token');
  },

  // 获取刷新token
  getRefreshToken: (): string | null => {
    return localStorage.getItem('refresh_token');
  },
};
