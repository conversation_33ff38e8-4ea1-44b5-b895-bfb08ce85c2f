{"name": "three-stdlib", "version": "2.36.0", "description": "stand-alone library of threejs examples", "keywords": ["three", "typescript", "examples", "helpers", "abstractions", "3d"], "author": "<PERSON> (https://github.com/drcmda)", "maintainers": ["<PERSON> (https://github.com/cody<PERSON><PERSON><PERSON>)", "<PERSON> (https://github.com/joshua<PERSON>s)"], "homepage": "https://github.com/pmndrs/three-stdlib", "repository": "https://github.com/pmndrs/three-stdlib", "license": "MIT", "types": "./index.d.ts", "main": "./index.cjs", "module": "./index.js", "exports": {"types": "./index.d.ts", "require": "./index.cjs", "import": "./index.js"}, "sideEffects": false, "devDependencies": {"@types/node": "^20.6.3", "@types/three": "^0.128.0", "copyfiles": "^2.4.1", "json": "^11.0.0", "prettier": "^2.2.1", "rimraf": "^3.0.2", "three": "^0.128.0", "typescript": "^4.7.4", "vite": "^4.3.8"}, "dependencies": {"@types/draco3d": "^1.4.0", "@types/offscreencanvas": "^2019.6.4", "@types/webxr": "^0.5.2", "draco3d": "^1.4.1", "fflate": "^0.6.9", "potpack": "^1.0.1"}, "peerDependencies": {"three": ">=0.128.0"}, "scripts": {"build": "rimraf dist && vite build && tsc --emitDeclarationOnly && copyfiles -u 1 \"src/**/*.d.ts\" dist && copyfiles package.json README.md LICENSE dist && json -I -f dist/package.json -e \"this.private=undefined;this.type=\\\"module\\\";\"", "lint": "tsc --noEmit"}, "type": "module"}