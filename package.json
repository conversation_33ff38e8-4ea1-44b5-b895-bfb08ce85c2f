{"name": "homereno-backend", "version": "1.0.0", "description": "HomeReno - Canadian Home Renovation Platform Backend", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "npm run typeorm -- migration:generate", "migration:run": "npm run typeorm -- migration:run", "migration:revert": "npm run typeorm -- migration:revert"}, "dependencies": {"@nestjs/bull": "^10.0.1", "@nestjs/cache-manager": "^2.1.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.1.0", "@nestjs/mongoose": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.1.0", "@nestjs/throttler": "^4.2.1", "@react-three/cannon": "^6.6.0", "@react-three/drei": "^10.4.2", "@types/three": "^0.178.0", "aws-sdk": "^2.1419.0", "bcryptjs": "^2.4.3", "bull": "^4.11.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "express-rate-limit": "^6.8.1", "helmet": "^7.0.0", "i18next": "^23.2.11", "i18next-fs-backend": "^2.1.5", "mongoose": "^7.4.0", "multer": "^1.4.5-lts.1", "nest-winston": "^1.9.3", "nestjs-i18n": "^10.5.1", "nodemailer": "^6.9.4", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "react-three-fiber": "^6.0.13", "redis": "^4.6.7", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "stripe": "^12.18.0", "three": "^0.178.0", "twilio": "^4.14.0", "winston": "^3.10.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcryptjs": "^2.4.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.7", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.9", "@types/passport-jwt": "^3.0.9", "@types/passport-local": "^1.0.35", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}