import { api } from './api';
import type { ApiResponse, PaginationParams } from './api';
import type { Project, ProjectCategory, ProjectStatus } from '../types';

// 项目相关的请求类型
export interface CreateProjectRequest {
  title: string;
  description: string;
  category: ProjectCategory;
  budget: {
    min: number;
    max: number;
  };
  timeline: {
    startDate: Date;
    endDate: Date;
  };
  location: {
    address: string;
    city: string;
    province: string;
    postalCode: string;
  };
  requirements?: string[];
  attachments?: string[];
}

export interface UpdateProjectRequest extends Partial<CreateProjectRequest> {
  status?: ProjectStatus;
}

export interface ProjectSearchParams extends PaginationParams {
  category?: ProjectCategory;
  status?: ProjectStatus;
  minBudget?: number;
  maxBudget?: number;
  city?: string;
  province?: string;
  keyword?: string;
}

// 项目服务
export const projectService = {
  // 创建项目
  createProject: async (projectData: CreateProjectRequest): Promise<ApiResponse<Project>> => {
    return api.post<Project>('/projects', projectData);
  },

  // 获取项目列表
  getProjects: async (params?: ProjectSearchParams): Promise<ApiResponse<Project[]>> => {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const url = `/projects${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return api.get<Project[]>(url);
  },

  // 获取我的项目
  getMyProjects: async (params?: PaginationParams): Promise<ApiResponse<Project[]>> => {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const url = `/projects/my${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return api.get<Project[]>(url);
  },

  // 获取单个项目详情
  getProject: async (projectId: string): Promise<ApiResponse<Project>> => {
    return api.get<Project>(`/projects/${projectId}`);
  },

  // 更新项目
  updateProject: async (projectId: string, projectData: UpdateProjectRequest): Promise<ApiResponse<Project>> => {
    return api.put<Project>(`/projects/${projectId}`, projectData);
  },

  // 删除项目
  deleteProject: async (projectId: string): Promise<ApiResponse<{ message: string }>> => {
    return api.delete<{ message: string }>(`/projects/${projectId}`);
  },

  // 上传项目附件
  uploadProjectAttachment: async (
    projectId: string, 
    file: File, 
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<{ fileUrl: string; fileName: string }>> => {
    return api.upload<{ fileUrl: string; fileName: string }>(
      `/projects/${projectId}/attachments`, 
      file, 
      onProgress
    );
  },

  // 删除项目附件
  deleteProjectAttachment: async (
    projectId: string, 
    attachmentId: string
  ): Promise<ApiResponse<{ message: string }>> => {
    return api.delete<{ message: string }>(`/projects/${projectId}/attachments/${attachmentId}`);
  },

  // 获取项目统计信息
  getProjectStats: async (): Promise<ApiResponse<{
    total: number;
    active: number;
    completed: number;
    cancelled: number;
    byCategory: Record<ProjectCategory, number>;
  }>> => {
    return api.get('/projects/stats');
  },

  // 搜索项目
  searchProjects: async (
    keyword: string, 
    filters?: Omit<ProjectSearchParams, 'keyword'>
  ): Promise<ApiResponse<Project[]>> => {
    const params = { keyword, ...filters };
    return projectService.getProjects(params);
  },

  // 获取项目类别列表
  getProjectCategories: async (): Promise<ApiResponse<{ 
    category: ProjectCategory; 
    name: string; 
    description: string; 
    count: number 
  }[]>> => {
    return api.get('/projects/categories');
  },

  // 获取热门项目
  getPopularProjects: async (limit: number = 10): Promise<ApiResponse<Project[]>> => {
    return api.get<Project[]>(`/projects/popular?limit=${limit}`);
  },

  // 获取最新项目
  getLatestProjects: async (limit: number = 10): Promise<ApiResponse<Project[]>> => {
    return api.get<Project[]>(`/projects/latest?limit=${limit}`);
  },

  // 收藏项目
  favoriteProject: async (projectId: string): Promise<ApiResponse<{ message: string }>> => {
    return api.post<{ message: string }>(`/projects/${projectId}/favorite`);
  },

  // 取消收藏项目
  unfavoriteProject: async (projectId: string): Promise<ApiResponse<{ message: string }>> => {
    return api.delete<{ message: string }>(`/projects/${projectId}/favorite`);
  },

  // 获取收藏的项目
  getFavoriteProjects: async (params?: PaginationParams): Promise<ApiResponse<Project[]>> => {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const url = `/projects/favorites${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return api.get<Project[]>(url);
  },

  // 复制项目
  duplicateProject: async (projectId: string): Promise<ApiResponse<Project>> => {
    return api.post<Project>(`/projects/${projectId}/duplicate`);
  },

  // 归档项目
  archiveProject: async (projectId: string): Promise<ApiResponse<{ message: string }>> => {
    return api.patch<{ message: string }>(`/projects/${projectId}/archive`);
  },

  // 恢复项目
  restoreProject: async (projectId: string): Promise<ApiResponse<{ message: string }>> => {
    return api.patch<{ message: string }>(`/projects/${projectId}/restore`);
  },

  // 获取项目活动日志
  getProjectActivity: async (projectId: string): Promise<ApiResponse<{
    id: string;
    action: string;
    description: string;
    userId: string;
    userName: string;
    createdAt: Date;
  }[]>> => {
    return api.get(`/projects/${projectId}/activity`);
  },
};
