{"version": 3, "file": "accept-language.resolver.js", "sourceRoot": "", "sources": ["../../src/resolvers/accept-language.resolver.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,oCAA6D;AAC7D,2CAA8D;AAC9D,mEAA8C;AAQvC,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IACjC,YAEU,UAAyC;QAC/C,SAAS,EAAE,cAAc;KAC1B;QAFO,YAAO,GAAP,OAAO,CAEd;IACA,CAAC;IAEJ,KAAK,CAAC,OAAO,CACX,OAAyB;QAEzB,IAAI,GAAQ,CAAC;QACb,IAAI,OAAoB,CAAC;QAEzB,QAAQ,OAAO,CAAC,OAAO,EAAY,EAAE;YACnC,KAAK,MAAM;gBACT,GAAG,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;gBAC1C,OAAO,GAAG,GAAG,CAAC,WAAW,CAAC;gBAC1B,MAAM;YACR,KAAK,SAAS;gBACZ,CAAC,EAAE,AAAD,EAAG,EAAE,GAAG,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;gBACxD,IAAI,CAAC,GAAG;oBAAE,OAAO,SAAS,CAAC;gBAC3B,MAAM;YACR;gBACE,OAAO,SAAS,CAAC;SACpB;QAED,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG;YAClB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,iBAAiB,CAAC;YACtC,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,CAAC,iBAAiB,CAAC,CAAC;QAEtC,IAAI,IAAI,EAAE;YACR,MAAM,cAAc,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;YACvD,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,QAAQ,EAAE;gBACvC,OAAO,IAAA,6BAAI,EAAC,cAAc,EAAE,IAAI,CAAC,CAAC;aACnC;iBAAM,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,OAAO,EAAE;gBAC7C,OAAO,IAAA,6BAAI,EAAC,cAAc,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;aACpD;YACD,OAAO,CACL,IAAA,6BAAI,EAAC,cAAc,EAAE,IAAI,CAAC;gBAC1B,IAAA,6BAAI,EAAC,cAAc,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAC5C,CAAC;SACH;QACD,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAA;AA7CY,sBAAsB;IADlC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,2BAAmB,GAAE,CAAA;;GAFb,sBAAsB,CA6ClC;AA7CY,wDAAsB"}