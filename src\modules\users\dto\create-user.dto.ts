import {
  IsEmail,
  IsString,
  IsEnum,
  IsOptional,
  MinLength,
  MaxLength,
  IsPhoneNumber,
  ValidateNested,
  IsObject,
  IsNumber,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { UserType } from '../schemas/user.schema';

export class CreateAddressDto {
  @ApiProperty({ description: '街道地址' })
  @IsString()
  @MaxLength(200)
  street: string;

  @ApiProperty({ description: '城市' })
  @IsString()
  @MaxLength(100)
  city: string;

  @ApiProperty({ description: '省份' })
  @IsString()
  @MaxLength(100)
  province: string;

  @ApiProperty({ description: '邮政编码' })
  @IsString()
  @MaxLength(10)
  postalCode: string;

  @ApiPropertyOptional({ description: '国家', default: 'CA' })
  @IsOptional()
  @IsString()
  @MaxLength(2)
  country?: string = 'CA';

  @ApiPropertyOptional({ description: '地理坐标' })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => CoordinatesDto)
  coordinates?: CoordinatesDto;
}

export class CoordinatesDto {
  @ApiProperty({ description: '纬度' })
  @IsNumber()
  lat: number;

  @ApiProperty({ description: '经度' })
  @IsNumber()
  lng: number;
}

export class CreateUserProfileDto {
  @ApiProperty({ description: '名字' })
  @IsString()
  @MinLength(1)
  @MaxLength(50)
  firstName: string;

  @ApiProperty({ description: '姓氏' })
  @IsString()
  @MinLength(1)
  @MaxLength(50)
  lastName: string;

  @ApiPropertyOptional({ description: '电话号码' })
  @IsOptional()
  @IsPhoneNumber('CA')
  phone?: string;

  @ApiPropertyOptional({ description: '头像URL' })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiPropertyOptional({ description: '首选语言', enum: ['en', 'zh', 'fr'] })
  @IsOptional()
  @IsEnum(['en', 'zh', 'fr'])
  language?: string = 'en';

  @ApiPropertyOptional({ description: '时区' })
  @IsOptional()
  @IsString()
  timezone?: string = 'America/Toronto';

  @ApiPropertyOptional({ description: '地址信息', type: CreateAddressDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateAddressDto)
  address?: CreateAddressDto;
}

export class NotificationPreferencesDto {
  @ApiPropertyOptional({ description: '邮件通知', default: true })
  @IsOptional()
  @IsBoolean()
  email?: boolean = true;

  @ApiPropertyOptional({ description: '短信通知', default: false })
  @IsOptional()
  @IsBoolean()
  sms?: boolean = false;

  @ApiPropertyOptional({ description: '推送通知', default: true })
  @IsOptional()
  @IsBoolean()
  push?: boolean = true;
}

export class PrivacySettingsDto {
  @ApiPropertyOptional({ description: '显示个人资料', default: true })
  @IsOptional()
  @IsBoolean()
  showProfile?: boolean = true;

  @ApiPropertyOptional({ description: '显示位置信息', default: true })
  @IsOptional()
  @IsBoolean()
  showLocation?: boolean = true;
}

export class UserPreferencesDto {
  @ApiPropertyOptional({ description: '通知偏好', type: NotificationPreferencesDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => NotificationPreferencesDto)
  notifications?: NotificationPreferencesDto;

  @ApiPropertyOptional({ description: '隐私设置', type: PrivacySettingsDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => PrivacySettingsDto)
  privacy?: PrivacySettingsDto;
}

export class CreateUserDto {
  @ApiProperty({ description: '邮箱地址' })
  @IsEmail()
  @MaxLength(255)
  email: string;

  @ApiProperty({ description: '密码', minLength: 8 })
  @IsString()
  @MinLength(8)
  @MaxLength(128)
  password: string;

  @ApiProperty({ description: '用户类型', enum: UserType })
  @IsEnum(UserType)
  userType: UserType;

  @ApiProperty({ description: '用户资料', type: CreateUserProfileDto })
  @ValidateNested()
  @Type(() => CreateUserProfileDto)
  profile: CreateUserProfileDto;

  @ApiPropertyOptional({ description: '用户偏好', type: UserPreferencesDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => UserPreferencesDto)
  preferences?: UserPreferencesDto;
}
