import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { File, FileDocument, FileType, FileStatus } from './schemas/file.schema';
import { UploadFileDto, FileQueryDto } from './dto/upload-file.dto';
import { PaginatedResult } from '../../common/interfaces/paginated-result.interface';
import { User } from '../users/schemas/user.schema';
import * as path from 'path';
import * as fs from 'fs';

@Injectable()
export class FilesService {
  constructor(
    @InjectModel(File.name) private fileModel: Model<FileDocument>,
  ) {}

  /**
   * 上传文件
   */
  async uploadFile(
    file: Express.Multer.File,
    uploadFileDto: UploadFileDto,
    user: User,
  ): Promise<File> {
    // 确定文件类型
    const fileType = this.getFileType(file.mimetype);
    
    // 生成文件URL
    const fileUrl = `/uploads/${file.filename}`;
    
    const fileData = {
      originalName: file.originalname,
      filename: file.filename,
      path: file.path,
      url: fileUrl,
      mimeType: file.mimetype,
      size: file.size,
      type: fileType,
      category: uploadFileDto.category,
      status: FileStatus.UPLOADED,
      uploadedBy: user._id,
      projectId: uploadFileDto.projectId ? new Types.ObjectId(uploadFileDto.projectId) : undefined,
      quoteId: uploadFileDto.quoteId ? new Types.ObjectId(uploadFileDto.quoteId) : undefined,
      serviceProviderId: uploadFileDto.serviceProviderId ? new Types.ObjectId(uploadFileDto.serviceProviderId) : undefined,
      description: uploadFileDto.description,
      tags: uploadFileDto.tags,
      isPublic: uploadFileDto.isPublic || false,
      expiresAt: uploadFileDto.expiresAt ? new Date(uploadFileDto.expiresAt) : undefined,
    };

    const createdFile = new this.fileModel(fileData);
    return createdFile.save();
  }

  /**
   * 获取文件列表
   */
  async findAll(query: FileQueryDto, user: User): Promise<PaginatedResult<File>> {
    const {
      page = 1,
      limit = 20,
      category,
      projectId,
      quoteId,
      serviceProviderId,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = query;

    const filter: any = {
      deletedAt: { $exists: false },
    };

    // 权限过滤：只能看到自己上传的文件或公开的文件
    filter.$or = [
      { uploadedBy: user._id },
      { isPublic: true },
    ];

    // 分类过滤
    if (category) {
      filter.category = category;
    }

    // 关联过滤
    if (projectId) {
      filter.projectId = projectId;
    }
    if (quoteId) {
      filter.quoteId = quoteId;
    }
    if (serviceProviderId) {
      filter.serviceProviderId = serviceProviderId;
    }

    // 搜索
    if (search) {
      filter.$and = filter.$and || [];
      filter.$and.push({
        $or: [
          { originalName: new RegExp(search, 'i') },
          { description: new RegExp(search, 'i') },
          { tags: new RegExp(search, 'i') },
        ],
      });
    }

    // 排序
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [items, total] = await Promise.all([
      this.fileModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('uploadedBy', 'profile.firstName profile.lastName')
        .exec(),
      this.fileModel.countDocuments(filter),
    ]);

    return {
      items,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * 根据ID获取文件详情
   */
  async findOne(id: string, user: User): Promise<File> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid file ID');
    }

    const file = await this.fileModel
      .findOne({
        _id: id,
        deletedAt: { $exists: false },
      })
      .populate('uploadedBy', 'profile.firstName profile.lastName')
      .exec();

    if (!file) {
      throw new NotFoundException('File not found');
    }

    // 权限检查
    if (!file.isPublic && file.uploadedBy.toString() !== user._id.toString()) {
      throw new ForbiddenException('You do not have permission to access this file');
    }

    // 更新访问记录
    await this.fileModel.findByIdAndUpdate(id, {
      $inc: { downloadCount: 1 },
      lastAccessedAt: new Date(),
    });

    return file;
  }

  /**
   * 删除文件
   */
  async remove(id: string, user: User): Promise<void> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid file ID');
    }

    const file = await this.fileModel.findOne({
      _id: id,
      deletedAt: { $exists: false },
    });

    if (!file) {
      throw new NotFoundException('File not found');
    }

    // 权限检查
    if (file.uploadedBy.toString() !== user._id.toString()) {
      throw new ForbiddenException('You can only delete your own files');
    }

    // 软删除
    await this.fileModel.findByIdAndUpdate(id, {
      deletedAt: new Date(),
      deletedBy: user._id,
    });
  }

  /**
   * 根据文件MIME类型确定文件类型
   */
  private getFileType(mimeType: string): FileType {
    if (mimeType.startsWith('image/')) {
      return FileType.IMAGE;
    } else if (mimeType.startsWith('video/')) {
      return FileType.VIDEO;
    } else if (mimeType.startsWith('audio/')) {
      return FileType.AUDIO;
    } else if (
      mimeType.includes('pdf') ||
      mimeType.includes('document') ||
      mimeType.includes('text') ||
      mimeType.includes('spreadsheet') ||
      mimeType.includes('presentation')
    ) {
      return FileType.DOCUMENT;
    } else {
      return FileType.OTHER;
    }
  }

  /**
   * 获取用户的文件统计
   */
  async getUserStats(userId: string): Promise<any> {
    const stats = await this.fileModel.aggregate([
      {
        $match: {
          uploadedBy: new Types.ObjectId(userId),
          deletedAt: { $exists: false },
        },
      },
      {
        $group: {
          _id: null,
          totalFiles: { $sum: 1 },
          totalSize: { $sum: '$size' },
          imageCount: { $sum: { $cond: [{ $eq: ['$type', FileType.IMAGE] }, 1, 0] } },
          documentCount: { $sum: { $cond: [{ $eq: ['$type', FileType.DOCUMENT] }, 1, 0] } },
          videoCount: { $sum: { $cond: [{ $eq: ['$type', FileType.VIDEO] }, 1, 0] } },
          audioCount: { $sum: { $cond: [{ $eq: ['$type', FileType.AUDIO] }, 1, 0] } },
          otherCount: { $sum: { $cond: [{ $eq: ['$type', FileType.OTHER] }, 1, 0] } },
          totalDownloads: { $sum: '$downloadCount' },
        },
      },
    ]);

    return stats[0] || {
      totalFiles: 0,
      totalSize: 0,
      imageCount: 0,
      documentCount: 0,
      videoCount: 0,
      audioCount: 0,
      otherCount: 0,
      totalDownloads: 0,
    };
  }
}
