{"version": 3, "file": "FresnelShader.cjs", "sources": ["../../src/shaders/FresnelShader.ts"], "sourcesContent": ["/**\n * Based on Nvidia Cg tutorial\n */\n\nexport const FresnelShader = {\n  uniforms: {\n    mRefractionRatio: { value: 1.02 },\n    mFresnelBias: { value: 0.1 },\n    mFresnelPower: { value: 2.0 },\n    mFresnelScale: { value: 1.0 },\n    tCube: { value: null },\n  },\n\n  vertexShader: /* glsl */ `\n    uniform float mRefractionRatio;\n    uniform float mFresnelBias;\n    uniform float mFresnelScale;\n    uniform float mFresnelPower;\n\n    varying vec3 vReflect;\n    varying vec3 vRefract[3];\n    varying float vReflectionFactor;\n\n    void main() {\n\n    \tvec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );\n    \tvec4 worldPosition = modelMatrix * vec4( position, 1.0 );\n\n    \tvec3 worldNormal = normalize( mat3( modelMatrix[0].xyz, modelMatrix[1].xyz, modelMatrix[2].xyz ) * normal );\n\n    \tvec3 I = worldPosition.xyz - cameraPosition;\n\n    \tvReflect = reflect( I, worldNormal );\n    \tvRefract[0] = refract( normalize( I ), worldNormal, mRefractionRatio );\n    \tvRefract[1] = refract( normalize( I ), worldNormal, mRefractionRatio * 0.99 );\n    \tvRefract[2] = refract( normalize( I ), worldNormal, mRefractionRatio * 0.98 );\n    \tvReflectionFactor = mFresnelBias + mFresnelScale * pow( 1.0 + dot( normalize( I ), worldNormal ), mFresnelPower );\n\n    \tgl_Position = projectionMatrix * mvPosition;\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform samplerCube tCube;\n\n    varying vec3 vReflect;\n    varying vec3 vRefract[3];\n    varying float vReflectionFactor;\n\n    void main() {\n\n    \tvec4 reflectedColor = textureCube( tCube, vec3( -vReflect.x, vReflect.yz ) );\n    \tvec4 refractedColor = vec4( 1.0 );\n\n    \trefractedColor.r = textureCube( tCube, vec3( -vRefract[0].x, vRefract[0].yz ) ).r;\n    \trefractedColor.g = textureCube( tCube, vec3( -vRefract[1].x, vRefract[1].yz ) ).g;\n    \trefractedColor.b = textureCube( tCube, vec3( -vRefract[2].x, vRefract[2].yz ) ).b;\n\n    \tgl_FragColor = mix( refractedColor, reflectedColor, clamp( vReflectionFactor, 0.0, 1.0 ) );\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";;AAIO,MAAM,gBAAgB;AAAA,EAC3B,UAAU;AAAA,IACR,kBAAkB,EAAE,OAAO,KAAK;AAAA,IAChC,cAAc,EAAE,OAAO,IAAI;AAAA,IAC3B,eAAe,EAAE,OAAO,EAAI;AAAA,IAC5B,eAAe,EAAE,OAAO,EAAI;AAAA,IAC5B,OAAO,EAAE,OAAO,KAAK;AAAA,EACvB;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA8BzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoB7B;;"}