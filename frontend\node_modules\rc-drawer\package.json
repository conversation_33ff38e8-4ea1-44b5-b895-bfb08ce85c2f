{"name": "rc-drawer", "version": "7.3.0", "description": "drawer component for react", "keywords": ["react", "react-component", "react-drawer", "drawer", "drawer-menu", "rc-drawer-menu", "react-drawer-menu", "animation", "drawer-motion", "drawer-animation"], "homepage": "https://github.com/react-component/drawer", "bugs": {"url": "https://github.com/react-component/drawer/issues"}, "repository": {"type": "git", "url": "https://github.com/react-component/drawer.git"}, "license": "MIT", "author": "<EMAIL>", "main": "./lib/index", "module": "./es/index", "files": ["lib", "assets/*.css", "es"], "scripts": {"build": "dumi build", "compile": "father build && lessc assets/index.less assets/index.css", "lint": "eslint src/ --ext .tsx,.ts", "now-build": "npm run build", "prepublishOnly": "npm run compile && np --no-cleanup --yolo --no-publish --branch=antd-5.x", "start": "dumi dev", "test": "rc-test", "prettier": "prettier --write .", "prepare": "husky"}, "dependencies": {"@babel/runtime": "^7.23.9", "@rc-component/portal": "^1.1.1", "classnames": "^2.2.6", "rc-motion": "^2.6.1", "rc-util": "^5.38.1"}, "devDependencies": {"@ant-design/icons": "^5.3.0", "@rc-component/father-plugin": "^1.0.0", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.0.0", "@types/classnames": "^2.2.9", "@types/jest": "^29.5.11", "@types/raf": "^3.4.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/warning": "^3.0.0", "antd": "^5.12.7", "dumi": "^2.2.0", "eslint": "^8.56.0", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-unicorn": "^51.0.1", "father": "^4.0.0", "glob": "^10.3.10", "husky": "^9.0.10", "less": "^4.2.0", "lint-staged": "^15.2.2", "np": "^9.2.0", "prettier": "^3.0.0", "rc-test": "^7.0.9", "react": "^18.0.0", "react-dom": "^18.0.0", "stylelint": "^16.2.1", "stylelint-config-standard-less": "^3.0.1", "typescript": "^5.3.3"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": "eslint --fix", "*.{css,less}": "stylelint --fix", "*.{js,jsx,ts,tsx,json,less,css,md}": "prettier --write"}}