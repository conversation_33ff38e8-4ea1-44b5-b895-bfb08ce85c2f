import {
  IsString,
  IsEnum,
  IsOptional,
  IsBoolean,
  IsDateString,
  IsArray,
  IsMongoId,
  IsNumber,
  IsObject,
  ValidateNested,
  <PERSON><PERSON>ength,
  <PERSON><PERSON>ength,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  NotificationType,
  NotificationPriority,
  NotificationChannel,
} from '../schemas/notification.schema';

export class CreateNotificationDataDto {
  @ApiPropertyOptional({ description: '项目ID' })
  @IsOptional()
  @IsString()
  projectId?: string;

  @ApiPropertyOptional({ description: '报价ID' })
  @IsOptional()
  @IsString()
  quoteId?: string;

  @ApiPropertyOptional({ description: '服务商ID' })
  @IsOptional()
  @IsString()
  serviceProviderId?: string;

  @ApiPropertyOptional({ description: '支付ID' })
  @IsOptional()
  @IsString()
  paymentId?: string;

  @ApiPropertyOptional({ description: '评价ID' })
  @IsOptional()
  @IsString()
  reviewId?: string;

  @ApiPropertyOptional({ description: '金额' })
  @IsOptional()
  @IsNumber()
  amount?: number;

  @ApiPropertyOptional({ description: '货币' })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiPropertyOptional({ description: '截止日期' })
  @IsOptional()
  @IsDateString()
  dueDate?: string;

  @ApiPropertyOptional({ description: '其他数据' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class CreateNotificationDto {
  @ApiProperty({ description: '接收用户ID' })
  @IsMongoId()
  userId: string;

  @ApiProperty({ description: '通知类型', enum: NotificationType })
  @IsEnum(NotificationType)
  type: NotificationType;

  @ApiProperty({ description: '通知标题' })
  @IsString()
  @MinLength(1)
  @MaxLength(200)
  title: string;

  @ApiProperty({ description: '通知内容' })
  @IsString()
  @MinLength(1)
  @MaxLength(1000)
  message: string;

  @ApiPropertyOptional({ description: '优先级', enum: NotificationPriority, default: NotificationPriority.NORMAL })
  @IsOptional()
  @IsEnum(NotificationPriority)
  priority?: NotificationPriority = NotificationPriority.NORMAL;

  @ApiPropertyOptional({ description: '发送渠道', enum: NotificationChannel, isArray: true })
  @IsOptional()
  @IsArray()
  @IsEnum(NotificationChannel, { each: true })
  channels?: NotificationChannel[] = [NotificationChannel.IN_APP];

  @ApiPropertyOptional({ description: '通知数据', type: CreateNotificationDataDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateNotificationDataDto)
  data?: CreateNotificationDataDto;

  @ApiPropertyOptional({ description: '计划发送时间' })
  @IsOptional()
  @IsDateString()
  scheduledAt?: string;

  @ApiPropertyOptional({ description: '过期时间' })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;

  @ApiPropertyOptional({ description: '操作URL' })
  @IsOptional()
  @IsString()
  actionUrl?: string;

  @ApiPropertyOptional({ description: '操作按钮文本' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  actionText?: string;

  @ApiPropertyOptional({ description: '图标' })
  @IsOptional()
  @IsString()
  icon?: string;

  @ApiPropertyOptional({ description: '图片URL' })
  @IsOptional()
  @IsString()
  imageUrl?: string;

  @ApiPropertyOptional({ description: '发送者ID' })
  @IsOptional()
  @IsMongoId()
  senderId?: string;
}

export class NotificationQueryDto {
  @ApiPropertyOptional({ description: '页码', default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', default: 20 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number = 20;

  @ApiPropertyOptional({ description: '通知类型', enum: NotificationType })
  @IsOptional()
  @IsEnum(NotificationType)
  type?: NotificationType;

  @ApiPropertyOptional({ description: '优先级', enum: NotificationPriority })
  @IsOptional()
  @IsEnum(NotificationPriority)
  priority?: NotificationPriority;

  @ApiPropertyOptional({ description: '是否已读' })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  isRead?: boolean;

  @ApiPropertyOptional({ description: '是否已发送' })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  isSent?: boolean;

  @ApiPropertyOptional({ description: '搜索关键词' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: '排序字段', enum: ['createdAt', 'priority', 'readAt'] })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ description: '排序方向', enum: ['asc', 'desc'] })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class BulkNotificationDto {
  @ApiProperty({ description: '接收用户ID列表', type: [String] })
  @IsArray()
  @IsMongoId({ each: true })
  userIds: string[];

  @ApiProperty({ description: '通知类型', enum: NotificationType })
  @IsEnum(NotificationType)
  type: NotificationType;

  @ApiProperty({ description: '通知标题' })
  @IsString()
  @MinLength(1)
  @MaxLength(200)
  title: string;

  @ApiProperty({ description: '通知内容' })
  @IsString()
  @MinLength(1)
  @MaxLength(1000)
  message: string;

  @ApiPropertyOptional({ description: '优先级', enum: NotificationPriority, default: NotificationPriority.NORMAL })
  @IsOptional()
  @IsEnum(NotificationPriority)
  priority?: NotificationPriority = NotificationPriority.NORMAL;

  @ApiPropertyOptional({ description: '发送渠道', enum: NotificationChannel, isArray: true })
  @IsOptional()
  @IsArray()
  @IsEnum(NotificationChannel, { each: true })
  channels?: NotificationChannel[] = [NotificationChannel.IN_APP];

  @ApiPropertyOptional({ description: '通知数据', type: CreateNotificationDataDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateNotificationDataDto)
  data?: CreateNotificationDataDto;

  @ApiPropertyOptional({ description: '计划发送时间' })
  @IsOptional()
  @IsDateString()
  scheduledAt?: string;

  @ApiPropertyOptional({ description: '过期时间' })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;

  @ApiPropertyOptional({ description: '操作URL' })
  @IsOptional()
  @IsString()
  actionUrl?: string;

  @ApiPropertyOptional({ description: '操作按钮文本' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  actionText?: string;

  @ApiPropertyOptional({ description: '图标' })
  @IsOptional()
  @IsString()
  icon?: string;

  @ApiPropertyOptional({ description: '图片URL' })
  @IsOptional()
  @IsString()
  imageUrl?: string;

  @ApiPropertyOptional({ description: '发送者ID' })
  @IsOptional()
  @IsMongoId()
  senderId?: string;
}
