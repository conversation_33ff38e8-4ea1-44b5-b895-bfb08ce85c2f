/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ([
/* 0 */,
/* 1 */
/***/ ((module) => {

module.exports = require("@nestjs/core");

/***/ }),
/* 2 */
/***/ ((module) => {

module.exports = require("@nestjs/common");

/***/ }),
/* 3 */
/***/ ((module) => {

module.exports = require("@nestjs/swagger");

/***/ }),
/* 4 */
/***/ ((module) => {

module.exports = require("@nestjs/config");

/***/ }),
/* 5 */
/***/ ((module) => {

module.exports = require("helmet");

/***/ }),
/* 6 */
/***/ ((module) => {

module.exports = require("compression");

/***/ }),
/* 7 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AppModule = void 0;
const common_1 = __webpack_require__(2);
const config_1 = __webpack_require__(4);
const mongoose_1 = __webpack_require__(8);
const cache_manager_1 = __webpack_require__(9);
const throttler_1 = __webpack_require__(10);
const bull_1 = __webpack_require__(11);
const nest_winston_1 = __webpack_require__(12);
const winston = __webpack_require__(13);
const redisStore = __webpack_require__(Object(function webpackMissingModule() { var e = new Error("Cannot find module 'cache-manager-redis-store'"); e.code = 'MODULE_NOT_FOUND'; throw e; }()));
const auth_module_1 = __webpack_require__(14);
const users_module_1 = __webpack_require__(29);
const projects_module_1 = __webpack_require__(39);
const service_providers_module_1 = __webpack_require__(46);
const quotes_module_1 = __webpack_require__(53);
const payments_module_1 = __webpack_require__(54);
const files_module_1 = __webpack_require__(55);
const notifications_module_1 = __webpack_require__(56);
const reviews_module_1 = __webpack_require__(57);
const tasks_module_1 = __webpack_require__(58);
const database_module_1 = __webpack_require__(59);
const redis_module_1 = __webpack_require__(60);
const email_module_1 = __webpack_require__(61);
const sms_module_1 = __webpack_require__(62);
const translation_module_1 = __webpack_require__(63);
const database_config_1 = __webpack_require__(64);
const redis_config_1 = __webpack_require__(65);
const auth_config_1 = __webpack_require__(66);
const email_config_1 = __webpack_require__(67);
const sms_config_1 = __webpack_require__(68);
const file_config_1 = __webpack_require__(69);
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                load: [
                    database_config_1.databaseConfig,
                    redis_config_1.redisConfig,
                    auth_config_1.authConfig,
                    email_config_1.emailConfig,
                    sms_config_1.smsConfig,
                    file_config_1.fileConfig,
                ],
                envFilePath: ['.env.local', '.env'],
            }),
            nest_winston_1.WinstonModule.forRootAsync({
                useFactory: (configService) => ({
                    transports: [
                        new winston.transports.Console({
                            level: configService.get('LOG_LEVEL') || 'info',
                            format: winston.format.combine(winston.format.timestamp(), winston.format.colorize(), winston.format.simple()),
                        }),
                        new winston.transports.File({
                            filename: 'logs/error.log',
                            level: 'error',
                            format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
                        }),
                        new winston.transports.File({
                            filename: 'logs/combined.log',
                            format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
                        }),
                    ],
                }),
                inject: [config_1.ConfigService],
            }),
            mongoose_1.MongooseModule.forRootAsync({
                useFactory: (configService) => ({
                    uri: configService.get('database.uri'),
                    useNewUrlParser: true,
                    useUnifiedTopology: true,
                }),
                inject: [config_1.ConfigService],
            }),
            cache_manager_1.CacheModule.registerAsync({
                isGlobal: true,
                useFactory: (configService) => ({
                    store: redisStore,
                    host: configService.get('redis.host'),
                    port: configService.get('redis.port'),
                    password: configService.get('redis.password'),
                    ttl: configService.get('redis.ttl'),
                }),
                inject: [config_1.ConfigService],
            }),
            throttler_1.ThrottlerModule.forRootAsync({
                useFactory: (configService) => ({
                    ttl: configService.get('THROTTLE_TTL') || 60,
                    limit: configService.get('THROTTLE_LIMIT') || 100,
                }),
                inject: [config_1.ConfigService],
            }),
            bull_1.BullModule.forRootAsync({
                useFactory: (configService) => ({
                    redis: {
                        host: configService.get('redis.host'),
                        port: configService.get('redis.port'),
                        password: configService.get('redis.password'),
                    },
                }),
                inject: [config_1.ConfigService],
            }),
            database_module_1.DatabaseModule,
            redis_module_1.RedisModule,
            email_module_1.EmailModule,
            sms_module_1.SmsModule,
            translation_module_1.TranslationModule,
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
            projects_module_1.ProjectsModule,
            service_providers_module_1.ServiceProvidersModule,
            quotes_module_1.QuotesModule,
            payments_module_1.PaymentsModule,
            files_module_1.FilesModule,
            notifications_module_1.NotificationsModule,
            reviews_module_1.ReviewsModule,
            tasks_module_1.TasksModule,
        ],
        controllers: [],
        providers: [],
    })
], AppModule);


/***/ }),
/* 8 */
/***/ ((module) => {

module.exports = require("@nestjs/mongoose");

/***/ }),
/* 9 */
/***/ ((module) => {

module.exports = require("@nestjs/cache-manager");

/***/ }),
/* 10 */
/***/ ((module) => {

module.exports = require("@nestjs/throttler");

/***/ }),
/* 11 */
/***/ ((module) => {

module.exports = require("@nestjs/bull");

/***/ }),
/* 12 */
/***/ ((module) => {

module.exports = require("nest-winston");

/***/ }),
/* 13 */
/***/ ((module) => {

module.exports = require("winston");

/***/ }),
/* 14 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AuthModule = void 0;
const common_1 = __webpack_require__(2);
const jwt_1 = __webpack_require__(15);
const passport_1 = __webpack_require__(16);
const config_1 = __webpack_require__(4);
const auth_service_1 = __webpack_require__(17);
const auth_controller_1 = __webpack_require__(22);
const users_module_1 = __webpack_require__(29);
const jwt_strategy_1 = __webpack_require__(37);
let AuthModule = class AuthModule {
};
exports.AuthModule = AuthModule;
exports.AuthModule = AuthModule = __decorate([
    (0, common_1.Module)({
        imports: [
            users_module_1.UsersModule,
            passport_1.PassportModule,
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    secret: configService.get('auth.jwt.secret'),
                    signOptions: {
                        expiresIn: configService.get('auth.jwt.expiresIn'),
                    },
                }),
                inject: [config_1.ConfigService],
            }),
        ],
        controllers: [auth_controller_1.AuthController],
        providers: [auth_service_1.AuthService, jwt_strategy_1.JwtStrategy],
        exports: [auth_service_1.AuthService],
    })
], AuthModule);


/***/ }),
/* 15 */
/***/ ((module) => {

module.exports = require("@nestjs/jwt");

/***/ }),
/* 16 */
/***/ ((module) => {

module.exports = require("@nestjs/passport");

/***/ }),
/* 17 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AuthService = void 0;
const common_1 = __webpack_require__(2);
const jwt_1 = __webpack_require__(15);
const config_1 = __webpack_require__(4);
const users_service_1 = __webpack_require__(18);
let AuthService = class AuthService {
    constructor(usersService, jwtService, configService) {
        this.usersService = usersService;
        this.jwtService = jwtService;
        this.configService = configService;
    }
    async register(registerDto) {
        const existingUser = await this.usersService.findByEmail(registerDto.email);
        if (existingUser) {
            throw new common_1.ConflictException('Email already exists');
        }
        const user = await this.usersService.create({
            email: registerDto.email,
            password: registerDto.password,
            userType: registerDto.userType,
            profile: registerDto.profile,
            preferences: registerDto.preferences,
        });
        const tokens = await this.generateTokens(user);
        return {
            user,
            ...tokens,
        };
    }
    async login(loginDto) {
        const { email, password } = loginDto;
        const user = await this.usersService.findByEmailWithPassword(email);
        if (!user) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        if (user.isLocked) {
            throw new common_1.UnauthorizedException('Account is temporarily locked');
        }
        const isPasswordValid = await this.usersService.validatePassword(user, password);
        if (!isPasswordValid) {
            await this.usersService.incrementLoginAttempts(user._id.toString());
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        await this.usersService.updateLastLogin(user._id.toString());
        const tokens = await this.generateTokens(user);
        const userWithoutPassword = user.toObject();
        delete userWithoutPassword.passwordHash;
        return {
            user: userWithoutPassword,
            ...tokens,
        };
    }
    async refreshToken(refreshToken) {
        try {
            const payload = this.jwtService.verify(refreshToken, {
                secret: this.configService.get('auth.jwt.refreshSecret'),
            });
            const user = await this.usersService.findById(payload.sub);
            if (!user) {
                throw new common_1.UnauthorizedException('Invalid refresh token');
            }
            const tokens = await this.generateTokens(user);
            return {
                user,
                ...tokens,
            };
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid refresh token');
        }
    }
    async validateUser(payload) {
        const user = await this.usersService.findById(payload.sub);
        if (!user) {
            throw new common_1.UnauthorizedException('User not found');
        }
        return user;
    }
    async generateTokens(user) {
        const payload = {
            sub: user._id.toString(),
            email: user.email,
            userType: user.userType,
        };
        const [accessToken, refreshToken] = await Promise.all([
            this.jwtService.signAsync(payload, {
                secret: this.configService.get('auth.jwt.secret'),
                expiresIn: this.configService.get('auth.jwt.expiresIn'),
            }),
            this.jwtService.signAsync(payload, {
                secret: this.configService.get('auth.jwt.refreshSecret'),
                expiresIn: this.configService.get('auth.jwt.refreshExpiresIn'),
            }),
        ]);
        return {
            accessToken,
            refreshToken,
        };
    }
    async forgotPassword(email) {
        const user = await this.usersService.findByEmail(email);
        if (!user) {
            return;
        }
        const resetToken = this.generateResetToken();
        const resetExpires = new Date(Date.now() + 3600000);
        await this.usersService.update(user._id.toString(), {
            'auth.resetPasswordToken': resetToken,
            'auth.resetPasswordExpires': resetExpires,
        });
    }
    async resetPassword(token, newPassword) {
        const user = await this.usersService.findByEmail('');
        if (!user || !user.auth.resetPasswordToken || user.auth.resetPasswordToken !== token) {
            throw new common_1.BadRequestException('Invalid or expired reset token');
        }
        if (user.auth.resetPasswordExpires < new Date()) {
            throw new common_1.BadRequestException('Reset token has expired');
        }
        await this.usersService.update(user._id.toString(), {
            password: newPassword,
            'auth.resetPasswordToken': undefined,
            'auth.resetPasswordExpires': undefined,
        });
    }
    generateResetToken() {
        return Math.random().toString(36).substring(2, 15) +
            Math.random().toString(36).substring(2, 15);
    }
    async verifyEmail(token) {
    }
    async resendVerificationEmail(email) {
        const user = await this.usersService.findByEmail(email);
        if (!user) {
            throw new common_1.BadRequestException('User not found');
        }
        if (user.auth.emailVerified) {
            throw new common_1.BadRequestException('Email already verified');
        }
        const verificationToken = this.generateResetToken();
        await this.usersService.update(user._id.toString(), {
            'auth.emailVerificationToken': verificationToken,
        });
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof users_service_1.UsersService !== "undefined" && users_service_1.UsersService) === "function" ? _a : Object, typeof (_b = typeof jwt_1.JwtService !== "undefined" && jwt_1.JwtService) === "function" ? _b : Object, typeof (_c = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _c : Object])
], AuthService);


/***/ }),
/* 18 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UsersService = void 0;
const common_1 = __webpack_require__(2);
const mongoose_1 = __webpack_require__(8);
const mongoose_2 = __webpack_require__(19);
const bcrypt = __webpack_require__(20);
const config_1 = __webpack_require__(4);
const user_schema_1 = __webpack_require__(21);
let UsersService = class UsersService {
    constructor(userModel, configService) {
        this.userModel = userModel;
        this.configService = configService;
    }
    async create(createUserDto) {
        const existingUser = await this.userModel.findOne({
            email: createUserDto.email.toLowerCase(),
        });
        if (existingUser) {
            throw new common_1.ConflictException('Email already exists');
        }
        const saltRounds = this.configService.get('auth.bcrypt.saltRounds');
        const passwordHash = await bcrypt.hash(createUserDto.password, saltRounds);
        const user = new this.userModel({
            ...createUserDto,
            email: createUserDto.email.toLowerCase(),
            passwordHash,
        });
        return user.save();
    }
    async findById(id) {
        if (!mongoose_2.Types.ObjectId.isValid(id)) {
            throw new common_1.BadRequestException('Invalid user ID');
        }
        const user = await this.userModel.findById(id);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        return user;
    }
    async findByEmail(email) {
        return this.userModel.findOne({ email: email.toLowerCase() });
    }
    async findByEmailWithPassword(email) {
        return this.userModel
            .findOne({ email: email.toLowerCase() })
            .select('+passwordHash');
    }
    async findAll(paginationDto, filters) {
        const { page = 1, limit = 20 } = paginationDto;
        const skip = (page - 1) * limit;
        const query = {};
        if (filters?.userType) {
            query.userType = filters.userType;
        }
        if (filters?.status) {
            query.status = filters.status;
        }
        if (filters?.search) {
            query.$or = [
                { email: { $regex: filters.search, $options: 'i' } },
                { 'profile.firstName': { $regex: filters.search, $options: 'i' } },
                { 'profile.lastName': { $regex: filters.search, $options: 'i' } },
            ];
        }
        const [users, total] = await Promise.all([
            this.userModel.find(query).skip(skip).limit(limit).sort({ createdAt: -1 }),
            this.userModel.countDocuments(query),
        ]);
        return {
            items: users,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async update(id, updateUserDto) {
        if (!mongoose_2.Types.ObjectId.isValid(id)) {
            throw new common_1.BadRequestException('Invalid user ID');
        }
        if (updateUserDto.email) {
            const existingUser = await this.userModel.findOne({
                email: updateUserDto.email.toLowerCase(),
                _id: { $ne: id },
            });
            if (existingUser) {
                throw new common_1.ConflictException('Email already exists');
            }
            updateUserDto.email = updateUserDto.email.toLowerCase();
        }
        const user = await this.userModel.findByIdAndUpdate(id, { $set: updateUserDto }, { new: true, runValidators: true });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        return user;
    }
    async changePassword(id, changePasswordDto) {
        if (!mongoose_2.Types.ObjectId.isValid(id)) {
            throw new common_1.BadRequestException('Invalid user ID');
        }
        const user = await this.userModel.findById(id).select('+passwordHash');
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const isCurrentPasswordValid = await bcrypt.compare(changePasswordDto.currentPassword, user.passwordHash);
        if (!isCurrentPasswordValid) {
            throw new common_1.UnauthorizedException('Current password is incorrect');
        }
        const saltRounds = this.configService.get('auth.bcrypt.saltRounds');
        const newPasswordHash = await bcrypt.hash(changePasswordDto.newPassword, saltRounds);
        await this.userModel.findByIdAndUpdate(id, {
            passwordHash: newPasswordHash,
        });
    }
    async validatePassword(user, password) {
        return bcrypt.compare(password, user.passwordHash);
    }
    async updateLastLogin(id) {
        await this.userModel.findByIdAndUpdate(id, {
            'auth.lastLogin': new Date(),
            'auth.loginAttempts': 0,
            $unset: { 'auth.lockUntil': 1 },
        });
    }
    async incrementLoginAttempts(id) {
        const maxAttempts = this.configService.get('auth.session.maxLoginAttempts');
        const lockoutDuration = this.configService.get('auth.session.lockoutDuration');
        const user = await this.userModel.findById(id);
        if (!user)
            return;
        const updates = { $inc: { 'auth.loginAttempts': 1 } };
        if (user.auth.loginAttempts + 1 >= maxAttempts) {
            updates['auth.lockUntil'] = new Date(Date.now() + lockoutDuration);
        }
        await this.userModel.findByIdAndUpdate(id, updates);
    }
    async verifyEmail(id) {
        await this.userModel.findByIdAndUpdate(id, {
            'auth.emailVerified': true,
            status: user_schema_1.UserStatus.ACTIVE,
            $unset: { 'auth.emailVerificationToken': 1 },
        });
    }
    async verifyPhone(id) {
        await this.userModel.findByIdAndUpdate(id, {
            'auth.phoneVerified': true,
        });
    }
    async remove(id) {
        if (!mongoose_2.Types.ObjectId.isValid(id)) {
            throw new common_1.BadRequestException('Invalid user ID');
        }
        const result = await this.userModel.findByIdAndUpdate(id, {
            status: user_schema_1.UserStatus.INACTIVE,
        });
        if (!result) {
            throw new common_1.NotFoundException('User not found');
        }
    }
    async findByLocation(lat, lng, radius = 50, userType) {
        const query = {
            'profile.address.coordinates': {
                $near: {
                    $geometry: {
                        type: 'Point',
                        coordinates: [lng, lat],
                    },
                    $maxDistance: radius * 1000,
                },
            },
        };
        if (userType) {
            query.userType = userType;
        }
        return this.userModel.find(query);
    }
    async getStats() {
        const [total, byType, byStatus, recentRegistrations] = await Promise.all([
            this.userModel.countDocuments(),
            this.userModel.aggregate([
                { $group: { _id: '$userType', count: { $sum: 1 } } },
            ]),
            this.userModel.aggregate([
                { $group: { _id: '$status', count: { $sum: 1 } } },
            ]),
            this.userModel.countDocuments({
                createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) },
            }),
        ]);
        return {
            total,
            byType: byType.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {}),
            byStatus: byStatus.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {}),
            recentRegistrations,
        };
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(user_schema_1.User.name)),
    __metadata("design:paramtypes", [typeof (_a = typeof mongoose_2.Model !== "undefined" && mongoose_2.Model) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object])
], UsersService);


/***/ }),
/* 19 */
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),
/* 20 */
/***/ ((module) => {

module.exports = require("bcryptjs");

/***/ }),
/* 21 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UserSchema = exports.User = exports.AuthInfo = exports.UserPreferences = exports.PrivacySettings = exports.NotificationPreferences = exports.UserProfile = exports.Address = exports.UserStatus = exports.UserType = void 0;
const mongoose_1 = __webpack_require__(8);
const mongoose_2 = __webpack_require__(19);
const swagger_1 = __webpack_require__(3);
var UserType;
(function (UserType) {
    UserType["HOMEOWNER"] = "homeowner";
    UserType["SERVICE_PROVIDER"] = "service_provider";
    UserType["ADMIN"] = "admin";
})(UserType || (exports.UserType = UserType = {}));
var UserStatus;
(function (UserStatus) {
    UserStatus["ACTIVE"] = "active";
    UserStatus["INACTIVE"] = "inactive";
    UserStatus["SUSPENDED"] = "suspended";
    UserStatus["PENDING_VERIFICATION"] = "pending_verification";
})(UserStatus || (exports.UserStatus = UserStatus = {}));
let Address = class Address {
};
exports.Address = Address;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '街道地址' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Address.prototype, "street", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '城市' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Address.prototype, "city", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '省份' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Address.prototype, "province", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邮政编码' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Address.prototype, "postalCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '国家', default: 'CA' }),
    (0, mongoose_1.Prop)({ default: 'CA' }),
    __metadata("design:type", String)
], Address.prototype, "country", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '地理坐标' }),
    (0, mongoose_1.Prop)({
        type: {
            lat: { type: Number, required: true },
            lng: { type: Number, required: true },
        },
    }),
    __metadata("design:type", Object)
], Address.prototype, "coordinates", void 0);
exports.Address = Address = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], Address);
let UserProfile = class UserProfile {
};
exports.UserProfile = UserProfile;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '名字' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], UserProfile.prototype, "firstName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '姓氏' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], UserProfile.prototype, "lastName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '电话号码' }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], UserProfile.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '头像URL' }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], UserProfile.prototype, "avatar", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '首选语言', enum: ['en', 'zh', 'fr'] }),
    (0, mongoose_1.Prop)({ enum: ['en', 'zh', 'fr'], default: 'en' }),
    __metadata("design:type", String)
], UserProfile.prototype, "language", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '时区' }),
    (0, mongoose_1.Prop)({ default: 'America/Toronto' }),
    __metadata("design:type", String)
], UserProfile.prototype, "timezone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '地址信息', type: Address }),
    (0, mongoose_1.Prop)({ type: Address }),
    __metadata("design:type", Address)
], UserProfile.prototype, "address", void 0);
exports.UserProfile = UserProfile = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], UserProfile);
let NotificationPreferences = class NotificationPreferences {
};
exports.NotificationPreferences = NotificationPreferences;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邮件通知' }),
    (0, mongoose_1.Prop)({ default: true }),
    __metadata("design:type", Boolean)
], NotificationPreferences.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '短信通知' }),
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], NotificationPreferences.prototype, "sms", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '推送通知' }),
    (0, mongoose_1.Prop)({ default: true }),
    __metadata("design:type", Boolean)
], NotificationPreferences.prototype, "push", void 0);
exports.NotificationPreferences = NotificationPreferences = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], NotificationPreferences);
let PrivacySettings = class PrivacySettings {
};
exports.PrivacySettings = PrivacySettings;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '显示个人资料' }),
    (0, mongoose_1.Prop)({ default: true }),
    __metadata("design:type", Boolean)
], PrivacySettings.prototype, "showProfile", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '显示位置信息' }),
    (0, mongoose_1.Prop)({ default: true }),
    __metadata("design:type", Boolean)
], PrivacySettings.prototype, "showLocation", void 0);
exports.PrivacySettings = PrivacySettings = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], PrivacySettings);
let UserPreferences = class UserPreferences {
};
exports.UserPreferences = UserPreferences;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '通知偏好', type: NotificationPreferences }),
    (0, mongoose_1.Prop)({ type: NotificationPreferences, default: () => ({}) }),
    __metadata("design:type", NotificationPreferences)
], UserPreferences.prototype, "notifications", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '隐私设置', type: PrivacySettings }),
    (0, mongoose_1.Prop)({ type: PrivacySettings, default: () => ({}) }),
    __metadata("design:type", PrivacySettings)
], UserPreferences.prototype, "privacy", void 0);
exports.UserPreferences = UserPreferences = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], UserPreferences);
let AuthInfo = class AuthInfo {
};
exports.AuthInfo = AuthInfo;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邮箱是否已验证' }),
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], AuthInfo.prototype, "emailVerified", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '手机是否已验证' }),
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], AuthInfo.prototype, "phoneVerified", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后登录时间' }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], AuthInfo.prototype, "lastLogin", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '登录尝试次数' }),
    (0, mongoose_1.Prop)({ default: 0 }),
    __metadata("design:type", Number)
], AuthInfo.prototype, "loginAttempts", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '锁定到期时间' }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], AuthInfo.prototype, "lockUntil", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '密码重置令牌' }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], AuthInfo.prototype, "resetPasswordToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '密码重置令牌过期时间' }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], AuthInfo.prototype, "resetPasswordExpires", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邮箱验证令牌' }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], AuthInfo.prototype, "emailVerificationToken", void 0);
exports.AuthInfo = AuthInfo = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], AuthInfo);
let User = class User {
    get fullName() {
        return `${this.profile.firstName} ${this.profile.lastName}`;
    }
    get isLocked() {
        return !!(this.auth.lockUntil && this.auth.lockUntil > new Date());
    }
};
exports.User = User;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", typeof (_d = typeof mongoose_2.Types !== "undefined" && mongoose_2.Types.ObjectId) === "function" ? _d : Object)
], User.prototype, "_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邮箱地址' }),
    (0, mongoose_1.Prop)({ required: true, unique: true, lowercase: true }),
    __metadata("design:type", String)
], User.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '密码哈希' }),
    (0, mongoose_1.Prop)({ required: true, select: false }),
    __metadata("design:type", String)
], User.prototype, "passwordHash", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户类型', enum: UserType }),
    (0, mongoose_1.Prop)({ enum: UserType, required: true }),
    __metadata("design:type", String)
], User.prototype, "userType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户状态', enum: UserStatus }),
    (0, mongoose_1.Prop)({ enum: UserStatus, default: UserStatus.PENDING_VERIFICATION }),
    __metadata("design:type", String)
], User.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户资料', type: UserProfile }),
    (0, mongoose_1.Prop)({ type: UserProfile, required: true }),
    __metadata("design:type", UserProfile)
], User.prototype, "profile", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户偏好', type: UserPreferences }),
    (0, mongoose_1.Prop)({ type: UserPreferences, default: () => ({}) }),
    __metadata("design:type", UserPreferences)
], User.prototype, "preferences", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '认证信息', type: AuthInfo }),
    (0, mongoose_1.Prop)({ type: AuthInfo, default: () => ({}) }),
    __metadata("design:type", AuthInfo)
], User.prototype, "auth", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], User.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], User.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '全名' }),
    __metadata("design:type", String),
    __metadata("design:paramtypes", [])
], User.prototype, "fullName", null);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否被锁定' }),
    __metadata("design:type", Boolean),
    __metadata("design:paramtypes", [])
], User.prototype, "isLocked", null);
exports.User = User = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], User);
exports.UserSchema = mongoose_1.SchemaFactory.createForClass(User);
exports.UserSchema.index({ email: 1 }, { unique: true });
exports.UserSchema.index({ userType: 1, status: 1 });
exports.UserSchema.index({ 'profile.address.city': 1, 'profile.address.province': 1 });
exports.UserSchema.index({ 'profile.address.coordinates': '2dsphere' });
exports.UserSchema.index({ createdAt: 1 });
exports.UserSchema.virtual('fullName').get(function () {
    return `${this.profile.firstName} ${this.profile.lastName}`;
});
exports.UserSchema.virtual('isLocked').get(function () {
    return !!(this.auth.lockUntil && this.auth.lockUntil > new Date());
});
exports.UserSchema.set('toJSON', {
    virtuals: true,
    transform: function (doc, ret) {
        delete ret.passwordHash;
        delete ret.__v;
        return ret;
    },
});


/***/ }),
/* 22 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AuthController = void 0;
const common_1 = __webpack_require__(2);
const swagger_1 = __webpack_require__(3);
const throttler_1 = __webpack_require__(10);
const auth_service_1 = __webpack_require__(17);
const login_dto_1 = __webpack_require__(23);
const register_dto_1 = __webpack_require__(25);
const auth_response_interface_1 = __webpack_require__(28);
let AuthController = class AuthController {
    constructor(authService) {
        this.authService = authService;
    }
    async register(registerDto) {
        return this.authService.register(registerDto);
    }
    async login(loginDto) {
        return this.authService.login(loginDto);
    }
    async refreshToken(refreshToken) {
        return this.authService.refreshToken(refreshToken);
    }
    async forgotPassword(email) {
        return this.authService.forgotPassword(email);
    }
    async resetPassword(token, newPassword) {
        return this.authService.resetPassword(token, newPassword);
    }
    async verifyEmail(token) {
        return this.authService.verifyEmail(token);
    }
    async resendVerificationEmail(email) {
        return this.authService.resendVerificationEmail(email);
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, common_1.Post)('register'),
    (0, swagger_1.ApiOperation)({ summary: '用户注册' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '注册成功',
        type: auth_response_interface_1.AuthResponse,
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: '邮箱已存在',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof register_dto_1.RegisterDto !== "undefined" && register_dto_1.RegisterDto) === "function" ? _b : Object]),
    __metadata("design:returntype", typeof (_c = typeof Promise !== "undefined" && Promise) === "function" ? _c : Object)
], AuthController.prototype, "register", null);
__decorate([
    (0, common_1.Post)('login'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '用户登录' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '登录成功',
        type: auth_response_interface_1.AuthResponse,
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '登录凭据无效',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_d = typeof login_dto_1.LoginDto !== "undefined" && login_dto_1.LoginDto) === "function" ? _d : Object]),
    __metadata("design:returntype", typeof (_e = typeof Promise !== "undefined" && Promise) === "function" ? _e : Object)
], AuthController.prototype, "login", null);
__decorate([
    (0, common_1.Post)('refresh'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '刷新访问令牌' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '令牌刷新成功',
        type: auth_response_interface_1.AuthResponse,
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '刷新令牌无效',
    }),
    __param(0, (0, common_1.Body)('refreshToken')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_f = typeof Promise !== "undefined" && Promise) === "function" ? _f : Object)
], AuthController.prototype, "refreshToken", null);
__decorate([
    (0, common_1.Post)('forgot-password'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: '忘记密码' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: '密码重置邮件已发送',
    }),
    __param(0, (0, common_1.Body)('email')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_g = typeof Promise !== "undefined" && Promise) === "function" ? _g : Object)
], AuthController.prototype, "forgotPassword", null);
__decorate([
    (0, common_1.Post)('reset-password'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: '重置密码' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: '密码重置成功',
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '重置令牌无效或已过期',
    }),
    __param(0, (0, common_1.Body)('token')),
    __param(1, (0, common_1.Body)('newPassword')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", typeof (_h = typeof Promise !== "undefined" && Promise) === "function" ? _h : Object)
], AuthController.prototype, "resetPassword", null);
__decorate([
    (0, common_1.Post)('verify-email'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: '验证邮箱' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: '邮箱验证成功',
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '验证令牌无效',
    }),
    __param(0, (0, common_1.Body)('token')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_j = typeof Promise !== "undefined" && Promise) === "function" ? _j : Object)
], AuthController.prototype, "verifyEmail", null);
__decorate([
    (0, common_1.Post)('resend-verification'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: '重新发送验证邮件' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: '验证邮件已发送',
    }),
    __param(0, (0, common_1.Body)('email')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_k = typeof Promise !== "undefined" && Promise) === "function" ? _k : Object)
], AuthController.prototype, "resendVerificationEmail", null);
exports.AuthController = AuthController = __decorate([
    (0, swagger_1.ApiTags)('Auth'),
    (0, common_1.Controller)('auth'),
    (0, common_1.UseGuards)(throttler_1.ThrottlerGuard),
    __metadata("design:paramtypes", [typeof (_a = typeof auth_service_1.AuthService !== "undefined" && auth_service_1.AuthService) === "function" ? _a : Object])
], AuthController);


/***/ }),
/* 23 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.LoginDto = void 0;
const class_validator_1 = __webpack_require__(24);
const swagger_1 = __webpack_require__(3);
class LoginDto {
}
exports.LoginDto = LoginDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邮箱地址' }),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], LoginDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '密码' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1),
    __metadata("design:type", String)
], LoginDto.prototype, "password", void 0);


/***/ }),
/* 24 */
/***/ ((module) => {

module.exports = require("class-validator");

/***/ }),
/* 25 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RegisterDto = void 0;
const create_user_dto_1 = __webpack_require__(26);
class RegisterDto extends create_user_dto_1.CreateUserDto {
}
exports.RegisterDto = RegisterDto;


/***/ }),
/* 26 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CreateUserDto = exports.UserPreferencesDto = exports.PrivacySettingsDto = exports.NotificationPreferencesDto = exports.CreateUserProfileDto = exports.CoordinatesDto = exports.CreateAddressDto = void 0;
const class_validator_1 = __webpack_require__(24);
const class_transformer_1 = __webpack_require__(27);
const swagger_1 = __webpack_require__(3);
const user_schema_1 = __webpack_require__(21);
class CreateAddressDto {
    constructor() {
        this.country = 'CA';
    }
}
exports.CreateAddressDto = CreateAddressDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '街道地址' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(200),
    __metadata("design:type", String)
], CreateAddressDto.prototype, "street", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '城市' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateAddressDto.prototype, "city", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '省份' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateAddressDto.prototype, "province", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邮政编码' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(10),
    __metadata("design:type", String)
], CreateAddressDto.prototype, "postalCode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '国家', default: 'CA' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(2),
    __metadata("design:type", String)
], CreateAddressDto.prototype, "country", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '地理坐标' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => CoordinatesDto),
    __metadata("design:type", CoordinatesDto)
], CreateAddressDto.prototype, "coordinates", void 0);
class CoordinatesDto {
}
exports.CoordinatesDto = CoordinatesDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '纬度' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CoordinatesDto.prototype, "lat", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '经度' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CoordinatesDto.prototype, "lng", void 0);
class CreateUserProfileDto {
    constructor() {
        this.language = 'en';
        this.timezone = 'America/Toronto';
    }
}
exports.CreateUserProfileDto = CreateUserProfileDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '名字' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], CreateUserProfileDto.prototype, "firstName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '姓氏' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], CreateUserProfileDto.prototype, "lastName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '电话号码' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsPhoneNumber)('CA'),
    __metadata("design:type", String)
], CreateUserProfileDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '头像URL' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateUserProfileDto.prototype, "avatar", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '首选语言', enum: ['en', 'zh', 'fr'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['en', 'zh', 'fr']),
    __metadata("design:type", String)
], CreateUserProfileDto.prototype, "language", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '时区' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateUserProfileDto.prototype, "timezone", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '地址信息', type: CreateAddressDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => CreateAddressDto),
    __metadata("design:type", CreateAddressDto)
], CreateUserProfileDto.prototype, "address", void 0);
class NotificationPreferencesDto {
    constructor() {
        this.email = true;
        this.sms = false;
        this.push = true;
    }
}
exports.NotificationPreferencesDto = NotificationPreferencesDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '邮件通知', default: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], NotificationPreferencesDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '短信通知', default: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], NotificationPreferencesDto.prototype, "sms", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '推送通知', default: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], NotificationPreferencesDto.prototype, "push", void 0);
class PrivacySettingsDto {
    constructor() {
        this.showProfile = true;
        this.showLocation = true;
    }
}
exports.PrivacySettingsDto = PrivacySettingsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '显示个人资料', default: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PrivacySettingsDto.prototype, "showProfile", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '显示位置信息', default: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PrivacySettingsDto.prototype, "showLocation", void 0);
class UserPreferencesDto {
}
exports.UserPreferencesDto = UserPreferencesDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '通知偏好', type: NotificationPreferencesDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => NotificationPreferencesDto),
    __metadata("design:type", NotificationPreferencesDto)
], UserPreferencesDto.prototype, "notifications", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '隐私设置', type: PrivacySettingsDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => PrivacySettingsDto),
    __metadata("design:type", PrivacySettingsDto)
], UserPreferencesDto.prototype, "privacy", void 0);
class CreateUserDto {
}
exports.CreateUserDto = CreateUserDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邮箱地址' }),
    (0, class_validator_1.IsEmail)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CreateUserDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '密码', minLength: 8 }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(8),
    (0, class_validator_1.MaxLength)(128),
    __metadata("design:type", String)
], CreateUserDto.prototype, "password", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户类型', enum: user_schema_1.UserType }),
    (0, class_validator_1.IsEnum)(user_schema_1.UserType),
    __metadata("design:type", typeof (_a = typeof user_schema_1.UserType !== "undefined" && user_schema_1.UserType) === "function" ? _a : Object)
], CreateUserDto.prototype, "userType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户资料', type: CreateUserProfileDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => CreateUserProfileDto),
    __metadata("design:type", CreateUserProfileDto)
], CreateUserDto.prototype, "profile", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '用户偏好', type: UserPreferencesDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => UserPreferencesDto),
    __metadata("design:type", UserPreferencesDto)
], CreateUserDto.prototype, "preferences", void 0);


/***/ }),
/* 27 */
/***/ ((module) => {

module.exports = require("class-transformer");

/***/ }),
/* 28 */
/***/ ((__unused_webpack_module, exports) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));


/***/ }),
/* 29 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UsersModule = void 0;
const common_1 = __webpack_require__(2);
const mongoose_1 = __webpack_require__(8);
const users_service_1 = __webpack_require__(18);
const users_controller_1 = __webpack_require__(30);
const user_schema_1 = __webpack_require__(21);
let UsersModule = class UsersModule {
};
exports.UsersModule = UsersModule;
exports.UsersModule = UsersModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([{ name: user_schema_1.User.name, schema: user_schema_1.UserSchema }]),
        ],
        controllers: [users_controller_1.UsersController],
        providers: [users_service_1.UsersService],
        exports: [users_service_1.UsersService],
    })
], UsersModule);


/***/ }),
/* 30 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UsersController = void 0;
const common_1 = __webpack_require__(2);
const swagger_1 = __webpack_require__(3);
const throttler_1 = __webpack_require__(10);
const users_service_1 = __webpack_require__(18);
const create_user_dto_1 = __webpack_require__(26);
const update_user_dto_1 = __webpack_require__(31);
const change_password_dto_1 = __webpack_require__(32);
const pagination_dto_1 = __webpack_require__(33);
const jwt_auth_guard_1 = __webpack_require__(34);
const roles_guard_1 = __webpack_require__(35);
const roles_decorator_1 = __webpack_require__(36);
const user_schema_1 = __webpack_require__(21);
let UsersController = class UsersController {
    constructor(usersService) {
        this.usersService = usersService;
    }
    async create(createUserDto) {
        return this.usersService.create(createUserDto);
    }
    async findAll(paginationDto, userType, status, search) {
        return this.usersService.findAll(paginationDto, {
            userType,
            status,
            search,
        });
    }
    async getProfile(req) {
        return this.usersService.findById(req.user.id);
    }
    async getStats() {
        return this.usersService.getStats();
    }
    async findNearby(lat, lng, radius, userType) {
        return this.usersService.findByLocation(lat, lng, radius, userType);
    }
    async findOne(id) {
        return this.usersService.findById(id);
    }
    async updateProfile(req, updateUserDto) {
        return this.usersService.update(req.user.id, updateUserDto);
    }
    async update(id, updateUserDto) {
        return this.usersService.update(id, updateUserDto);
    }
    async changePassword(req, changePasswordDto) {
        return this.usersService.changePassword(req.user.id, changePasswordDto);
    }
    async verifyEmail(id) {
        return this.usersService.verifyEmail(id);
    }
    async verifyPhone(id) {
        return this.usersService.verifyPhone(id);
    }
    async deleteAccount(req) {
        return this.usersService.remove(req.user.id);
    }
    async remove(id) {
        return this.usersService.remove(id);
    }
};
exports.UsersController = UsersController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建新用户' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '用户创建成功',
        type: user_schema_1.User,
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: '邮箱已存在',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof create_user_dto_1.CreateUserDto !== "undefined" && create_user_dto_1.CreateUserDto) === "function" ? _b : Object]),
    __metadata("design:returntype", typeof (_c = typeof Promise !== "undefined" && Promise) === "function" ? _c : Object)
], UsersController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户列表（管理员）' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'userType', required: false, enum: user_schema_1.UserType }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, enum: user_schema_1.UserStatus }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '用户列表获取成功',
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Query)('userType')),
    __param(2, (0, common_1.Query)('status')),
    __param(3, (0, common_1.Query)('search')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_d = typeof pagination_dto_1.PaginationDto !== "undefined" && pagination_dto_1.PaginationDto) === "function" ? _d : Object, typeof (_e = typeof user_schema_1.UserType !== "undefined" && user_schema_1.UserType) === "function" ? _e : Object, typeof (_f = typeof user_schema_1.UserStatus !== "undefined" && user_schema_1.UserStatus) === "function" ? _f : Object, String]),
    __metadata("design:returntype", typeof (_g = typeof Promise !== "undefined" && Promise) === "function" ? _g : Object)
], UsersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('me'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: '获取当前用户信息' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '用户信息获取成功',
        type: user_schema_1.User,
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_h = typeof Promise !== "undefined" && Promise) === "function" ? _h : Object)
], UsersController.prototype, "getProfile", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户统计信息（管理员）' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '统计信息获取成功',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)('nearby'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: '根据地理位置查找附近用户' }),
    (0, swagger_1.ApiQuery)({ name: 'lat', required: true, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'lng', required: true, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'radius', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'userType', required: false, enum: user_schema_1.UserType }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '附近用户列表',
        type: [user_schema_1.User],
    }),
    __param(0, (0, common_1.Query)('lat')),
    __param(1, (0, common_1.Query)('lng')),
    __param(2, (0, common_1.Query)('radius')),
    __param(3, (0, common_1.Query)('userType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Number, typeof (_j = typeof user_schema_1.UserType !== "undefined" && user_schema_1.UserType) === "function" ? _j : Object]),
    __metadata("design:returntype", typeof (_k = typeof Promise !== "undefined" && Promise) === "function" ? _k : Object)
], UsersController.prototype, "findNearby", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取用户信息' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '用户信息获取成功',
        type: user_schema_1.User,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '用户不存在',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_l = typeof Promise !== "undefined" && Promise) === "function" ? _l : Object)
], UsersController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)('me'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: '更新当前用户信息' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '用户信息更新成功',
        type: user_schema_1.User,
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, typeof (_m = typeof update_user_dto_1.UpdateUserDto !== "undefined" && update_user_dto_1.UpdateUserDto) === "function" ? _m : Object]),
    __metadata("design:returntype", typeof (_o = typeof Promise !== "undefined" && Promise) === "function" ? _o : Object)
], UsersController.prototype, "updateProfile", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: '更新用户信息（管理员）' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '用户信息更新成功',
        type: user_schema_1.User,
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, typeof (_p = typeof update_user_dto_1.UpdateUserDto !== "undefined" && update_user_dto_1.UpdateUserDto) === "function" ? _p : Object]),
    __metadata("design:returntype", typeof (_q = typeof Promise !== "undefined" && Promise) === "function" ? _q : Object)
], UsersController.prototype, "update", null);
__decorate([
    (0, common_1.Post)('me/change-password'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: '修改当前用户密码' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: '密码修改成功',
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '当前密码错误',
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, typeof (_r = typeof change_password_dto_1.ChangePasswordDto !== "undefined" && change_password_dto_1.ChangePasswordDto) === "function" ? _r : Object]),
    __metadata("design:returntype", typeof (_s = typeof Promise !== "undefined" && Promise) === "function" ? _s : Object)
], UsersController.prototype, "changePassword", null);
__decorate([
    (0, common_1.Post)(':id/verify-email'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: '验证用户邮箱（管理员）' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: '邮箱验证成功',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_t = typeof Promise !== "undefined" && Promise) === "function" ? _t : Object)
], UsersController.prototype, "verifyEmail", null);
__decorate([
    (0, common_1.Post)(':id/verify-phone'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: '验证用户手机号（管理员）' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: '手机号验证成功',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_u = typeof Promise !== "undefined" && Promise) === "function" ? _u : Object)
], UsersController.prototype, "verifyPhone", null);
__decorate([
    (0, common_1.Delete)('me'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: '删除当前用户账户' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: '账户删除成功',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_v = typeof Promise !== "undefined" && Promise) === "function" ? _v : Object)
], UsersController.prototype, "deleteAccount", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: '删除用户（管理员）' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: '用户删除成功',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_w = typeof Promise !== "undefined" && Promise) === "function" ? _w : Object)
], UsersController.prototype, "remove", null);
exports.UsersController = UsersController = __decorate([
    (0, swagger_1.ApiTags)('Users'),
    (0, common_1.Controller)('users'),
    (0, common_1.UseGuards)(throttler_1.ThrottlerGuard),
    __metadata("design:paramtypes", [typeof (_a = typeof users_service_1.UsersService !== "undefined" && users_service_1.UsersService) === "function" ? _a : Object])
], UsersController);


/***/ }),
/* 31 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UpdateUserDto = void 0;
const swagger_1 = __webpack_require__(3);
const create_user_dto_1 = __webpack_require__(26);
class UpdateUserDto extends (0, swagger_1.PartialType)((0, swagger_1.OmitType)(create_user_dto_1.CreateUserDto, ['password'])) {
}
exports.UpdateUserDto = UpdateUserDto;


/***/ }),
/* 32 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ChangePasswordDto = void 0;
const class_validator_1 = __webpack_require__(24);
const swagger_1 = __webpack_require__(3);
class ChangePasswordDto {
}
exports.ChangePasswordDto = ChangePasswordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前密码' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1),
    __metadata("design:type", String)
], ChangePasswordDto.prototype, "currentPassword", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '新密码', minLength: 8 }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(8),
    (0, class_validator_1.MaxLength)(128),
    __metadata("design:type", String)
], ChangePasswordDto.prototype, "newPassword", void 0);


/***/ }),
/* 33 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.PaginationDto = void 0;
const class_validator_1 = __webpack_require__(24);
const class_transformer_1 = __webpack_require__(27);
const swagger_1 = __webpack_require__(3);
class PaginationDto {
    constructor() {
        this.page = 1;
        this.limit = 20;
    }
}
exports.PaginationDto = PaginationDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '页码', minimum: 1, default: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsPositive)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], PaginationDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '每页数量', minimum: 1, maximum: 100, default: 20 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsPositive)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], PaginationDto.prototype, "limit", void 0);


/***/ }),
/* 34 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.JwtAuthGuard = void 0;
const common_1 = __webpack_require__(2);
const passport_1 = __webpack_require__(16);
let JwtAuthGuard = class JwtAuthGuard extends (0, passport_1.AuthGuard)('jwt') {
};
exports.JwtAuthGuard = JwtAuthGuard;
exports.JwtAuthGuard = JwtAuthGuard = __decorate([
    (0, common_1.Injectable)()
], JwtAuthGuard);


/***/ }),
/* 35 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RolesGuard = void 0;
const common_1 = __webpack_require__(2);
const core_1 = __webpack_require__(1);
const roles_decorator_1 = __webpack_require__(36);
const user_schema_1 = __webpack_require__(21);
let RolesGuard = class RolesGuard {
    constructor(reflector) {
        this.reflector = reflector;
    }
    canActivate(context) {
        const requiredRoles = this.reflector.getAllAndOverride(roles_decorator_1.ROLES_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);
        if (!requiredRoles) {
            return true;
        }
        const { user } = context.switchToHttp().getRequest();
        if (user.userType === user_schema_1.UserType.ADMIN) {
            return true;
        }
        return requiredRoles.some((role) => user.userType === role);
    }
};
exports.RolesGuard = RolesGuard;
exports.RolesGuard = RolesGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof core_1.Reflector !== "undefined" && core_1.Reflector) === "function" ? _a : Object])
], RolesGuard);


/***/ }),
/* 36 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Roles = exports.ROLES_KEY = void 0;
const common_1 = __webpack_require__(2);
exports.ROLES_KEY = 'roles';
const Roles = (...roles) => (0, common_1.SetMetadata)(exports.ROLES_KEY, roles);
exports.Roles = Roles;


/***/ }),
/* 37 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.JwtStrategy = void 0;
const common_1 = __webpack_require__(2);
const passport_1 = __webpack_require__(16);
const passport_jwt_1 = __webpack_require__(38);
const config_1 = __webpack_require__(4);
const auth_service_1 = __webpack_require__(17);
let JwtStrategy = class JwtStrategy extends (0, passport_1.PassportStrategy)(passport_jwt_1.Strategy) {
    constructor(authService, configService) {
        super({
            jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configService.get('auth.jwt.secret'),
        });
        this.authService = authService;
        this.configService = configService;
    }
    async validate(payload) {
        try {
            const user = await this.authService.validateUser(payload);
            return user;
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid token');
        }
    }
};
exports.JwtStrategy = JwtStrategy;
exports.JwtStrategy = JwtStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof auth_service_1.AuthService !== "undefined" && auth_service_1.AuthService) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object])
], JwtStrategy);


/***/ }),
/* 38 */
/***/ ((module) => {

module.exports = require("passport-jwt");

/***/ }),
/* 39 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ProjectsModule = void 0;
const common_1 = __webpack_require__(2);
const mongoose_1 = __webpack_require__(8);
const projects_service_1 = __webpack_require__(40);
const projects_controller_1 = __webpack_require__(42);
const project_schema_1 = __webpack_require__(41);
let ProjectsModule = class ProjectsModule {
};
exports.ProjectsModule = ProjectsModule;
exports.ProjectsModule = ProjectsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([{ name: project_schema_1.Project.name, schema: project_schema_1.ProjectSchema }]),
        ],
        controllers: [projects_controller_1.ProjectsController],
        providers: [projects_service_1.ProjectsService],
        exports: [projects_service_1.ProjectsService],
    })
], ProjectsModule);


/***/ }),
/* 40 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ProjectsService = void 0;
const common_1 = __webpack_require__(2);
const mongoose_1 = __webpack_require__(8);
const mongoose_2 = __webpack_require__(19);
const project_schema_1 = __webpack_require__(41);
let ProjectsService = class ProjectsService {
    constructor(projectModel) {
        this.projectModel = projectModel;
    }
    async create(createProjectDto, user) {
        if (createProjectDto.budget.min > createProjectDto.budget.max) {
            throw new common_1.BadRequestException('Minimum budget cannot be greater than maximum budget');
        }
        if (createProjectDto.timeline?.preferredStartDate && createProjectDto.timeline?.expectedEndDate) {
            const startDate = new Date(createProjectDto.timeline.preferredStartDate);
            const endDate = new Date(createProjectDto.timeline.expectedEndDate);
            if (startDate >= endDate) {
                throw new common_1.BadRequestException('Start date must be before end date');
            }
        }
        const projectData = {
            ...createProjectDto,
            owner: {
                userId: user._id,
                name: `${user.profile.firstName} ${user.profile.lastName}`,
                email: user.email,
                phone: user.profile.phone,
            },
            status: project_schema_1.ProjectStatus.DRAFT,
        };
        const createdProject = new this.projectModel(projectData);
        return createdProject.save();
    }
    async findAll(query) {
        const { page = 1, limit = 20, status, category, province, city, minBudget, maxBudget, search, sortBy = 'createdAt', sortOrder = 'desc', lat, lng, radius = 50, } = query;
        const filter = {};
        if (status) {
            filter.status = status;
        }
        if (category) {
            filter.category = category;
        }
        if (province) {
            filter['location.province'] = new RegExp(province, 'i');
        }
        if (city) {
            filter['location.city'] = new RegExp(city, 'i');
        }
        if (minBudget !== undefined || maxBudget !== undefined) {
            filter.$and = filter.$and || [];
            if (minBudget !== undefined) {
                filter.$and.push({ 'budget.max': { $gte: minBudget } });
            }
            if (maxBudget !== undefined) {
                filter.$and.push({ 'budget.min': { $lte: maxBudget } });
            }
        }
        if (search) {
            filter.$or = [
                { title: new RegExp(search, 'i') },
                { description: new RegExp(search, 'i') },
                { 'requirements.specialRequirements': new RegExp(search, 'i') },
            ];
        }
        if (lat && lng) {
            filter['location.coordinates'] = {
                $near: {
                    $geometry: {
                        type: 'Point',
                        coordinates: [lng, lat],
                    },
                    $maxDistance: radius * 1000,
                },
            };
        }
        const sort = {};
        sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
        const skip = (page - 1) * limit;
        const [items, total] = await Promise.all([
            this.projectModel
                .find(filter)
                .sort(sort)
                .skip(skip)
                .limit(limit)
                .populate('owner.userId', 'profile.firstName profile.lastName profile.avatar')
                .exec(),
            this.projectModel.countDocuments(filter),
        ]);
        return {
            items,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async findOne(id) {
        if (!mongoose_2.Types.ObjectId.isValid(id)) {
            throw new common_1.BadRequestException('Invalid project ID');
        }
        const project = await this.projectModel
            .findById(id)
            .populate('owner.userId', 'profile.firstName profile.lastName profile.avatar profile.phone')
            .populate('matches.providerId', 'businessInfo.companyName profile.firstName profile.lastName')
            .populate('quotes.providerId', 'businessInfo.companyName profile.firstName profile.lastName')
            .populate('selectedProvider.providerId', 'businessInfo.companyName profile.firstName profile.lastName')
            .exec();
        if (!project) {
            throw new common_1.NotFoundException('Project not found');
        }
        return project;
    }
    async update(id, updateProjectDto, user) {
        if (!mongoose_2.Types.ObjectId.isValid(id)) {
            throw new common_1.BadRequestException('Invalid project ID');
        }
        const project = await this.projectModel.findById(id);
        if (!project) {
            throw new common_1.NotFoundException('Project not found');
        }
        if (project.owner.userId.toString() !== user._id.toString()) {
            throw new common_1.ForbiddenException('You can only update your own projects');
        }
        if (updateProjectDto.budget) {
            const minBudget = updateProjectDto.budget.min ?? project.budget.min;
            const maxBudget = updateProjectDto.budget.max ?? project.budget.max;
            if (minBudget > maxBudget) {
                throw new common_1.BadRequestException('Minimum budget cannot be greater than maximum budget');
            }
        }
        if (updateProjectDto.timeline) {
            const startDate = updateProjectDto.timeline.preferredStartDate
                ? new Date(updateProjectDto.timeline.preferredStartDate)
                : project.timeline?.preferredStartDate;
            const endDate = updateProjectDto.timeline.expectedEndDate
                ? new Date(updateProjectDto.timeline.expectedEndDate)
                : project.timeline?.expectedEndDate;
            if (startDate && endDate && startDate >= endDate) {
                throw new common_1.BadRequestException('Start date must be before end date');
            }
        }
        const updatedProject = await this.projectModel
            .findByIdAndUpdate(id, updateProjectDto, { new: true })
            .populate('owner.userId', 'profile.firstName profile.lastName profile.avatar')
            .exec();
        return updatedProject;
    }
    async remove(id, user) {
        if (!mongoose_2.Types.ObjectId.isValid(id)) {
            throw new common_1.BadRequestException('Invalid project ID');
        }
        const project = await this.projectModel.findById(id);
        if (!project) {
            throw new common_1.NotFoundException('Project not found');
        }
        if (project.owner.userId.toString() !== user._id.toString()) {
            throw new common_1.ForbiddenException('You can only delete your own projects');
        }
        if (project.status === project_schema_1.ProjectStatus.IN_PROGRESS) {
            throw new common_1.BadRequestException('Cannot delete project that is in progress');
        }
        await this.projectModel.findByIdAndDelete(id);
    }
    async publish(id, user) {
        if (!mongoose_2.Types.ObjectId.isValid(id)) {
            throw new common_1.BadRequestException('Invalid project ID');
        }
        const project = await this.projectModel.findById(id);
        if (!project) {
            throw new common_1.NotFoundException('Project not found');
        }
        if (project.owner.userId.toString() !== user._id.toString()) {
            throw new common_1.ForbiddenException('You can only publish your own projects');
        }
        if (project.status !== project_schema_1.ProjectStatus.DRAFT) {
            throw new common_1.BadRequestException('Only draft projects can be published');
        }
        const updatedProject = await this.projectModel
            .findByIdAndUpdate(id, {
            status: project_schema_1.ProjectStatus.ACTIVE,
            publishedAt: new Date(),
        }, { new: true })
            .populate('owner.userId', 'profile.firstName profile.lastName profile.avatar')
            .exec();
        return updatedProject;
    }
    async findByUser(userId, query) {
        if (!mongoose_2.Types.ObjectId.isValid(userId)) {
            throw new common_1.BadRequestException('Invalid user ID');
        }
        const modifiedQuery = {
            ...query,
        };
        const filter = { 'owner.userId': userId };
        if (query.status) {
            filter.status = query.status;
        }
        if (query.category) {
            filter.category = query.category;
        }
        const { page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'desc' } = query;
        const skip = (page - 1) * limit;
        const sort = {};
        sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
        const [items, total] = await Promise.all([
            this.projectModel
                .find(filter)
                .sort(sort)
                .skip(skip)
                .limit(limit)
                .exec(),
            this.projectModel.countDocuments(filter),
        ]);
        return {
            items,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async findNearby(lat, lng, radius = 50) {
        return this.projectModel
            .find({
            'location.coordinates': {
                $near: {
                    $geometry: {
                        type: 'Point',
                        coordinates: [lng, lat],
                    },
                    $maxDistance: radius * 1000,
                },
            },
            status: project_schema_1.ProjectStatus.ACTIVE,
        })
            .limit(20)
            .populate('owner.userId', 'profile.firstName profile.lastName')
            .exec();
    }
    async getStats(userId) {
        const matchStage = {};
        if (userId) {
            matchStage['owner.userId'] = new mongoose_2.Types.ObjectId(userId);
        }
        const stats = await this.projectModel.aggregate([
            { $match: matchStage },
            {
                $group: {
                    _id: null,
                    total: { $sum: 1 },
                    draft: { $sum: { $cond: [{ $eq: ['$status', project_schema_1.ProjectStatus.DRAFT] }, 1, 0] } },
                    active: { $sum: { $cond: [{ $eq: ['$status', project_schema_1.ProjectStatus.ACTIVE] }, 1, 0] } },
                    inProgress: { $sum: { $cond: [{ $eq: ['$status', project_schema_1.ProjectStatus.IN_PROGRESS] }, 1, 0] } },
                    completed: { $sum: { $cond: [{ $eq: ['$status', project_schema_1.ProjectStatus.COMPLETED] }, 1, 0] } },
                    cancelled: { $sum: { $cond: [{ $eq: ['$status', project_schema_1.ProjectStatus.CANCELLED] }, 1, 0] } },
                    avgBudgetMin: { $avg: '$budget.min' },
                    avgBudgetMax: { $avg: '$budget.max' },
                },
            },
        ]);
        return stats[0] || {
            total: 0,
            draft: 0,
            active: 0,
            inProgress: 0,
            completed: 0,
            cancelled: 0,
            avgBudgetMin: 0,
            avgBudgetMax: 0,
        };
    }
};
exports.ProjectsService = ProjectsService;
exports.ProjectsService = ProjectsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(project_schema_1.Project.name)),
    __metadata("design:paramtypes", [typeof (_a = typeof mongoose_2.Model !== "undefined" && mongoose_2.Model) === "function" ? _a : Object])
], ProjectsService);


/***/ }),
/* 41 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ProjectSchema = exports.Project = exports.ProjectProgress = exports.ProjectMilestone = exports.SelectedProvider = exports.ProjectQuote = exports.ProjectMatch = exports.ProjectAttachment = exports.ProjectRequirements = exports.PermitInfo = exports.MaterialPreferences = exports.ProjectLocation = exports.Coordinates = exports.ProjectTimeline = exports.ProjectBudget = exports.ProjectOwner = exports.MilestoneStatus = exports.QuoteStatus = exports.MatchStatus = exports.AttachmentType = exports.Urgency = exports.TimelineFlexibility = exports.BudgetFlexibility = exports.ProjectCategory = exports.ProjectStatus = void 0;
const mongoose_1 = __webpack_require__(8);
const mongoose_2 = __webpack_require__(19);
const swagger_1 = __webpack_require__(3);
var ProjectStatus;
(function (ProjectStatus) {
    ProjectStatus["DRAFT"] = "draft";
    ProjectStatus["ACTIVE"] = "active";
    ProjectStatus["IN_PROGRESS"] = "in_progress";
    ProjectStatus["COMPLETED"] = "completed";
    ProjectStatus["CANCELLED"] = "cancelled";
})(ProjectStatus || (exports.ProjectStatus = ProjectStatus = {}));
var ProjectCategory;
(function (ProjectCategory) {
    ProjectCategory["FULL_RENOVATION"] = "full_renovation";
    ProjectCategory["KITCHEN"] = "kitchen";
    ProjectCategory["BATHROOM"] = "bathroom";
    ProjectCategory["FLOORING"] = "flooring";
    ProjectCategory["PAINTING"] = "painting";
    ProjectCategory["ELECTRICAL"] = "electrical";
    ProjectCategory["PLUMBING"] = "plumbing";
    ProjectCategory["ROOFING"] = "roofing";
    ProjectCategory["LANDSCAPING"] = "landscaping";
    ProjectCategory["OTHER"] = "other";
})(ProjectCategory || (exports.ProjectCategory = ProjectCategory = {}));
var BudgetFlexibility;
(function (BudgetFlexibility) {
    BudgetFlexibility["FIRM"] = "firm";
    BudgetFlexibility["FLEXIBLE"] = "flexible";
    BudgetFlexibility["NEGOTIABLE"] = "negotiable";
})(BudgetFlexibility || (exports.BudgetFlexibility = BudgetFlexibility = {}));
var TimelineFlexibility;
(function (TimelineFlexibility) {
    TimelineFlexibility["FIRM"] = "firm";
    TimelineFlexibility["FLEXIBLE"] = "flexible";
})(TimelineFlexibility || (exports.TimelineFlexibility = TimelineFlexibility = {}));
var Urgency;
(function (Urgency) {
    Urgency["LOW"] = "low";
    Urgency["MEDIUM"] = "medium";
    Urgency["HIGH"] = "high";
})(Urgency || (exports.Urgency = Urgency = {}));
var AttachmentType;
(function (AttachmentType) {
    AttachmentType["IMAGE"] = "image";
    AttachmentType["DOCUMENT"] = "document";
    AttachmentType["FLOORPLAN"] = "floorplan";
})(AttachmentType || (exports.AttachmentType = AttachmentType = {}));
var MatchStatus;
(function (MatchStatus) {
    MatchStatus["MATCHED"] = "matched";
    MatchStatus["CONTACTED"] = "contacted";
    MatchStatus["DECLINED"] = "declined";
})(MatchStatus || (exports.MatchStatus = MatchStatus = {}));
var QuoteStatus;
(function (QuoteStatus) {
    QuoteStatus["PENDING"] = "pending";
    QuoteStatus["ACCEPTED"] = "accepted";
    QuoteStatus["DECLINED"] = "declined";
    QuoteStatus["EXPIRED"] = "expired";
})(QuoteStatus || (exports.QuoteStatus = QuoteStatus = {}));
var MilestoneStatus;
(function (MilestoneStatus) {
    MilestoneStatus["PENDING"] = "pending";
    MilestoneStatus["IN_PROGRESS"] = "in_progress";
    MilestoneStatus["COMPLETED"] = "completed";
})(MilestoneStatus || (exports.MilestoneStatus = MilestoneStatus = {}));
let ProjectOwner = class ProjectOwner {
};
exports.ProjectOwner = ProjectOwner;
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Types.ObjectId, required: true, ref: 'User' }),
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", typeof (_a = typeof mongoose_2.Types !== "undefined" && mongoose_2.Types.ObjectId) === "function" ? _a : Object)
], ProjectOwner.prototype, "userId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '用户姓名' }),
    __metadata("design:type", String)
], ProjectOwner.prototype, "name", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '邮箱地址' }),
    __metadata("design:type", String)
], ProjectOwner.prototype, "email", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '电话号码' }),
    __metadata("design:type", String)
], ProjectOwner.prototype, "phone", void 0);
exports.ProjectOwner = ProjectOwner = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], ProjectOwner);
let ProjectBudget = class ProjectBudget {
};
exports.ProjectBudget = ProjectBudget;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '最低预算' }),
    __metadata("design:type", Number)
], ProjectBudget.prototype, "min", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '最高预算' }),
    __metadata("design:type", Number)
], ProjectBudget.prototype, "max", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 'CAD' }),
    (0, swagger_1.ApiProperty)({ description: '货币单位', default: 'CAD' }),
    __metadata("design:type", String)
], ProjectBudget.prototype, "currency", void 0);
__decorate([
    (0, mongoose_1.Prop)({ enum: BudgetFlexibility, default: BudgetFlexibility.FLEXIBLE }),
    (0, swagger_1.ApiProperty)({ description: '预算灵活性', enum: BudgetFlexibility }),
    __metadata("design:type", String)
], ProjectBudget.prototype, "flexibility", void 0);
exports.ProjectBudget = ProjectBudget = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], ProjectBudget);
let ProjectTimeline = class ProjectTimeline {
};
exports.ProjectTimeline = ProjectTimeline;
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '期望开始日期' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], ProjectTimeline.prototype, "preferredStartDate", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '期望结束日期' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], ProjectTimeline.prototype, "expectedEndDate", void 0);
__decorate([
    (0, mongoose_1.Prop)({ enum: TimelineFlexibility, default: TimelineFlexibility.FLEXIBLE }),
    (0, swagger_1.ApiProperty)({ description: '时间灵活性', enum: TimelineFlexibility }),
    __metadata("design:type", String)
], ProjectTimeline.prototype, "flexibility", void 0);
__decorate([
    (0, mongoose_1.Prop)({ enum: Urgency, default: Urgency.MEDIUM }),
    (0, swagger_1.ApiProperty)({ description: '紧急程度', enum: Urgency }),
    __metadata("design:type", String)
], ProjectTimeline.prototype, "urgency", void 0);
exports.ProjectTimeline = ProjectTimeline = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], ProjectTimeline);
let Coordinates = class Coordinates {
};
exports.Coordinates = Coordinates;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '纬度' }),
    __metadata("design:type", Number)
], Coordinates.prototype, "lat", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '经度' }),
    __metadata("design:type", Number)
], Coordinates.prototype, "lng", void 0);
exports.Coordinates = Coordinates = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], Coordinates);
let ProjectLocation = class ProjectLocation {
};
exports.ProjectLocation = ProjectLocation;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '详细地址' }),
    __metadata("design:type", String)
], ProjectLocation.prototype, "address", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '城市' }),
    __metadata("design:type", String)
], ProjectLocation.prototype, "city", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '省份' }),
    __metadata("design:type", String)
], ProjectLocation.prototype, "province", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '邮政编码' }),
    __metadata("design:type", String)
], ProjectLocation.prototype, "postalCode", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Coordinates }),
    (0, swagger_1.ApiProperty)({ description: '地理坐标', type: Coordinates }),
    __metadata("design:type", Coordinates)
], ProjectLocation.prototype, "coordinates", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '进入说明' }),
    __metadata("design:type", String)
], ProjectLocation.prototype, "accessNotes", void 0);
exports.ProjectLocation = ProjectLocation = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], ProjectLocation);
let MaterialPreferences = class MaterialPreferences {
};
exports.MaterialPreferences = MaterialPreferences;
__decorate([
    (0, mongoose_1.Prop)([String]),
    (0, swagger_1.ApiProperty)({ description: '材料偏好', type: [String] }),
    __metadata("design:type", Array)
], MaterialPreferences.prototype, "preferences", void 0);
__decorate([
    (0, mongoose_1.Prop)([String]),
    (0, swagger_1.ApiProperty)({ description: '材料限制', type: [String] }),
    __metadata("design:type", Array)
], MaterialPreferences.prototype, "restrictions", void 0);
exports.MaterialPreferences = MaterialPreferences = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], MaterialPreferences);
let PermitInfo = class PermitInfo {
};
exports.PermitInfo = PermitInfo;
__decorate([
    (0, mongoose_1.Prop)({ default: false }),
    (0, swagger_1.ApiProperty)({ description: '是否需要许可证' }),
    __metadata("design:type", Boolean)
], PermitInfo.prototype, "required", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: false }),
    (0, swagger_1.ApiProperty)({ description: '是否已获得许可证' }),
    __metadata("design:type", Boolean)
], PermitInfo.prototype, "obtained", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '许可证备注' }),
    __metadata("design:type", String)
], PermitInfo.prototype, "notes", void 0);
exports.PermitInfo = PermitInfo = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], PermitInfo);
let ProjectRequirements = class ProjectRequirements {
};
exports.ProjectRequirements = ProjectRequirements;
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '面积(平方英尺)' }),
    __metadata("design:type", Number)
], ProjectRequirements.prototype, "area", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '房间数量' }),
    __metadata("design:type", Number)
], ProjectRequirements.prototype, "rooms", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '装修风格' }),
    __metadata("design:type", String)
], ProjectRequirements.prototype, "style", void 0);
__decorate([
    (0, mongoose_1.Prop)([String]),
    (0, swagger_1.ApiProperty)({ description: '特殊要求', type: [String] }),
    __metadata("design:type", Array)
], ProjectRequirements.prototype, "specialRequirements", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: MaterialPreferences }),
    (0, swagger_1.ApiProperty)({ description: '材料要求', type: MaterialPreferences }),
    __metadata("design:type", MaterialPreferences)
], ProjectRequirements.prototype, "materials", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: PermitInfo }),
    (0, swagger_1.ApiProperty)({ description: '许可证信息', type: PermitInfo }),
    __metadata("design:type", PermitInfo)
], ProjectRequirements.prototype, "permits", void 0);
exports.ProjectRequirements = ProjectRequirements = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], ProjectRequirements);
let ProjectAttachment = class ProjectAttachment {
};
exports.ProjectAttachment = ProjectAttachment;
__decorate([
    (0, mongoose_1.Prop)({ enum: AttachmentType, required: true }),
    (0, swagger_1.ApiProperty)({ description: '附件类型', enum: AttachmentType }),
    __metadata("design:type", String)
], ProjectAttachment.prototype, "type", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '文件URL' }),
    __metadata("design:type", String)
], ProjectAttachment.prototype, "url", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '文件名' }),
    __metadata("design:type", String)
], ProjectAttachment.prototype, "filename", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '文件描述' }),
    __metadata("design:type", String)
], ProjectAttachment.prototype, "description", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: Date.now }),
    (0, swagger_1.ApiProperty)({ description: '上传时间' }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], ProjectAttachment.prototype, "uploadedAt", void 0);
exports.ProjectAttachment = ProjectAttachment = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], ProjectAttachment);
let ProjectMatch = class ProjectMatch {
};
exports.ProjectMatch = ProjectMatch;
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Types.ObjectId, required: true, ref: 'ServiceProvider' }),
    (0, swagger_1.ApiProperty)({ description: '服务商ID' }),
    __metadata("design:type", typeof (_e = typeof mongoose_2.Types !== "undefined" && mongoose_2.Types.ObjectId) === "function" ? _e : Object)
], ProjectMatch.prototype, "providerId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '匹配度分数' }),
    __metadata("design:type", Number)
], ProjectMatch.prototype, "matchScore", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: Date.now }),
    (0, swagger_1.ApiProperty)({ description: '匹配时间' }),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], ProjectMatch.prototype, "matchedAt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ enum: MatchStatus, default: MatchStatus.MATCHED }),
    (0, swagger_1.ApiProperty)({ description: '匹配状态', enum: MatchStatus }),
    __metadata("design:type", String)
], ProjectMatch.prototype, "status", void 0);
exports.ProjectMatch = ProjectMatch = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], ProjectMatch);
let ProjectQuote = class ProjectQuote {
};
exports.ProjectQuote = ProjectQuote;
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Types.ObjectId, required: true, ref: 'Quote' }),
    (0, swagger_1.ApiProperty)({ description: '报价ID' }),
    __metadata("design:type", typeof (_g = typeof mongoose_2.Types !== "undefined" && mongoose_2.Types.ObjectId) === "function" ? _g : Object)
], ProjectQuote.prototype, "quoteId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Types.ObjectId, required: true, ref: 'ServiceProvider' }),
    (0, swagger_1.ApiProperty)({ description: '服务商ID' }),
    __metadata("design:type", typeof (_h = typeof mongoose_2.Types !== "undefined" && mongoose_2.Types.ObjectId) === "function" ? _h : Object)
], ProjectQuote.prototype, "providerId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ enum: QuoteStatus, default: QuoteStatus.PENDING }),
    (0, swagger_1.ApiProperty)({ description: '报价状态', enum: QuoteStatus }),
    __metadata("design:type", String)
], ProjectQuote.prototype, "status", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: Date.now }),
    (0, swagger_1.ApiProperty)({ description: '提交时间' }),
    __metadata("design:type", typeof (_j = typeof Date !== "undefined" && Date) === "function" ? _j : Object)
], ProjectQuote.prototype, "submittedAt", void 0);
exports.ProjectQuote = ProjectQuote = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], ProjectQuote);
let SelectedProvider = class SelectedProvider {
};
exports.SelectedProvider = SelectedProvider;
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Types.ObjectId, required: true, ref: 'ServiceProvider' }),
    (0, swagger_1.ApiProperty)({ description: '服务商ID' }),
    __metadata("design:type", typeof (_k = typeof mongoose_2.Types !== "undefined" && mongoose_2.Types.ObjectId) === "function" ? _k : Object)
], SelectedProvider.prototype, "providerId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Types.ObjectId, required: true, ref: 'Quote' }),
    (0, swagger_1.ApiProperty)({ description: '报价ID' }),
    __metadata("design:type", typeof (_l = typeof mongoose_2.Types !== "undefined" && mongoose_2.Types.ObjectId) === "function" ? _l : Object)
], SelectedProvider.prototype, "quoteId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: Date.now }),
    (0, swagger_1.ApiProperty)({ description: '选择时间' }),
    __metadata("design:type", typeof (_m = typeof Date !== "undefined" && Date) === "function" ? _m : Object)
], SelectedProvider.prototype, "selectedAt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Types.ObjectId, ref: 'Contract' }),
    (0, swagger_1.ApiProperty)({ description: '合同ID' }),
    __metadata("design:type", typeof (_o = typeof mongoose_2.Types !== "undefined" && mongoose_2.Types.ObjectId) === "function" ? _o : Object)
], SelectedProvider.prototype, "contractId", void 0);
exports.SelectedProvider = SelectedProvider = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], SelectedProvider);
let ProjectMilestone = class ProjectMilestone {
};
exports.ProjectMilestone = ProjectMilestone;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '里程碑名称' }),
    __metadata("design:type", String)
], ProjectMilestone.prototype, "name", void 0);
__decorate([
    (0, mongoose_1.Prop)({ enum: MilestoneStatus, default: MilestoneStatus.PENDING }),
    (0, swagger_1.ApiProperty)({ description: '里程碑状态', enum: MilestoneStatus }),
    __metadata("design:type", String)
], ProjectMilestone.prototype, "status", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '计划日期' }),
    __metadata("design:type", typeof (_p = typeof Date !== "undefined" && Date) === "function" ? _p : Object)
], ProjectMilestone.prototype, "scheduledDate", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '完成日期' }),
    __metadata("design:type", typeof (_q = typeof Date !== "undefined" && Date) === "function" ? _q : Object)
], ProjectMilestone.prototype, "completedDate", void 0);
exports.ProjectMilestone = ProjectMilestone = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], ProjectMilestone);
let ProjectProgress = class ProjectProgress {
};
exports.ProjectProgress = ProjectProgress;
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '当前阶段' }),
    __metadata("design:type", String)
], ProjectProgress.prototype, "currentPhase", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '完成百分比' }),
    __metadata("design:type", Number)
], ProjectProgress.prototype, "completionPercentage", void 0);
__decorate([
    (0, mongoose_1.Prop)([ProjectMilestone]),
    (0, swagger_1.ApiProperty)({ description: '里程碑列表', type: [ProjectMilestone] }),
    __metadata("design:type", Array)
], ProjectProgress.prototype, "milestones", void 0);
exports.ProjectProgress = ProjectProgress = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], ProjectProgress);
let Project = class Project {
};
exports.Project = Project;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '项目标题' }),
    __metadata("design:type", String)
], Project.prototype, "title", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '项目描述' }),
    __metadata("design:type", String)
], Project.prototype, "description", void 0);
__decorate([
    (0, mongoose_1.Prop)({ enum: ProjectCategory, required: true }),
    (0, swagger_1.ApiProperty)({ description: '项目类别', enum: ProjectCategory }),
    __metadata("design:type", String)
], Project.prototype, "category", void 0);
__decorate([
    (0, mongoose_1.Prop)({ enum: ProjectStatus, default: ProjectStatus.DRAFT }),
    (0, swagger_1.ApiProperty)({ description: '项目状态', enum: ProjectStatus }),
    __metadata("design:type", String)
], Project.prototype, "status", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: ProjectOwner, required: true }),
    (0, swagger_1.ApiProperty)({ description: '项目所有者', type: ProjectOwner }),
    __metadata("design:type", ProjectOwner)
], Project.prototype, "owner", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: ProjectBudget, required: true }),
    (0, swagger_1.ApiProperty)({ description: '预算信息', type: ProjectBudget }),
    __metadata("design:type", ProjectBudget)
], Project.prototype, "budget", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: ProjectTimeline }),
    (0, swagger_1.ApiProperty)({ description: '时间安排', type: ProjectTimeline }),
    __metadata("design:type", ProjectTimeline)
], Project.prototype, "timeline", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: ProjectLocation, required: true }),
    (0, swagger_1.ApiProperty)({ description: '位置信息', type: ProjectLocation }),
    __metadata("design:type", ProjectLocation)
], Project.prototype, "location", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: ProjectRequirements }),
    (0, swagger_1.ApiProperty)({ description: '项目要求', type: ProjectRequirements }),
    __metadata("design:type", ProjectRequirements)
], Project.prototype, "requirements", void 0);
__decorate([
    (0, mongoose_1.Prop)([ProjectAttachment]),
    (0, swagger_1.ApiProperty)({ description: '附件列表', type: [ProjectAttachment] }),
    __metadata("design:type", Array)
], Project.prototype, "attachments", void 0);
__decorate([
    (0, mongoose_1.Prop)([ProjectMatch]),
    (0, swagger_1.ApiProperty)({ description: '匹配的服务商', type: [ProjectMatch] }),
    __metadata("design:type", Array)
], Project.prototype, "matches", void 0);
__decorate([
    (0, mongoose_1.Prop)([ProjectQuote]),
    (0, swagger_1.ApiProperty)({ description: '收到的报价', type: [ProjectQuote] }),
    __metadata("design:type", Array)
], Project.prototype, "quotes", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: SelectedProvider }),
    (0, swagger_1.ApiProperty)({ description: '选中的服务商', type: SelectedProvider }),
    __metadata("design:type", SelectedProvider)
], Project.prototype, "selectedProvider", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: ProjectProgress }),
    (0, swagger_1.ApiProperty)({ description: '项目进度', type: ProjectProgress }),
    __metadata("design:type", ProjectProgress)
], Project.prototype, "progress", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '发布时间' }),
    __metadata("design:type", typeof (_r = typeof Date !== "undefined" && Date) === "function" ? _r : Object)
], Project.prototype, "publishedAt", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '完成时间' }),
    __metadata("design:type", typeof (_s = typeof Date !== "undefined" && Date) === "function" ? _s : Object)
], Project.prototype, "completedAt", void 0);
exports.Project = Project = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], Project);
exports.ProjectSchema = mongoose_1.SchemaFactory.createForClass(Project);
exports.ProjectSchema.index({ 'owner.userId': 1, status: 1 });
exports.ProjectSchema.index({ category: 1, status: 1, createdAt: -1 });
exports.ProjectSchema.index({ status: 1, publishedAt: -1 });
exports.ProjectSchema.index({ 'location.coordinates': '2dsphere' });
exports.ProjectSchema.index({ 'budget.min': 1, 'budget.max': 1 });
exports.ProjectSchema.index({ 'location.province': 1 });
exports.ProjectSchema.virtual('isActive').get(function () {
    return this.status === ProjectStatus.ACTIVE;
});
exports.ProjectSchema.virtual('isCompleted').get(function () {
    return this.status === ProjectStatus.COMPLETED;
});
exports.ProjectSchema.virtual('daysActive').get(function () {
    if (!this.publishedAt)
        return 0;
    const now = new Date();
    const published = new Date(this.publishedAt);
    return Math.floor((now.getTime() - published.getTime()) / (1000 * 60 * 60 * 24));
});
exports.ProjectSchema.set('toJSON', { virtuals: true });
exports.ProjectSchema.set('toObject', { virtuals: true });


/***/ }),
/* 42 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ProjectsController = void 0;
const common_1 = __webpack_require__(2);
const swagger_1 = __webpack_require__(3);
const throttler_1 = __webpack_require__(10);
const projects_service_1 = __webpack_require__(40);
const create_project_dto_1 = __webpack_require__(43);
const update_project_dto_1 = __webpack_require__(44);
const project_query_dto_1 = __webpack_require__(45);
const jwt_auth_guard_1 = __webpack_require__(34);
const roles_guard_1 = __webpack_require__(35);
const roles_decorator_1 = __webpack_require__(36);
const project_schema_1 = __webpack_require__(41);
const user_schema_1 = __webpack_require__(21);
let ProjectsController = class ProjectsController {
    constructor(projectsService) {
        this.projectsService = projectsService;
    }
    async create(createProjectDto, req) {
        return this.projectsService.create(createProjectDto, req.user);
    }
    async findAll(query) {
        return this.projectsService.findAll(query);
    }
    async findMyProjects(query, req) {
        return this.projectsService.findByUser(req.user._id.toString(), query);
    }
    async getStats() {
        return this.projectsService.getStats();
    }
    async getMyStats(req) {
        return this.projectsService.getStats(req.user._id.toString());
    }
    async findNearby(lat, lng, radius) {
        return this.projectsService.findNearby(lat, lng, radius);
    }
    async findOne(id) {
        return this.projectsService.findOne(id);
    }
    async update(id, updateProjectDto, req) {
        return this.projectsService.update(id, updateProjectDto, req.user);
    }
    async remove(id, req) {
        return this.projectsService.remove(id, req.user);
    }
    async publish(id, req) {
        return this.projectsService.publish(id, req.user);
    }
};
exports.ProjectsController = ProjectsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '创建项目' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '项目创建成功',
        type: project_schema_1.Project,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数错误',
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '未授权',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof create_project_dto_1.CreateProjectDto !== "undefined" && create_project_dto_1.CreateProjectDto) === "function" ? _b : Object, Object]),
    __metadata("design:returntype", typeof (_c = typeof Promise !== "undefined" && Promise) === "function" ? _c : Object)
], ProjectsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取项目列表' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取项目列表成功',
        type: [project_schema_1.Project],
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, description: '页码' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: '每页数量' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, description: '项目状态' }),
    (0, swagger_1.ApiQuery)({ name: 'category', required: false, description: '项目类别' }),
    (0, swagger_1.ApiQuery)({ name: 'province', required: false, description: '省份' }),
    (0, swagger_1.ApiQuery)({ name: 'city', required: false, description: '城市' }),
    (0, swagger_1.ApiQuery)({ name: 'minBudget', required: false, description: '最低预算' }),
    (0, swagger_1.ApiQuery)({ name: 'maxBudget', required: false, description: '最高预算' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: '搜索关键词' }),
    (0, swagger_1.ApiQuery)({ name: 'lat', required: false, description: '纬度' }),
    (0, swagger_1.ApiQuery)({ name: 'lng', required: false, description: '经度' }),
    (0, swagger_1.ApiQuery)({ name: 'radius', required: false, description: '搜索半径(公里)' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_d = typeof project_query_dto_1.ProjectQueryDto !== "undefined" && project_query_dto_1.ProjectQueryDto) === "function" ? _d : Object]),
    __metadata("design:returntype", typeof (_e = typeof Promise !== "undefined" && Promise) === "function" ? _e : Object)
], ProjectsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('me'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取我的项目列表' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取我的项目列表成功',
        type: [project_schema_1.Project],
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_f = typeof project_query_dto_1.ProjectQueryDto !== "undefined" && project_query_dto_1.ProjectQueryDto) === "function" ? _f : Object, Object]),
    __metadata("design:returntype", typeof (_g = typeof Promise !== "undefined" && Promise) === "function" ? _g : Object)
], ProjectsController.prototype, "findMyProjects", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(user_schema_1.UserType.ADMIN),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取项目统计信息（管理员）' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取统计信息成功',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_h = typeof Promise !== "undefined" && Promise) === "function" ? _h : Object)
], ProjectsController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)('me/stats'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取我的项目统计信息' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取统计信息成功',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_j = typeof Promise !== "undefined" && Promise) === "function" ? _j : Object)
], ProjectsController.prototype, "getMyStats", null);
__decorate([
    (0, common_1.Get)('nearby'),
    (0, swagger_1.ApiOperation)({ summary: '获取附近的项目' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取附近项目成功',
        type: [project_schema_1.Project],
    }),
    (0, swagger_1.ApiQuery)({ name: 'lat', required: true, description: '纬度' }),
    (0, swagger_1.ApiQuery)({ name: 'lng', required: true, description: '经度' }),
    (0, swagger_1.ApiQuery)({ name: 'radius', required: false, description: '搜索半径(公里)' }),
    __param(0, (0, common_1.Query)('lat')),
    __param(1, (0, common_1.Query)('lng')),
    __param(2, (0, common_1.Query)('radius')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Number]),
    __metadata("design:returntype", typeof (_k = typeof Promise !== "undefined" && Promise) === "function" ? _k : Object)
], ProjectsController.prototype, "findNearby", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取项目详情' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取项目详情成功',
        type: project_schema_1.Project,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '项目不存在',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '项目ID' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_l = typeof Promise !== "undefined" && Promise) === "function" ? _l : Object)
], ProjectsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '更新项目' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '项目更新成功',
        type: project_schema_1.Project,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数错误',
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '未授权',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: '无权限',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '项目不存在',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '项目ID' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, typeof (_m = typeof update_project_dto_1.UpdateProjectDto !== "undefined" && update_project_dto_1.UpdateProjectDto) === "function" ? _m : Object, Object]),
    __metadata("design:returntype", typeof (_o = typeof Promise !== "undefined" && Promise) === "function" ? _o : Object)
], ProjectsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: '删除项目' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: '项目删除成功',
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数错误',
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '未授权',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: '无权限',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '项目不存在',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '项目ID' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_p = typeof Promise !== "undefined" && Promise) === "function" ? _p : Object)
], ProjectsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/publish'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '发布项目' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '项目发布成功',
        type: project_schema_1.Project,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数错误',
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '未授权',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: '无权限',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '项目不存在',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '项目ID' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_q = typeof Promise !== "undefined" && Promise) === "function" ? _q : Object)
], ProjectsController.prototype, "publish", null);
exports.ProjectsController = ProjectsController = __decorate([
    (0, swagger_1.ApiTags)('Projects'),
    (0, common_1.Controller)('projects'),
    (0, common_1.UseGuards)(throttler_1.ThrottlerGuard),
    __metadata("design:paramtypes", [typeof (_a = typeof projects_service_1.ProjectsService !== "undefined" && projects_service_1.ProjectsService) === "function" ? _a : Object])
], ProjectsController);


/***/ }),
/* 43 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CreateProjectDto = exports.CreateProjectRequirementsDto = exports.CreatePermitInfoDto = exports.CreateMaterialPreferencesDto = exports.CreateProjectTimelineDto = exports.CreateProjectBudgetDto = exports.CreateProjectLocationDto = exports.CreateCoordinatesDto = void 0;
const class_validator_1 = __webpack_require__(24);
const class_transformer_1 = __webpack_require__(27);
const swagger_1 = __webpack_require__(3);
const project_schema_1 = __webpack_require__(41);
class CreateCoordinatesDto {
}
exports.CreateCoordinatesDto = CreateCoordinatesDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '纬度' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateCoordinatesDto.prototype, "lat", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '经度' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateCoordinatesDto.prototype, "lng", void 0);
class CreateProjectLocationDto {
}
exports.CreateProjectLocationDto = CreateProjectLocationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '详细地址' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1),
    (0, class_validator_1.MaxLength)(200),
    __metadata("design:type", String)
], CreateProjectLocationDto.prototype, "address", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '城市' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateProjectLocationDto.prototype, "city", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '省份' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateProjectLocationDto.prototype, "province", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邮政编码' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1),
    (0, class_validator_1.MaxLength)(10),
    __metadata("design:type", String)
], CreateProjectLocationDto.prototype, "postalCode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '地理坐标', type: CreateCoordinatesDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => CreateCoordinatesDto),
    __metadata("design:type", CreateCoordinatesDto)
], CreateProjectLocationDto.prototype, "coordinates", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '进入说明' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], CreateProjectLocationDto.prototype, "accessNotes", void 0);
class CreateProjectBudgetDto {
    constructor() {
        this.currency = 'CAD';
        this.flexibility = project_schema_1.BudgetFlexibility.FLEXIBLE;
    }
}
exports.CreateProjectBudgetDto = CreateProjectBudgetDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最低预算' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateProjectBudgetDto.prototype, "min", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最高预算' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateProjectBudgetDto.prototype, "max", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '货币单位', default: 'CAD' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProjectBudgetDto.prototype, "currency", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '预算灵活性', enum: project_schema_1.BudgetFlexibility }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(project_schema_1.BudgetFlexibility),
    __metadata("design:type", typeof (_a = typeof project_schema_1.BudgetFlexibility !== "undefined" && project_schema_1.BudgetFlexibility) === "function" ? _a : Object)
], CreateProjectBudgetDto.prototype, "flexibility", void 0);
class CreateProjectTimelineDto {
    constructor() {
        this.flexibility = project_schema_1.TimelineFlexibility.FLEXIBLE;
        this.urgency = project_schema_1.Urgency.MEDIUM;
    }
}
exports.CreateProjectTimelineDto = CreateProjectTimelineDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '期望开始日期' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateProjectTimelineDto.prototype, "preferredStartDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '期望结束日期' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateProjectTimelineDto.prototype, "expectedEndDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '时间灵活性', enum: project_schema_1.TimelineFlexibility }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(project_schema_1.TimelineFlexibility),
    __metadata("design:type", typeof (_b = typeof project_schema_1.TimelineFlexibility !== "undefined" && project_schema_1.TimelineFlexibility) === "function" ? _b : Object)
], CreateProjectTimelineDto.prototype, "flexibility", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '紧急程度', enum: project_schema_1.Urgency }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(project_schema_1.Urgency),
    __metadata("design:type", typeof (_c = typeof project_schema_1.Urgency !== "undefined" && project_schema_1.Urgency) === "function" ? _c : Object)
], CreateProjectTimelineDto.prototype, "urgency", void 0);
class CreateMaterialPreferencesDto {
}
exports.CreateMaterialPreferencesDto = CreateMaterialPreferencesDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '材料偏好', type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateMaterialPreferencesDto.prototype, "preferences", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '材料限制', type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateMaterialPreferencesDto.prototype, "restrictions", void 0);
class CreatePermitInfoDto {
    constructor() {
        this.required = false;
        this.obtained = false;
    }
}
exports.CreatePermitInfoDto = CreatePermitInfoDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '是否需要许可证', default: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreatePermitInfoDto.prototype, "required", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '是否已获得许可证', default: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreatePermitInfoDto.prototype, "obtained", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '许可证备注' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], CreatePermitInfoDto.prototype, "notes", void 0);
class CreateProjectRequirementsDto {
}
exports.CreateProjectRequirementsDto = CreateProjectRequirementsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '面积(平方英尺)' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateProjectRequirementsDto.prototype, "area", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '房间数量' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(50),
    __metadata("design:type", Number)
], CreateProjectRequirementsDto.prototype, "rooms", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '装修风格' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateProjectRequirementsDto.prototype, "style", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '特殊要求', type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateProjectRequirementsDto.prototype, "specialRequirements", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '材料要求', type: CreateMaterialPreferencesDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => CreateMaterialPreferencesDto),
    __metadata("design:type", CreateMaterialPreferencesDto)
], CreateProjectRequirementsDto.prototype, "materials", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '许可证信息', type: CreatePermitInfoDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => CreatePermitInfoDto),
    __metadata("design:type", CreatePermitInfoDto)
], CreateProjectRequirementsDto.prototype, "permits", void 0);
class CreateProjectDto {
}
exports.CreateProjectDto = CreateProjectDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '项目标题' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1),
    (0, class_validator_1.MaxLength)(200),
    __metadata("design:type", String)
], CreateProjectDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '项目描述' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1),
    (0, class_validator_1.MaxLength)(2000),
    __metadata("design:type", String)
], CreateProjectDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '项目类别', enum: project_schema_1.ProjectCategory }),
    (0, class_validator_1.IsEnum)(project_schema_1.ProjectCategory),
    __metadata("design:type", typeof (_d = typeof project_schema_1.ProjectCategory !== "undefined" && project_schema_1.ProjectCategory) === "function" ? _d : Object)
], CreateProjectDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '预算信息', type: CreateProjectBudgetDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => CreateProjectBudgetDto),
    __metadata("design:type", CreateProjectBudgetDto)
], CreateProjectDto.prototype, "budget", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '位置信息', type: CreateProjectLocationDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => CreateProjectLocationDto),
    __metadata("design:type", CreateProjectLocationDto)
], CreateProjectDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '时间安排', type: CreateProjectTimelineDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => CreateProjectTimelineDto),
    __metadata("design:type", CreateProjectTimelineDto)
], CreateProjectDto.prototype, "timeline", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '项目要求', type: CreateProjectRequirementsDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => CreateProjectRequirementsDto),
    __metadata("design:type", CreateProjectRequirementsDto)
], CreateProjectDto.prototype, "requirements", void 0);


/***/ }),
/* 44 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UpdateProjectDto = void 0;
const swagger_1 = __webpack_require__(3);
const create_project_dto_1 = __webpack_require__(43);
class UpdateProjectDto extends (0, swagger_1.PartialType)(create_project_dto_1.CreateProjectDto) {
}
exports.UpdateProjectDto = UpdateProjectDto;


/***/ }),
/* 45 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ProjectQueryDto = void 0;
const class_validator_1 = __webpack_require__(24);
const class_transformer_1 = __webpack_require__(27);
const swagger_1 = __webpack_require__(3);
const pagination_dto_1 = __webpack_require__(33);
const project_schema_1 = __webpack_require__(41);
class ProjectQueryDto extends pagination_dto_1.PaginationDto {
    constructor() {
        super(...arguments);
        this.sortBy = 'createdAt';
        this.sortOrder = 'desc';
        this.radius = 50;
    }
}
exports.ProjectQueryDto = ProjectQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '项目状态', enum: project_schema_1.ProjectStatus }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(project_schema_1.ProjectStatus),
    __metadata("design:type", typeof (_a = typeof project_schema_1.ProjectStatus !== "undefined" && project_schema_1.ProjectStatus) === "function" ? _a : Object)
], ProjectQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '项目类别', enum: project_schema_1.ProjectCategory }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(project_schema_1.ProjectCategory),
    __metadata("design:type", typeof (_b = typeof project_schema_1.ProjectCategory !== "undefined" && project_schema_1.ProjectCategory) === "function" ? _b : Object)
], ProjectQueryDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '省份' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ProjectQueryDto.prototype, "province", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '城市' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ProjectQueryDto.prototype, "city", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '最低预算' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], ProjectQueryDto.prototype, "minBudget", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '最高预算' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], ProjectQueryDto.prototype, "maxBudget", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '搜索关键词' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ProjectQueryDto.prototype, "search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '排序字段', enum: ['createdAt', 'publishedAt', 'budget.min', 'budget.max'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ProjectQueryDto.prototype, "sortBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '排序方向', enum: ['asc', 'desc'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ProjectQueryDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '纬度（用于地理位置搜索）' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ProjectQueryDto.prototype, "lat", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '经度（用于地理位置搜索）' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ProjectQueryDto.prototype, "lng", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '搜索半径（公里）', minimum: 1, maximum: 500 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(500),
    __metadata("design:type", Number)
], ProjectQueryDto.prototype, "radius", void 0);


/***/ }),
/* 46 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ServiceProvidersModule = void 0;
const common_1 = __webpack_require__(2);
const mongoose_1 = __webpack_require__(8);
const service_providers_service_1 = __webpack_require__(47);
const service_providers_controller_1 = __webpack_require__(49);
const service_provider_schema_1 = __webpack_require__(48);
let ServiceProvidersModule = class ServiceProvidersModule {
};
exports.ServiceProvidersModule = ServiceProvidersModule;
exports.ServiceProvidersModule = ServiceProvidersModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: service_provider_schema_1.ServiceProvider.name, schema: service_provider_schema_1.ServiceProviderSchema },
            ]),
        ],
        controllers: [service_providers_controller_1.ServiceProvidersController],
        providers: [service_providers_service_1.ServiceProvidersService],
        exports: [service_providers_service_1.ServiceProvidersService],
    })
], ServiceProvidersModule);


/***/ }),
/* 47 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ServiceProvidersService = void 0;
const common_1 = __webpack_require__(2);
const mongoose_1 = __webpack_require__(8);
const mongoose_2 = __webpack_require__(19);
const service_provider_schema_1 = __webpack_require__(48);
const user_schema_1 = __webpack_require__(21);
let ServiceProvidersService = class ServiceProvidersService {
    constructor(serviceProviderModel) {
        this.serviceProviderModel = serviceProviderModel;
    }
    async create(createServiceProviderDto, user) {
        const existingProvider = await this.serviceProviderModel.findOne({
            userId: user._id,
        });
        if (existingProvider) {
            throw new common_1.ConflictException('User is already registered as a service provider');
        }
        if (!createServiceProviderDto.serviceAreas.length) {
            throw new common_1.BadRequestException('At least one service area is required');
        }
        if (!createServiceProviderDto.services.length) {
            throw new common_1.BadRequestException('At least one service is required');
        }
        const serviceProviderData = {
            ...createServiceProviderDto,
            userId: user._id,
            status: service_provider_schema_1.ServiceProviderStatus.PENDING,
            rating: {
                overall: 0,
                quality: 0,
                timeliness: 0,
                communication: 0,
                value: 0,
                totalReviews: 0,
                totalRating: 0,
            },
            stats: {
                projectsCompleted: 0,
                responseTime: 0,
                onTimeRate: 0,
                repeatCustomerRate: 0,
            },
        };
        const createdProvider = new this.serviceProviderModel(serviceProviderData);
        return createdProvider.save();
    }
    async findAll(query) {
        const { page = 1, limit = 20, status, category, province, city, minRating, search, sortBy = 'createdAt', sortOrder = 'desc', lat, lng, radius = 50, } = query;
        const filter = {};
        if (status) {
            filter.status = status;
        }
        else {
            filter.status = service_provider_schema_1.ServiceProviderStatus.APPROVED;
        }
        if (category) {
            filter['services.category'] = category;
        }
        if (province) {
            filter['serviceAreas.province'] = new RegExp(province, 'i');
        }
        if (city) {
            filter['serviceAreas.cities'] = new RegExp(city, 'i');
        }
        if (minRating !== undefined) {
            filter['rating.overall'] = { $gte: minRating };
        }
        if (search) {
            filter.$or = [
                { 'businessInfo.companyName': new RegExp(search, 'i') },
                { 'businessInfo.description': new RegExp(search, 'i') },
                { 'services.name': new RegExp(search, 'i') },
                { 'services.description': new RegExp(search, 'i') },
            ];
        }
        if (lat && lng && province) {
        }
        const sort = {};
        sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
        const skip = (page - 1) * limit;
        const [items, total] = await Promise.all([
            this.serviceProviderModel
                .find(filter)
                .sort(sort)
                .skip(skip)
                .limit(limit)
                .populate('userId', 'profile.firstName profile.lastName profile.avatar email')
                .exec(),
            this.serviceProviderModel.countDocuments(filter),
        ]);
        return {
            items,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async findOne(id) {
        if (!mongoose_2.Types.ObjectId.isValid(id)) {
            throw new common_1.BadRequestException('Invalid service provider ID');
        }
        const serviceProvider = await this.serviceProviderModel
            .findById(id)
            .populate('userId', 'profile.firstName profile.lastName profile.avatar email profile.phone')
            .populate('reviewedBy', 'profile.firstName profile.lastName')
            .exec();
        if (!serviceProvider) {
            throw new common_1.NotFoundException('Service provider not found');
        }
        return serviceProvider;
    }
    async findByUserId(userId) {
        if (!mongoose_2.Types.ObjectId.isValid(userId)) {
            throw new common_1.BadRequestException('Invalid user ID');
        }
        const serviceProvider = await this.serviceProviderModel
            .findOne({ userId })
            .populate('userId', 'profile.firstName profile.lastName profile.avatar email profile.phone')
            .exec();
        if (!serviceProvider) {
            throw new common_1.NotFoundException('Service provider not found');
        }
        return serviceProvider;
    }
    async update(id, updateServiceProviderDto, user) {
        if (!mongoose_2.Types.ObjectId.isValid(id)) {
            throw new common_1.BadRequestException('Invalid service provider ID');
        }
        const serviceProvider = await this.serviceProviderModel.findById(id);
        if (!serviceProvider) {
            throw new common_1.NotFoundException('Service provider not found');
        }
        if (serviceProvider.userId.toString() !== user._id.toString()) {
            throw new common_1.ForbiddenException('You can only update your own service provider profile');
        }
        if (serviceProvider.status === service_provider_schema_1.ServiceProviderStatus.APPROVED) {
            const sensitiveFields = ['businessInfo', 'services', 'certifications', 'insurance'];
            const hasSensitiveChanges = sensitiveFields.some(field => updateServiceProviderDto[field] !== undefined);
            if (hasSensitiveChanges) {
                updateServiceProviderDto['status'] = service_provider_schema_1.ServiceProviderStatus.PENDING;
            }
        }
        const updatedProvider = await this.serviceProviderModel
            .findByIdAndUpdate(id, updateServiceProviderDto, { new: true })
            .populate('userId', 'profile.firstName profile.lastName profile.avatar email')
            .exec();
        return updatedProvider;
    }
    async remove(id, user) {
        if (!mongoose_2.Types.ObjectId.isValid(id)) {
            throw new common_1.BadRequestException('Invalid service provider ID');
        }
        const serviceProvider = await this.serviceProviderModel.findById(id);
        if (!serviceProvider) {
            throw new common_1.NotFoundException('Service provider not found');
        }
        if (serviceProvider.userId.toString() !== user._id.toString()) {
            throw new common_1.ForbiddenException('You can only delete your own service provider profile');
        }
        if (serviceProvider.status === service_provider_schema_1.ServiceProviderStatus.APPROVED) {
        }
        await this.serviceProviderModel.findByIdAndDelete(id);
    }
    async review(id, status, reviewNotes, adminUser) {
        if (!mongoose_2.Types.ObjectId.isValid(id)) {
            throw new common_1.BadRequestException('Invalid service provider ID');
        }
        if (adminUser.userType !== user_schema_1.UserType.ADMIN) {
            throw new common_1.ForbiddenException('Only administrators can review service provider applications');
        }
        const serviceProvider = await this.serviceProviderModel.findById(id);
        if (!serviceProvider) {
            throw new common_1.NotFoundException('Service provider not found');
        }
        if (serviceProvider.status !== service_provider_schema_1.ServiceProviderStatus.PENDING) {
            throw new common_1.BadRequestException('Only pending applications can be reviewed');
        }
        const updatedProvider = await this.serviceProviderModel
            .findByIdAndUpdate(id, {
            status,
            reviewNotes,
            reviewedBy: adminUser._id,
            reviewedAt: new Date(),
        }, { new: true })
            .populate('userId', 'profile.firstName profile.lastName profile.avatar email')
            .populate('reviewedBy', 'profile.firstName profile.lastName')
            .exec();
        return updatedProvider;
    }
    async getStats() {
        const stats = await this.serviceProviderModel.aggregate([
            {
                $group: {
                    _id: null,
                    total: { $sum: 1 },
                    pending: { $sum: { $cond: [{ $eq: ['$status', service_provider_schema_1.ServiceProviderStatus.PENDING] }, 1, 0] } },
                    approved: { $sum: { $cond: [{ $eq: ['$status', service_provider_schema_1.ServiceProviderStatus.APPROVED] }, 1, 0] } },
                    suspended: { $sum: { $cond: [{ $eq: ['$status', service_provider_schema_1.ServiceProviderStatus.SUSPENDED] }, 1, 0] } },
                    rejected: { $sum: { $cond: [{ $eq: ['$status', service_provider_schema_1.ServiceProviderStatus.REJECTED] }, 1, 0] } },
                    avgRating: { $avg: '$rating.overall' },
                    totalProjects: { $sum: '$stats.projectsCompleted' },
                },
            },
        ]);
        return stats[0] || {
            total: 0,
            pending: 0,
            approved: 0,
            suspended: 0,
            rejected: 0,
            avgRating: 0,
            totalProjects: 0,
        };
    }
    async matchProvidersForProject(projectCategory, projectLocation, projectBudget, limit = 10) {
        const filter = {
            status: service_provider_schema_1.ServiceProviderStatus.APPROVED,
            'services.category': projectCategory,
            'serviceAreas.province': projectLocation.province,
            'serviceAreas.cities': projectLocation.city,
        };
        return this.serviceProviderModel
            .find(filter)
            .sort({ 'rating.overall': -1, 'stats.projectsCompleted': -1 })
            .limit(limit)
            .populate('userId', 'profile.firstName profile.lastName profile.avatar')
            .exec();
    }
};
exports.ServiceProvidersService = ServiceProvidersService;
exports.ServiceProvidersService = ServiceProvidersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(service_provider_schema_1.ServiceProvider.name)),
    __metadata("design:paramtypes", [typeof (_a = typeof mongoose_2.Model !== "undefined" && mongoose_2.Model) === "function" ? _a : Object])
], ServiceProvidersService);


/***/ }),
/* 48 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ServiceProviderSchema = exports.ServiceProvider = exports.BusinessStats = exports.Rating = exports.Portfolio = exports.Insurance = exports.Certification = exports.ServiceOffered = exports.ServiceArea = exports.BusinessInfo = exports.ServiceCategory = exports.BusinessType = exports.ServiceProviderStatus = void 0;
const mongoose_1 = __webpack_require__(8);
const mongoose_2 = __webpack_require__(19);
const swagger_1 = __webpack_require__(3);
var ServiceProviderStatus;
(function (ServiceProviderStatus) {
    ServiceProviderStatus["PENDING"] = "pending";
    ServiceProviderStatus["APPROVED"] = "approved";
    ServiceProviderStatus["SUSPENDED"] = "suspended";
    ServiceProviderStatus["REJECTED"] = "rejected";
})(ServiceProviderStatus || (exports.ServiceProviderStatus = ServiceProviderStatus = {}));
var BusinessType;
(function (BusinessType) {
    BusinessType["INDIVIDUAL"] = "individual";
    BusinessType["COMPANY"] = "company";
})(BusinessType || (exports.BusinessType = BusinessType = {}));
var ServiceCategory;
(function (ServiceCategory) {
    ServiceCategory["FULL_RENOVATION"] = "full_renovation";
    ServiceCategory["KITCHEN"] = "kitchen";
    ServiceCategory["BATHROOM"] = "bathroom";
    ServiceCategory["FLOORING"] = "flooring";
    ServiceCategory["PAINTING"] = "painting";
    ServiceCategory["ELECTRICAL"] = "electrical";
    ServiceCategory["PLUMBING"] = "plumbing";
    ServiceCategory["ROOFING"] = "roofing";
    ServiceCategory["LANDSCAPING"] = "landscaping";
    ServiceCategory["HVAC"] = "hvac";
    ServiceCategory["CARPENTRY"] = "carpentry";
    ServiceCategory["TILING"] = "tiling";
    ServiceCategory["DRYWALL"] = "drywall";
    ServiceCategory["INSULATION"] = "insulation";
    ServiceCategory["WINDOWS_DOORS"] = "windows_doors";
    ServiceCategory["OTHER"] = "other";
})(ServiceCategory || (exports.ServiceCategory = ServiceCategory = {}));
let BusinessInfo = class BusinessInfo {
};
exports.BusinessInfo = BusinessInfo;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '公司名称' }),
    __metadata("design:type", String)
], BusinessInfo.prototype, "companyName", void 0);
__decorate([
    (0, mongoose_1.Prop)({ enum: BusinessType, required: true }),
    (0, swagger_1.ApiProperty)({ description: '业务类型', enum: BusinessType }),
    __metadata("design:type", String)
], BusinessInfo.prototype, "businessType", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '营业执照号' }),
    __metadata("design:type", String)
], BusinessInfo.prototype, "licenseNumber", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '税号' }),
    __metadata("design:type", String)
], BusinessInfo.prototype, "taxNumber", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '成立年份' }),
    __metadata("design:type", Number)
], BusinessInfo.prototype, "establishedYear", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '员工数量' }),
    __metadata("design:type", Number)
], BusinessInfo.prototype, "employeeCount", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '公司描述' }),
    __metadata("design:type", String)
], BusinessInfo.prototype, "description", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '公司网站' }),
    __metadata("design:type", String)
], BusinessInfo.prototype, "website", void 0);
__decorate([
    (0, mongoose_1.Prop)([String]),
    (0, swagger_1.ApiProperty)({ description: '营业执照图片', type: [String] }),
    __metadata("design:type", Array)
], BusinessInfo.prototype, "licenseImages", void 0);
exports.BusinessInfo = BusinessInfo = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], BusinessInfo);
let ServiceArea = class ServiceArea {
};
exports.ServiceArea = ServiceArea;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '省份' }),
    __metadata("design:type", String)
], ServiceArea.prototype, "province", void 0);
__decorate([
    (0, mongoose_1.Prop)([String]),
    (0, swagger_1.ApiProperty)({ description: '服务城市', type: [String] }),
    __metadata("design:type", Array)
], ServiceArea.prototype, "cities", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '服务半径(公里)' }),
    __metadata("design:type", Number)
], ServiceArea.prototype, "radius", void 0);
exports.ServiceArea = ServiceArea = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], ServiceArea);
let ServiceOffered = class ServiceOffered {
};
exports.ServiceOffered = ServiceOffered;
__decorate([
    (0, mongoose_1.Prop)({ enum: ServiceCategory, required: true }),
    (0, swagger_1.ApiProperty)({ description: '服务类别', enum: ServiceCategory }),
    __metadata("design:type", String)
], ServiceOffered.prototype, "category", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '服务名称' }),
    __metadata("design:type", String)
], ServiceOffered.prototype, "name", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '服务描述' }),
    __metadata("design:type", String)
], ServiceOffered.prototype, "description", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '起始价格' }),
    __metadata("design:type", Number)
], ServiceOffered.prototype, "startingPrice", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '价格单位' }),
    __metadata("design:type", String)
], ServiceOffered.prototype, "priceUnit", void 0);
__decorate([
    (0, mongoose_1.Prop)([String]),
    (0, swagger_1.ApiProperty)({ description: '服务图片', type: [String] }),
    __metadata("design:type", Array)
], ServiceOffered.prototype, "images", void 0);
exports.ServiceOffered = ServiceOffered = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], ServiceOffered);
let Certification = class Certification {
};
exports.Certification = Certification;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '认证名称' }),
    __metadata("design:type", String)
], Certification.prototype, "name", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '颁发机构' }),
    __metadata("design:type", String)
], Certification.prototype, "issuingOrganization", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '认证编号' }),
    __metadata("design:type", String)
], Certification.prototype, "certificationNumber", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '获得日期' }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], Certification.prototype, "issuedDate", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '过期日期' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], Certification.prototype, "expiryDate", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '认证文件URL' }),
    __metadata("design:type", String)
], Certification.prototype, "documentUrl", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: false }),
    (0, swagger_1.ApiProperty)({ description: '是否已验证' }),
    __metadata("design:type", Boolean)
], Certification.prototype, "verified", void 0);
exports.Certification = Certification = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], Certification);
let Insurance = class Insurance {
};
exports.Insurance = Insurance;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '保险类型' }),
    __metadata("design:type", String)
], Insurance.prototype, "type", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '保险公司' }),
    __metadata("design:type", String)
], Insurance.prototype, "provider", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '保单号' }),
    __metadata("design:type", String)
], Insurance.prototype, "policyNumber", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '保险金额' }),
    __metadata("design:type", Number)
], Insurance.prototype, "coverageAmount", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '生效日期' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], Insurance.prototype, "effectiveDate", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '过期日期' }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], Insurance.prototype, "expiryDate", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '保单文件URL' }),
    __metadata("design:type", String)
], Insurance.prototype, "documentUrl", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: false }),
    (0, swagger_1.ApiProperty)({ description: '是否已验证' }),
    __metadata("design:type", Boolean)
], Insurance.prototype, "verified", void 0);
exports.Insurance = Insurance = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], Insurance);
let Portfolio = class Portfolio {
};
exports.Portfolio = Portfolio;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    (0, swagger_1.ApiProperty)({ description: '项目标题' }),
    __metadata("design:type", String)
], Portfolio.prototype, "title", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '项目描述' }),
    __metadata("design:type", String)
], Portfolio.prototype, "description", void 0);
__decorate([
    (0, mongoose_1.Prop)({ enum: ServiceCategory }),
    (0, swagger_1.ApiProperty)({ description: '项目类别', enum: ServiceCategory }),
    __metadata("design:type", String)
], Portfolio.prototype, "category", void 0);
__decorate([
    (0, mongoose_1.Prop)([String]),
    (0, swagger_1.ApiProperty)({ description: '项目图片', type: [String] }),
    __metadata("design:type", Array)
], Portfolio.prototype, "images", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '项目成本' }),
    __metadata("design:type", Number)
], Portfolio.prototype, "cost", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '项目时长(天)' }),
    __metadata("design:type", Number)
], Portfolio.prototype, "duration", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '完成日期' }),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], Portfolio.prototype, "completedDate", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '客户评价' }),
    __metadata("design:type", String)
], Portfolio.prototype, "clientTestimonial", void 0);
exports.Portfolio = Portfolio = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], Portfolio);
let Rating = class Rating {
};
exports.Rating = Rating;
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '总评分' }),
    __metadata("design:type", Number)
], Rating.prototype, "overall", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '质量评分' }),
    __metadata("design:type", Number)
], Rating.prototype, "quality", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '时效评分' }),
    __metadata("design:type", Number)
], Rating.prototype, "timeliness", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '沟通评分' }),
    __metadata("design:type", Number)
], Rating.prototype, "communication", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '价值评分' }),
    __metadata("design:type", Number)
], Rating.prototype, "value", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '评价总数' }),
    __metadata("design:type", Number)
], Rating.prototype, "totalReviews", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '总评分数' }),
    __metadata("design:type", Number)
], Rating.prototype, "totalRating", void 0);
exports.Rating = Rating = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], Rating);
let BusinessStats = class BusinessStats {
};
exports.BusinessStats = BusinessStats;
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '完成项目数' }),
    __metadata("design:type", Number)
], BusinessStats.prototype, "projectsCompleted", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '平均响应时间(小时)' }),
    __metadata("design:type", Number)
], BusinessStats.prototype, "responseTime", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '按时完成率' }),
    __metadata("design:type", Number)
], BusinessStats.prototype, "onTimeRate", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '回头客率' }),
    __metadata("design:type", Number)
], BusinessStats.prototype, "repeatCustomerRate", void 0);
exports.BusinessStats = BusinessStats = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], BusinessStats);
let ServiceProvider = class ServiceProvider {
};
exports.ServiceProvider = ServiceProvider;
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Types.ObjectId, required: true, ref: 'User' }),
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", typeof (_f = typeof mongoose_2.Types !== "undefined" && mongoose_2.Types.ObjectId) === "function" ? _f : Object)
], ServiceProvider.prototype, "userId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ enum: ServiceProviderStatus, default: ServiceProviderStatus.PENDING }),
    (0, swagger_1.ApiProperty)({ description: '服务商状态', enum: ServiceProviderStatus }),
    __metadata("design:type", String)
], ServiceProvider.prototype, "status", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: BusinessInfo, required: true }),
    (0, swagger_1.ApiProperty)({ description: '业务信息', type: BusinessInfo }),
    __metadata("design:type", BusinessInfo)
], ServiceProvider.prototype, "businessInfo", void 0);
__decorate([
    (0, mongoose_1.Prop)([ServiceArea]),
    (0, swagger_1.ApiProperty)({ description: '服务区域', type: [ServiceArea] }),
    __metadata("design:type", Array)
], ServiceProvider.prototype, "serviceAreas", void 0);
__decorate([
    (0, mongoose_1.Prop)([ServiceOffered]),
    (0, swagger_1.ApiProperty)({ description: '提供的服务', type: [ServiceOffered] }),
    __metadata("design:type", Array)
], ServiceProvider.prototype, "services", void 0);
__decorate([
    (0, mongoose_1.Prop)([Certification]),
    (0, swagger_1.ApiProperty)({ description: '认证信息', type: [Certification] }),
    __metadata("design:type", Array)
], ServiceProvider.prototype, "certifications", void 0);
__decorate([
    (0, mongoose_1.Prop)([Insurance]),
    (0, swagger_1.ApiProperty)({ description: '保险信息', type: [Insurance] }),
    __metadata("design:type", Array)
], ServiceProvider.prototype, "insurance", void 0);
__decorate([
    (0, mongoose_1.Prop)([Portfolio]),
    (0, swagger_1.ApiProperty)({ description: '作品集', type: [Portfolio] }),
    __metadata("design:type", Array)
], ServiceProvider.prototype, "portfolio", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Rating }),
    (0, swagger_1.ApiProperty)({ description: '评分信息', type: Rating }),
    __metadata("design:type", Rating)
], ServiceProvider.prototype, "rating", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: BusinessStats }),
    (0, swagger_1.ApiProperty)({ description: '业务统计', type: BusinessStats }),
    __metadata("design:type", BusinessStats)
], ServiceProvider.prototype, "stats", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '审核时间' }),
    __metadata("design:type", typeof (_g = typeof Date !== "undefined" && Date) === "function" ? _g : Object)
], ServiceProvider.prototype, "reviewedAt", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    (0, swagger_1.ApiProperty)({ description: '审核备注' }),
    __metadata("design:type", String)
], ServiceProvider.prototype, "reviewNotes", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Types.ObjectId, ref: 'User' }),
    (0, swagger_1.ApiProperty)({ description: '审核人ID' }),
    __metadata("design:type", typeof (_h = typeof mongoose_2.Types !== "undefined" && mongoose_2.Types.ObjectId) === "function" ? _h : Object)
], ServiceProvider.prototype, "reviewedBy", void 0);
exports.ServiceProvider = ServiceProvider = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], ServiceProvider);
exports.ServiceProviderSchema = mongoose_1.SchemaFactory.createForClass(ServiceProvider);
exports.ServiceProviderSchema.index({ userId: 1 }, { unique: true });
exports.ServiceProviderSchema.index({ status: 1 });
exports.ServiceProviderSchema.index({ 'businessInfo.companyName': 'text', 'businessInfo.description': 'text' });
exports.ServiceProviderSchema.index({ 'serviceAreas.province': 1, 'serviceAreas.cities': 1 });
exports.ServiceProviderSchema.index({ 'services.category': 1 });
exports.ServiceProviderSchema.index({ 'rating.overall': -1 });
exports.ServiceProviderSchema.index({ 'stats.projectsCompleted': -1 });
exports.ServiceProviderSchema.virtual('isApproved').get(function () {
    return this.status === ServiceProviderStatus.APPROVED;
});
exports.ServiceProviderSchema.virtual('averageRating').get(function () {
    return this.rating?.totalReviews > 0 ? this.rating.totalRating / this.rating.totalReviews : 0;
});
exports.ServiceProviderSchema.set('toJSON', { virtuals: true });
exports.ServiceProviderSchema.set('toObject', { virtuals: true });


/***/ }),
/* 49 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ServiceProvidersController = void 0;
const common_1 = __webpack_require__(2);
const swagger_1 = __webpack_require__(3);
const throttler_1 = __webpack_require__(10);
const service_providers_service_1 = __webpack_require__(47);
const create_service_provider_dto_1 = __webpack_require__(50);
const update_service_provider_dto_1 = __webpack_require__(51);
const service_provider_query_dto_1 = __webpack_require__(52);
const jwt_auth_guard_1 = __webpack_require__(34);
const roles_guard_1 = __webpack_require__(35);
const roles_decorator_1 = __webpack_require__(36);
const service_provider_schema_1 = __webpack_require__(48);
const user_schema_1 = __webpack_require__(21);
let ServiceProvidersController = class ServiceProvidersController {
    constructor(serviceProvidersService) {
        this.serviceProvidersService = serviceProvidersService;
    }
    async create(createServiceProviderDto, req) {
        return this.serviceProvidersService.create(createServiceProviderDto, req.user);
    }
    async findAll(query) {
        return this.serviceProvidersService.findAll(query);
    }
    async findMyProfile(req) {
        return this.serviceProvidersService.findByUserId(req.user._id.toString());
    }
    async getStats() {
        return this.serviceProvidersService.getStats();
    }
    async findOne(id) {
        return this.serviceProvidersService.findOne(id);
    }
    async update(id, updateServiceProviderDto, req) {
        return this.serviceProvidersService.update(id, updateServiceProviderDto, req.user);
    }
    async remove(id, req) {
        return this.serviceProvidersService.remove(id, req.user);
    }
    async review(id, body, req) {
        return this.serviceProvidersService.review(id, body.status, body.reviewNotes, req.user);
    }
};
exports.ServiceProvidersController = ServiceProvidersController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '申请成为服务商' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '服务商申请提交成功',
        type: service_provider_schema_1.ServiceProvider,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数错误',
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '未授权',
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: '用户已经是服务商',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof create_service_provider_dto_1.CreateServiceProviderDto !== "undefined" && create_service_provider_dto_1.CreateServiceProviderDto) === "function" ? _b : Object, Object]),
    __metadata("design:returntype", typeof (_c = typeof Promise !== "undefined" && Promise) === "function" ? _c : Object)
], ServiceProvidersController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取服务商列表' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取服务商列表成功',
        type: [service_provider_schema_1.ServiceProvider],
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, description: '页码' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: '每页数量' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, description: '服务商状态' }),
    (0, swagger_1.ApiQuery)({ name: 'category', required: false, description: '服务类别' }),
    (0, swagger_1.ApiQuery)({ name: 'province', required: false, description: '省份' }),
    (0, swagger_1.ApiQuery)({ name: 'city', required: false, description: '城市' }),
    (0, swagger_1.ApiQuery)({ name: 'minRating', required: false, description: '最低评分' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: '搜索关键词' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_d = typeof service_provider_query_dto_1.ServiceProviderQueryDto !== "undefined" && service_provider_query_dto_1.ServiceProviderQueryDto) === "function" ? _d : Object]),
    __metadata("design:returntype", typeof (_e = typeof Promise !== "undefined" && Promise) === "function" ? _e : Object)
], ServiceProvidersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('me'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取我的服务商信息' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取服务商信息成功',
        type: service_provider_schema_1.ServiceProvider,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '服务商信息不存在',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_f = typeof Promise !== "undefined" && Promise) === "function" ? _f : Object)
], ServiceProvidersController.prototype, "findMyProfile", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(user_schema_1.UserType.ADMIN),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取服务商统计信息（管理员）' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取统计信息成功',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_g = typeof Promise !== "undefined" && Promise) === "function" ? _g : Object)
], ServiceProvidersController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取服务商详情' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取服务商详情成功',
        type: service_provider_schema_1.ServiceProvider,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '服务商不存在',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '服务商ID' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_h = typeof Promise !== "undefined" && Promise) === "function" ? _h : Object)
], ServiceProvidersController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '更新服务商信息' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '服务商信息更新成功',
        type: service_provider_schema_1.ServiceProvider,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数错误',
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '未授权',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: '无权限',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '服务商不存在',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '服务商ID' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, typeof (_j = typeof update_service_provider_dto_1.UpdateServiceProviderDto !== "undefined" && update_service_provider_dto_1.UpdateServiceProviderDto) === "function" ? _j : Object, Object]),
    __metadata("design:returntype", typeof (_k = typeof Promise !== "undefined" && Promise) === "function" ? _k : Object)
], ServiceProvidersController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: '删除服务商申请' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: '服务商申请删除成功',
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数错误',
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '未授权',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: '无权限',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '服务商不存在',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '服务商ID' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_l = typeof Promise !== "undefined" && Promise) === "function" ? _l : Object)
], ServiceProvidersController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/review'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(user_schema_1.UserType.ADMIN),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '审核服务商申请（管理员）' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '审核完成',
        type: service_provider_schema_1.ServiceProvider,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数错误',
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '未授权',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: '无权限',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '服务商不存在',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '服务商ID' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                status: {
                    type: 'string',
                    enum: Object.values(service_provider_schema_1.ServiceProviderStatus),
                    description: '审核状态',
                },
                reviewNotes: {
                    type: 'string',
                    description: '审核备注',
                },
            },
            required: ['status', 'reviewNotes'],
        },
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", typeof (_m = typeof Promise !== "undefined" && Promise) === "function" ? _m : Object)
], ServiceProvidersController.prototype, "review", null);
exports.ServiceProvidersController = ServiceProvidersController = __decorate([
    (0, swagger_1.ApiTags)('Service Providers'),
    (0, common_1.Controller)('service-providers'),
    (0, common_1.UseGuards)(throttler_1.ThrottlerGuard),
    __metadata("design:paramtypes", [typeof (_a = typeof service_providers_service_1.ServiceProvidersService !== "undefined" && service_providers_service_1.ServiceProvidersService) === "function" ? _a : Object])
], ServiceProvidersController);


/***/ }),
/* 50 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CreateServiceProviderDto = exports.CreatePortfolioDto = exports.CreateInsuranceDto = exports.CreateCertificationDto = exports.CreateServiceOfferedDto = exports.CreateServiceAreaDto = exports.CreateBusinessInfoDto = void 0;
const class_validator_1 = __webpack_require__(24);
const class_transformer_1 = __webpack_require__(27);
const swagger_1 = __webpack_require__(3);
const service_provider_schema_1 = __webpack_require__(48);
class CreateBusinessInfoDto {
}
exports.CreateBusinessInfoDto = CreateBusinessInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '公司名称' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1),
    (0, class_validator_1.MaxLength)(200),
    __metadata("design:type", String)
], CreateBusinessInfoDto.prototype, "companyName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '业务类型', enum: service_provider_schema_1.BusinessType }),
    (0, class_validator_1.IsEnum)(service_provider_schema_1.BusinessType),
    __metadata("design:type", typeof (_a = typeof service_provider_schema_1.BusinessType !== "undefined" && service_provider_schema_1.BusinessType) === "function" ? _a : Object)
], CreateBusinessInfoDto.prototype, "businessType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '营业执照号' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateBusinessInfoDto.prototype, "licenseNumber", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '税号' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateBusinessInfoDto.prototype, "taxNumber", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '成立年份' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1900),
    (0, class_validator_1.Max)(new Date().getFullYear()),
    __metadata("design:type", Number)
], CreateBusinessInfoDto.prototype, "establishedYear", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '员工数量' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(10000),
    __metadata("design:type", Number)
], CreateBusinessInfoDto.prototype, "employeeCount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '公司描述' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(2000),
    __metadata("design:type", String)
], CreateBusinessInfoDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '公司网站' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateBusinessInfoDto.prototype, "website", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '营业执照图片', type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateBusinessInfoDto.prototype, "licenseImages", void 0);
class CreateServiceAreaDto {
}
exports.CreateServiceAreaDto = CreateServiceAreaDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '省份' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateServiceAreaDto.prototype, "province", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '服务城市', type: [String] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateServiceAreaDto.prototype, "cities", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '服务半径(公里)' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(1000),
    __metadata("design:type", Number)
], CreateServiceAreaDto.prototype, "radius", void 0);
class CreateServiceOfferedDto {
}
exports.CreateServiceOfferedDto = CreateServiceOfferedDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '服务类别', enum: service_provider_schema_1.ServiceCategory }),
    (0, class_validator_1.IsEnum)(service_provider_schema_1.ServiceCategory),
    __metadata("design:type", typeof (_b = typeof service_provider_schema_1.ServiceCategory !== "undefined" && service_provider_schema_1.ServiceCategory) === "function" ? _b : Object)
], CreateServiceOfferedDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '服务名称' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1),
    (0, class_validator_1.MaxLength)(200),
    __metadata("design:type", String)
], CreateServiceOfferedDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '服务描述' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(1000),
    __metadata("design:type", String)
], CreateServiceOfferedDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '起始价格' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateServiceOfferedDto.prototype, "startingPrice", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '价格单位' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], CreateServiceOfferedDto.prototype, "priceUnit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '服务图片', type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateServiceOfferedDto.prototype, "images", void 0);
class CreateCertificationDto {
}
exports.CreateCertificationDto = CreateCertificationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '认证名称' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1),
    (0, class_validator_1.MaxLength)(200),
    __metadata("design:type", String)
], CreateCertificationDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '颁发机构' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1),
    (0, class_validator_1.MaxLength)(200),
    __metadata("design:type", String)
], CreateCertificationDto.prototype, "issuingOrganization", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '认证编号' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateCertificationDto.prototype, "certificationNumber", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '获得日期' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], CreateCertificationDto.prototype, "issuedDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '过期日期' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], CreateCertificationDto.prototype, "expiryDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '认证文件URL' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateCertificationDto.prototype, "documentUrl", void 0);
class CreateInsuranceDto {
}
exports.CreateInsuranceDto = CreateInsuranceDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '保险类型' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateInsuranceDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '保险公司' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1),
    (0, class_validator_1.MaxLength)(200),
    __metadata("design:type", String)
], CreateInsuranceDto.prototype, "provider", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '保单号' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateInsuranceDto.prototype, "policyNumber", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '保险金额' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateInsuranceDto.prototype, "coverageAmount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '生效日期' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], CreateInsuranceDto.prototype, "effectiveDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '过期日期' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], CreateInsuranceDto.prototype, "expiryDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '保单文件URL' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateInsuranceDto.prototype, "documentUrl", void 0);
class CreatePortfolioDto {
}
exports.CreatePortfolioDto = CreatePortfolioDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '项目标题' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1),
    (0, class_validator_1.MaxLength)(200),
    __metadata("design:type", String)
], CreatePortfolioDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '项目描述' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(1000),
    __metadata("design:type", String)
], CreatePortfolioDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '项目类别', enum: service_provider_schema_1.ServiceCategory }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(service_provider_schema_1.ServiceCategory),
    __metadata("design:type", typeof (_g = typeof service_provider_schema_1.ServiceCategory !== "undefined" && service_provider_schema_1.ServiceCategory) === "function" ? _g : Object)
], CreatePortfolioDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '项目图片', type: [String] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreatePortfolioDto.prototype, "images", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '项目成本' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreatePortfolioDto.prototype, "cost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '项目时长(天)' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(3650),
    __metadata("design:type", Number)
], CreatePortfolioDto.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '完成日期' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_h = typeof Date !== "undefined" && Date) === "function" ? _h : Object)
], CreatePortfolioDto.prototype, "completedDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '客户评价' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(1000),
    __metadata("design:type", String)
], CreatePortfolioDto.prototype, "clientTestimonial", void 0);
class CreateServiceProviderDto {
}
exports.CreateServiceProviderDto = CreateServiceProviderDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '业务信息', type: CreateBusinessInfoDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => CreateBusinessInfoDto),
    __metadata("design:type", CreateBusinessInfoDto)
], CreateServiceProviderDto.prototype, "businessInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '服务区域', type: [CreateServiceAreaDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CreateServiceAreaDto),
    __metadata("design:type", Array)
], CreateServiceProviderDto.prototype, "serviceAreas", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '提供的服务', type: [CreateServiceOfferedDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CreateServiceOfferedDto),
    __metadata("design:type", Array)
], CreateServiceProviderDto.prototype, "services", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '认证信息', type: [CreateCertificationDto] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CreateCertificationDto),
    __metadata("design:type", Array)
], CreateServiceProviderDto.prototype, "certifications", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '保险信息', type: [CreateInsuranceDto] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CreateInsuranceDto),
    __metadata("design:type", Array)
], CreateServiceProviderDto.prototype, "insurance", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '作品集', type: [CreatePortfolioDto] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CreatePortfolioDto),
    __metadata("design:type", Array)
], CreateServiceProviderDto.prototype, "portfolio", void 0);


/***/ }),
/* 51 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UpdateServiceProviderDto = void 0;
const swagger_1 = __webpack_require__(3);
const create_service_provider_dto_1 = __webpack_require__(50);
class UpdateServiceProviderDto extends (0, swagger_1.PartialType)(create_service_provider_dto_1.CreateServiceProviderDto) {
}
exports.UpdateServiceProviderDto = UpdateServiceProviderDto;


/***/ }),
/* 52 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ServiceProviderQueryDto = void 0;
const class_validator_1 = __webpack_require__(24);
const class_transformer_1 = __webpack_require__(27);
const swagger_1 = __webpack_require__(3);
const pagination_dto_1 = __webpack_require__(33);
const service_provider_schema_1 = __webpack_require__(48);
class ServiceProviderQueryDto extends pagination_dto_1.PaginationDto {
    constructor() {
        super(...arguments);
        this.sortBy = 'createdAt';
        this.sortOrder = 'desc';
        this.radius = 50;
    }
}
exports.ServiceProviderQueryDto = ServiceProviderQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '服务商状态', enum: service_provider_schema_1.ServiceProviderStatus }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(service_provider_schema_1.ServiceProviderStatus),
    __metadata("design:type", typeof (_a = typeof service_provider_schema_1.ServiceProviderStatus !== "undefined" && service_provider_schema_1.ServiceProviderStatus) === "function" ? _a : Object)
], ServiceProviderQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '服务类别', enum: service_provider_schema_1.ServiceCategory }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(service_provider_schema_1.ServiceCategory),
    __metadata("design:type", typeof (_b = typeof service_provider_schema_1.ServiceCategory !== "undefined" && service_provider_schema_1.ServiceCategory) === "function" ? _b : Object)
], ServiceProviderQueryDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '省份' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ServiceProviderQueryDto.prototype, "province", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '城市' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ServiceProviderQueryDto.prototype, "city", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '最低评分' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(5),
    __metadata("design:type", Number)
], ServiceProviderQueryDto.prototype, "minRating", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '搜索关键词' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ServiceProviderQueryDto.prototype, "search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '排序字段', enum: ['createdAt', 'rating.overall', 'stats.projectsCompleted'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ServiceProviderQueryDto.prototype, "sortBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '排序方向', enum: ['asc', 'desc'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ServiceProviderQueryDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '纬度（用于地理位置搜索）' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ServiceProviderQueryDto.prototype, "lat", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '经度（用于地理位置搜索）' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ServiceProviderQueryDto.prototype, "lng", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '搜索半径（公里）', minimum: 1, maximum: 500 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(500),
    __metadata("design:type", Number)
], ServiceProviderQueryDto.prototype, "radius", void 0);


/***/ }),
/* 53 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.QuotesModule = void 0;
const common_1 = __webpack_require__(2);
let QuotesModule = class QuotesModule {
};
exports.QuotesModule = QuotesModule;
exports.QuotesModule = QuotesModule = __decorate([
    (0, common_1.Module)({})
], QuotesModule);


/***/ }),
/* 54 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.PaymentsModule = void 0;
const common_1 = __webpack_require__(2);
let PaymentsModule = class PaymentsModule {
};
exports.PaymentsModule = PaymentsModule;
exports.PaymentsModule = PaymentsModule = __decorate([
    (0, common_1.Module)({})
], PaymentsModule);


/***/ }),
/* 55 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.FilesModule = void 0;
const common_1 = __webpack_require__(2);
let FilesModule = class FilesModule {
};
exports.FilesModule = FilesModule;
exports.FilesModule = FilesModule = __decorate([
    (0, common_1.Module)({})
], FilesModule);


/***/ }),
/* 56 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.NotificationsModule = void 0;
const common_1 = __webpack_require__(2);
let NotificationsModule = class NotificationsModule {
};
exports.NotificationsModule = NotificationsModule;
exports.NotificationsModule = NotificationsModule = __decorate([
    (0, common_1.Module)({})
], NotificationsModule);


/***/ }),
/* 57 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ReviewsModule = void 0;
const common_1 = __webpack_require__(2);
let ReviewsModule = class ReviewsModule {
};
exports.ReviewsModule = ReviewsModule;
exports.ReviewsModule = ReviewsModule = __decorate([
    (0, common_1.Module)({})
], ReviewsModule);


/***/ }),
/* 58 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.TasksModule = void 0;
const common_1 = __webpack_require__(2);
let TasksModule = class TasksModule {
};
exports.TasksModule = TasksModule;
exports.TasksModule = TasksModule = __decorate([
    (0, common_1.Module)({})
], TasksModule);


/***/ }),
/* 59 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.DatabaseModule = void 0;
const common_1 = __webpack_require__(2);
let DatabaseModule = class DatabaseModule {
};
exports.DatabaseModule = DatabaseModule;
exports.DatabaseModule = DatabaseModule = __decorate([
    (0, common_1.Module)({})
], DatabaseModule);


/***/ }),
/* 60 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RedisModule = void 0;
const common_1 = __webpack_require__(2);
let RedisModule = class RedisModule {
};
exports.RedisModule = RedisModule;
exports.RedisModule = RedisModule = __decorate([
    (0, common_1.Module)({})
], RedisModule);


/***/ }),
/* 61 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.EmailModule = void 0;
const common_1 = __webpack_require__(2);
let EmailModule = class EmailModule {
};
exports.EmailModule = EmailModule;
exports.EmailModule = EmailModule = __decorate([
    (0, common_1.Module)({})
], EmailModule);


/***/ }),
/* 62 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.SmsModule = void 0;
const common_1 = __webpack_require__(2);
let SmsModule = class SmsModule {
};
exports.SmsModule = SmsModule;
exports.SmsModule = SmsModule = __decorate([
    (0, common_1.Module)({})
], SmsModule);


/***/ }),
/* 63 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.TranslationModule = void 0;
const common_1 = __webpack_require__(2);
let TranslationModule = class TranslationModule {
};
exports.TranslationModule = TranslationModule;
exports.TranslationModule = TranslationModule = __decorate([
    (0, common_1.Module)({})
], TranslationModule);


/***/ }),
/* 64 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.databaseConfig = void 0;
const config_1 = __webpack_require__(4);
exports.databaseConfig = (0, config_1.registerAs)('database', () => ({
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/homereno',
    options: {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        maxPoolSize: parseInt(process.env.DB_MAX_POOL_SIZE) || 10,
        serverSelectionTimeoutMS: parseInt(process.env.DB_SERVER_SELECTION_TIMEOUT) || 5000,
        socketTimeoutMS: parseInt(process.env.DB_SOCKET_TIMEOUT) || 45000,
        bufferMaxEntries: 0,
        bufferCommands: false,
    },
}));


/***/ }),
/* 65 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.redisConfig = void 0;
const config_1 = __webpack_require__(4);
exports.redisConfig = (0, config_1.registerAs)('redis', () => ({
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB) || 0,
    ttl: parseInt(process.env.REDIS_TTL) || 3600,
    maxRetriesPerRequest: 3,
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    maxRetriesPerRequest: null,
}));


/***/ }),
/* 66 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.authConfig = void 0;
const config_1 = __webpack_require__(4);
exports.authConfig = (0, config_1.registerAs)('auth', () => ({
    jwt: {
        secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
        expiresIn: process.env.JWT_EXPIRES_IN || '24h',
        refreshSecret: process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key',
        refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    },
    bcrypt: {
        saltRounds: parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12,
    },
    oauth: {
        google: {
            clientId: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET,
        },
        facebook: {
            clientId: process.env.FACEBOOK_CLIENT_ID,
            clientSecret: process.env.FACEBOOK_CLIENT_SECRET,
        },
    },
    session: {
        maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS) || 5,
        lockoutDuration: parseInt(process.env.LOCKOUT_DURATION) || 900000,
    },
}));


/***/ }),
/* 67 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.emailConfig = void 0;
const config_1 = __webpack_require__(4);
exports.emailConfig = (0, config_1.registerAs)('email', () => ({
    smtp: {
        host: process.env.SMTP_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.SMTP_PORT) || 587,
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASS,
        },
    },
    from: {
        name: process.env.EMAIL_FROM_NAME || 'HomeReno',
        address: process.env.EMAIL_FROM_ADDRESS || '<EMAIL>',
    },
    templates: {
        verification: 'email-verification',
        passwordReset: 'password-reset',
        welcome: 'welcome',
        projectUpdate: 'project-update',
        quoteReceived: 'quote-received',
    },
}));


/***/ }),
/* 68 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.smsConfig = void 0;
const config_1 = __webpack_require__(4);
exports.smsConfig = (0, config_1.registerAs)('sms', () => ({
    twilio: {
        accountSid: process.env.TWILIO_ACCOUNT_SID,
        authToken: process.env.TWILIO_AUTH_TOKEN,
        phoneNumber: process.env.TWILIO_PHONE_NUMBER,
    },
    templates: {
        verification: 'Your HomeReno verification code is: {{code}}',
        projectUpdate: 'HomeReno: Your project {{projectName}} has been updated.',
        quoteReceived: 'HomeReno: You have received a new quote for your project.',
    },
}));


/***/ }),
/* 69 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.fileConfig = void 0;
const config_1 = __webpack_require__(4);
exports.fileConfig = (0, config_1.registerAs)('file', () => ({
    upload: {
        maxSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024,
        allowedTypes: [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ],
        destination: process.env.UPLOAD_DESTINATION || './uploads',
    },
    aws: {
        region: process.env.AWS_REGION || 'us-east-1',
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
        bucket: process.env.AWS_S3_BUCKET,
        cloudFront: process.env.AWS_CLOUDFRONT_DOMAIN,
    },
    storage: {
        provider: process.env.STORAGE_PROVIDER || 'local',
    },
}));


/***/ })
/******/ 	]);
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
var exports = __webpack_exports__;

Object.defineProperty(exports, "__esModule", ({ value: true }));
const core_1 = __webpack_require__(1);
const common_1 = __webpack_require__(2);
const swagger_1 = __webpack_require__(3);
const config_1 = __webpack_require__(4);
const helmet_1 = __webpack_require__(5);
const compression = __webpack_require__(6);
const app_module_1 = __webpack_require__(7);
const nest_winston_1 = __webpack_require__(12);
const winston = __webpack_require__(13);
async function bootstrap() {
    const logger = nest_winston_1.WinstonModule.createLogger({
        transports: [
            new winston.transports.Console({
                format: winston.format.combine(winston.format.timestamp(), winston.format.colorize(), winston.format.simple()),
            }),
            new winston.transports.File({
                filename: 'logs/error.log',
                level: 'error',
                format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
            }),
            new winston.transports.File({
                filename: 'logs/combined.log',
                format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
            }),
        ],
    });
    const app = await core_1.NestFactory.create(app_module_1.AppModule, {
        logger,
    });
    const configService = app.get(config_1.ConfigService);
    app.use((0, helmet_1.default)());
    app.use(compression());
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        transformOptions: {
            enableImplicitConversion: true,
        },
    }));
    app.enableCors({
        origin: configService.get('CORS_ORIGINS')?.split(',') || ['http://localhost:3000'],
        credentials: true,
    });
    app.setGlobalPrefix('api/v1');
    if (configService.get('NODE_ENV') !== 'production') {
        const config = new swagger_1.DocumentBuilder()
            .setTitle('HomeReno API')
            .setDescription('HomeReno - Canadian Home Renovation Platform API Documentation')
            .setVersion('1.0')
            .addBearerAuth({
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
            name: 'JWT',
            description: 'Enter JWT token',
            in: 'header',
        }, 'JWT-auth')
            .addTag('Auth', 'Authentication endpoints')
            .addTag('Users', 'User management endpoints')
            .addTag('Projects', 'Project management endpoints')
            .addTag('Service Providers', 'Service provider endpoints')
            .addTag('Quotes', 'Quote management endpoints')
            .addTag('Payments', 'Payment processing endpoints')
            .addTag('Files', 'File upload/download endpoints')
            .addTag('Notifications', 'Notification endpoints')
            .build();
        const document = swagger_1.SwaggerModule.createDocument(app, config);
        swagger_1.SwaggerModule.setup('api/docs', app, document, {
            swaggerOptions: {
                persistAuthorization: true,
            },
        });
    }
    const port = configService.get('PORT') || 3000;
    await app.listen(port);
    logger.log(`Application is running on: http://localhost:${port}`);
    logger.log(`Swagger documentation: http://localhost:${port}/api/docs`);
}
bootstrap().catch((error) => {
    console.error('Application failed to start:', error);
    process.exit(1);
});

})();

/******/ })()
;