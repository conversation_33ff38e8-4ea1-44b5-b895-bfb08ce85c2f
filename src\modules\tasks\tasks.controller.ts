import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { TasksService } from './tasks.service';
import {
  CreateTaskDto,
  UpdateTaskDto,
  TaskQueryDto,
  CreateTaskCommentDto,
  CreateTimeLogDto,
} from './dto/create-task.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Task, TaskStatus } from './schemas/task.schema';
import { PaginatedResult } from '../../common/interfaces/paginated-result.interface';
import { UserType } from '../users/schemas/user.schema';

@ApiTags('Tasks')
@Controller('tasks')
@UseGuards(ThrottlerGuard)
export class TasksController {
  constructor(private readonly tasksService: TasksService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建任务' })
  @ApiResponse({
    status: 201,
    description: '任务创建成功',
    type: Task,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  async create(@Body() createTaskDto: CreateTaskDto, @Request() req: any): Promise<Task> {
    return this.tasksService.create(createTaskDto, req.user);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取任务列表' })
  @ApiResponse({
    status: 200,
    description: '获取任务列表成功',
    type: [Task],
  })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiQuery({ name: 'type', required: false, description: '任务类型' })
  @ApiQuery({ name: 'status', required: false, description: '任务状态' })
  @ApiQuery({ name: 'priority', required: false, description: '任务优先级' })
  async findAll(@Query() query: TaskQueryDto, @Request() req: any): Promise<PaginatedResult<Task>> {
    return this.tasksService.findAll(query, req.user);
  }

  @Get('stats/:userId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取用户任务统计' })
  @ApiResponse({
    status: 200,
    description: '获取统计信息成功',
  })
  @ApiParam({ name: 'userId', description: '用户ID' })
  async getUserStats(@Param('userId') userId: string): Promise<any> {
    return this.tasksService.getUserStats(userId);
  }

  @Get('project-stats/:projectId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取项目任务统计' })
  @ApiResponse({
    status: 200,
    description: '获取统计信息成功',
  })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  async getProjectStats(@Param('projectId') projectId: string): Promise<any> {
    return this.tasksService.getProjectStats(projectId);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据ID获取任务详情' })
  @ApiResponse({
    status: 200,
    description: '获取任务详情成功',
    type: Task,
  })
  @ApiResponse({
    status: 404,
    description: '任务不存在',
  })
  @ApiParam({ name: 'id', description: '任务ID' })
  async findOne(@Param('id') id: string, @Request() req: any): Promise<Task> {
    return this.tasksService.findOne(id, req.user);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新任务' })
  @ApiResponse({
    status: 200,
    description: '任务更新成功',
    type: Task,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '任务不存在',
  })
  @ApiParam({ name: 'id', description: '任务ID' })
  async update(
    @Param('id') id: string,
    @Body() updateTaskDto: UpdateTaskDto,
    @Request() req: any,
  ): Promise<Task> {
    return this.tasksService.update(id, updateTaskDto, req.user);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除任务' })
  @ApiResponse({
    status: 204,
    description: '任务删除成功',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '任务不存在',
  })
  @ApiParam({ name: 'id', description: '任务ID' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string, @Request() req: any): Promise<void> {
    return this.tasksService.remove(id, req.user);
  }

  @Post(':id/comments')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '添加任务评论' })
  @ApiResponse({
    status: 200,
    description: '评论添加成功',
    type: Task,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '任务不存在',
  })
  @ApiParam({ name: 'id', description: '任务ID' })
  async addComment(
    @Param('id') id: string,
    @Body() commentDto: CreateTaskCommentDto,
    @Request() req: any,
  ): Promise<Task> {
    return this.tasksService.addComment(id, commentDto, req.user);
  }

  @Post(':id/time-logs')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '添加时间记录' })
  @ApiResponse({
    status: 200,
    description: '时间记录添加成功',
    type: Task,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '任务不存在',
  })
  @ApiParam({ name: 'id', description: '任务ID' })
  async addTimeLog(
    @Param('id') id: string,
    @Body() timeLogDto: CreateTimeLogDto,
    @Request() req: any,
  ): Promise<Task> {
    return this.tasksService.addTimeLog(id, timeLogDto, req.user);
  }

  @Patch(':id/status')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新任务状态' })
  @ApiResponse({
    status: 200,
    description: '状态更新成功',
    type: Task,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '任务不存在',
  })
  @ApiParam({ name: 'id', description: '任务ID' })
  async updateStatus(
    @Param('id') id: string,
    @Body() body: { status: TaskStatus },
    @Request() req: any,
  ): Promise<Task> {
    return this.tasksService.updateStatus(id, body.status, req.user);
  }
}
