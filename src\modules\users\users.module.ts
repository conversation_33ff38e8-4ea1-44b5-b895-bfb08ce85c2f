import { <PERSON>du<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { User, UserSchema } from './schemas/user.schema';
import { DatabaseAdapterService } from '../../common/services/database-adapter.service';
import { MockDatabaseService } from '../../common/services/mock-database.service';

@Module({
  imports: [
    ...(process.env.USE_MOCK_DATABASE === 'true' ? [] : [
      MongooseModule.forFeature([{ name: User.name, schema: UserSchema }])
    ]),
  ],
  controllers: [UsersController],
  providers: [UsersService, DatabaseAdapterService, MockDatabaseService],
  exports: [UsersService],
})
export class UsersModule {}
