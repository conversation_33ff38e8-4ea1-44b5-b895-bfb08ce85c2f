import { NestFactory } from '@nestjs/core';
import { AppModule } from '../../../app.module';
import { DesignToolsService } from '../design-tools.service';

async function testApiEndpoints() {
  console.log('🧪 测试3D设计工具API端点...');
  
  const app = await NestFactory.createApplicationContext(AppModule);
  const designToolsService = app.get(DesignToolsService);
  
  try {
    // 测试获取设计模板
    console.log('\n📐 测试获取设计模板...');
    const templates = await designToolsService.findAllTemplates({
      page: 1,
      limit: 10
    });
    console.log(`✅ 成功获取 ${templates.data.length} 个设计模板`);
    templates.data.forEach(template => {
      console.log(`   - ${template.name} (${template.style}风格)`);
    });
    
    // 测试获取设计资产
    console.log('\n🎨 测试获取设计资产...');
    const assets = await designToolsService.findAllAssets({
      page: 1,
      limit: 10
    });
    console.log(`✅ 成功获取 ${assets.data.length} 个设计资产`);
    assets.data.forEach(asset => {
      console.log(`   - ${asset.name} (${asset.type}类型)`);
    });
    
    // 测试按类别筛选模板
    console.log('\n🏠 测试按风格筛选模板...');
    const modernTemplates = await designToolsService.findAllTemplates({
      page: 1,
      limit: 10,
      style: 'modern'
    });
    console.log(`✅ 找到 ${modernTemplates.data.length} 个现代风格模板`);
    
    // 测试按类型筛选资产
    console.log('\n🪑 测试按类型筛选资产...');
    const furnitureAssets = await designToolsService.findAllAssets({
      page: 1,
      limit: 10,
      type: 'furniture'
    });
    console.log(`✅ 找到 ${furnitureAssets.data.length} 个家具资产`);
    
    // 测试创建设计项目
    console.log('\n🎯 测试创建设计项目...');
    const projectData = {
      name: '测试客厅设计',
      description: '这是一个测试的客厅设计项目',
      roomType: 'living_room',
      dimensions: {
        width: 5.0,
        height: 3.0,
        length: 6.0
      },
      style: 'modern',
      budget: {
        min: 10000,
        max: 20000,
        currency: 'CAD'
      }
    };
    
    const project = await designToolsService.createProject('test-user-id', projectData);
    console.log(`✅ 成功创建设计项目: ${project.name}`);
    console.log(`   - 项目ID: ${project._id}`);
    console.log(`   - 房间类型: ${project.roomType}`);
    console.log(`   - 风格: ${project.style}`);
    
    // 测试获取项目详情
    console.log('\n📋 测试获取项目详情...');
    const projectDetails = await designToolsService.findProjectById(project._id.toString());
    console.log(`✅ 成功获取项目详情: ${projectDetails.name}`);
    
    console.log('\n🎉 所有API端点测试通过！');
    console.log('\n📊 测试结果总结:');
    console.log(`   ✅ 设计模板API: ${templates.data.length} 个模板`);
    console.log(`   ✅ 设计资产API: ${assets.data.length} 个资产`);
    console.log(`   ✅ 模板筛选API: ${modernTemplates.data.length} 个现代风格模板`);
    console.log(`   ✅ 资产筛选API: ${furnitureAssets.data.length} 个家具资产`);
    console.log(`   ✅ 项目创建API: 项目创建成功`);
    console.log(`   ✅ 项目查询API: 项目查询成功`);
    
  } catch (error) {
    console.error('❌ API测试失败:', error.message);
    console.error(error.stack);
  } finally {
    await app.close();
  }
}

testApiEndpoints();
