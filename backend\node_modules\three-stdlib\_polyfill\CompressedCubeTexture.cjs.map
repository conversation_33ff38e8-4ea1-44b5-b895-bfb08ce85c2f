{"version": 3, "file": "CompressedCubeTexture.cjs", "sources": ["../../src/_polyfill/CompressedCubeTexture.js"], "sourcesContent": ["import { CompressedTexture, CubeReflectionMapping } from 'three'\n\nclass CompressedCubeTexture extends CompressedTexture {\n  constructor(images, format, type) {\n    super(undefined, images[0].width, images[0].height, format, type, CubeReflectionMapping)\n\n    this.isCompressedCubeTexture = true\n    this.isCubeTexture = true\n\n    this.image = images\n  }\n}\n\nexport { CompressedCubeTexture }\n"], "names": ["CompressedTexture", "CubeReflectionMapping"], "mappings": ";;;AAEA,MAAM,8BAA8BA,MAAAA,kBAAkB;AAAA,EACpD,YAAY,QAAQ,QAAQ,MAAM;AAChC,UAAM,QAAW,OAAO,CAAC,EAAE,OAAO,OAAO,CAAC,EAAE,QAAQ,QAAQ,MAAMC,MAAAA,qBAAqB;AAEvF,SAAK,0BAA0B;AAC/B,SAAK,gBAAgB;AAErB,SAAK,QAAQ;AAAA,EACd;AACH;;"}