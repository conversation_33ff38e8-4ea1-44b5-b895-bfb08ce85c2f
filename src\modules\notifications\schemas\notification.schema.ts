import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type NotificationDocument = Notification & Document;

export enum NotificationType {
  PROJECT_UPDATE = 'project_update',
  QUOTE_RECEIVED = 'quote_received',
  QUOTE_ACCEPTED = 'quote_accepted',
  QUOTE_DECLINED = 'quote_declined',
  PAYMENT_RECEIVED = 'payment_received',
  PAYMENT_REQUIRED = 'payment_required',
  SERVICE_PROVIDER_APPROVED = 'service_provider_approved',
  SERVICE_PROVIDER_REJECTED = 'service_provider_rejected',
  PROJECT_COMPLETED = 'project_completed',
  REVIEW_RECEIVED = 'review_received',
  MESSAGE_RECEIVED = 'message_received',
  SYSTEM_ANNOUNCEMENT = 'system_announcement',
  REMINDER = 'reminder',
}

export enum NotificationPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
}

export enum NotificationChannel {
  IN_APP = 'in_app',
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push',
}

@Schema({ _id: false })
export class NotificationData {
  @Prop()
  @ApiProperty({ description: '项目ID' })
  projectId?: string;

  @Prop()
  @ApiProperty({ description: '报价ID' })
  quoteId?: string;

  @Prop()
  @ApiProperty({ description: '服务商ID' })
  serviceProviderId?: string;

  @Prop()
  @ApiProperty({ description: '支付ID' })
  paymentId?: string;

  @Prop()
  @ApiProperty({ description: '评价ID' })
  reviewId?: string;

  @Prop()
  @ApiProperty({ description: '金额' })
  amount?: number;

  @Prop()
  @ApiProperty({ description: '货币' })
  currency?: string;

  @Prop()
  @ApiProperty({ description: '截止日期' })
  dueDate?: Date;

  @Prop({ type: Object })
  @ApiProperty({ description: '其他数据' })
  metadata?: Record<string, any>;
}

@Schema({ timestamps: true })
export class Notification {
  @Prop({ type: Types.ObjectId, required: true, ref: 'User' })
  @ApiProperty({ description: '接收用户ID' })
  userId: Types.ObjectId;

  @Prop({ enum: NotificationType, required: true })
  @ApiProperty({ description: '通知类型', enum: NotificationType })
  type: NotificationType;

  @Prop({ required: true })
  @ApiProperty({ description: '通知标题' })
  title: string;

  @Prop({ required: true })
  @ApiProperty({ description: '通知内容' })
  message: string;

  @Prop({ enum: NotificationPriority, default: NotificationPriority.NORMAL })
  @ApiProperty({ description: '优先级', enum: NotificationPriority })
  priority: NotificationPriority;

  @Prop([{ type: String, enum: NotificationChannel }])
  @ApiProperty({ description: '发送渠道', enum: NotificationChannel, isArray: true })
  channels: NotificationChannel[];

  @Prop({ type: NotificationData })
  @ApiProperty({ description: '通知数据', type: NotificationData })
  data?: NotificationData;

  @Prop({ default: false })
  @ApiProperty({ description: '是否已读' })
  isRead: boolean;

  @Prop()
  @ApiProperty({ description: '阅读时间' })
  readAt?: Date;

  @Prop({ default: false })
  @ApiProperty({ description: '是否已发送' })
  isSent: boolean;

  @Prop()
  @ApiProperty({ description: '发送时间' })
  sentAt?: Date;

  @Prop()
  @ApiProperty({ description: '计划发送时间' })
  scheduledAt?: Date;

  @Prop()
  @ApiProperty({ description: '过期时间' })
  expiresAt?: Date;

  @Prop()
  @ApiProperty({ description: '操作URL' })
  actionUrl?: string;

  @Prop()
  @ApiProperty({ description: '操作按钮文本' })
  actionText?: string;

  @Prop()
  @ApiProperty({ description: '图标' })
  icon?: string;

  @Prop()
  @ApiProperty({ description: '图片URL' })
  imageUrl?: string;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  @ApiProperty({ description: '发送者ID' })
  senderId?: Types.ObjectId;

  @Prop({ default: 0 })
  @ApiProperty({ description: '重试次数' })
  retryCount: number;

  @Prop()
  @ApiProperty({ description: '最后错误信息' })
  lastError?: string;

  @Prop({ default: false })
  @ApiProperty({ description: '是否已删除' })
  isDeleted: boolean;

  @Prop()
  @ApiProperty({ description: '删除时间' })
  deletedAt?: Date;
}

export const NotificationSchema = SchemaFactory.createForClass(Notification);

// 创建索引
NotificationSchema.index({ userId: 1, createdAt: -1 });
NotificationSchema.index({ userId: 1, isRead: 1 });
NotificationSchema.index({ userId: 1, type: 1 });
NotificationSchema.index({ scheduledAt: 1, isSent: 1 });
NotificationSchema.index({ expiresAt: 1 });
NotificationSchema.index({ isDeleted: 1, createdAt: -1 });

// 虚拟字段
NotificationSchema.virtual('isExpired').get(function() {
  return this.expiresAt && new Date() > this.expiresAt;
});

NotificationSchema.virtual('isPending').get(function() {
  return !this.isSent && (!this.scheduledAt || this.scheduledAt <= new Date());
});

NotificationSchema.virtual('isScheduled').get(function() {
  return !this.isSent && this.scheduledAt && this.scheduledAt > new Date();
});

// 设置虚拟字段在JSON序列化时包含
NotificationSchema.set('toJSON', { virtuals: true });
NotificationSchema.set('toObject', { virtuals: true });
