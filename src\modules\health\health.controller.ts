import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { DatabaseAdapterService } from '../../common/services/database-adapter.service';
import { MockDatabaseService } from '../../common/services/mock-database.service';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(
    private databaseAdapter: DatabaseAdapterService,
    private mockDatabaseService?: MockDatabaseService,
  ) {}

  @Get()
  @ApiOperation({ summary: '健康检查' })
  @ApiResponse({ status: 200, description: '系统状态信息' })
  getHealth() {
    const databaseInfo = this.databaseAdapter.getDatabaseInfo();
    
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      database: databaseInfo,
      features: {
        mockDatabase: process.env.USE_MOCK_DATABASE === 'true',
        authentication: true,
        fileUpload: true,
        internationalization: true,
      }
    };
  }

  @Get('database')
  @ApiOperation({ summary: '数据库状态' })
  @ApiResponse({ status: 200, description: '数据库详细信息' })
  getDatabaseStatus() {
    const info = this.databaseAdapter.getDatabaseInfo();
    
    if (info.type === 'mock' && this.mockDatabaseService) {
      return {
        type: 'Mock Database (In-Memory)',
        status: 'connected',
        stats: this.mockDatabaseService.getStats(),
        sampleData: {
          users: ['<EMAIL>', '<EMAIL>'],
          projects: ['Kitchen renovation'],
          note: 'This is sample data for development. Use password: password123'
        }
      };
    }
    
    return info;
  }
}
