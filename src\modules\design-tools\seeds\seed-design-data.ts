import { NestFactory } from '@nestjs/core';
import { AppModule } from '../../../app.module';
import { DesignToolsService } from '../design-tools.service';
import { Model } from 'mongoose';
import { getModelToken } from '@nestjs/mongoose';
import { DesignTemplate } from '../schemas/design-template.schema';
import { DesignAsset } from '../schemas/design-asset.schema';
import { designTemplatesSeed } from './design-templates.seed';
import { designAssetsSeed } from './design-assets.seed';

async function seedDesignData() {
  const app = await NestFactory.createApplicationContext(AppModule);
  
  try {
    const designTemplateModel = app.get<Model<DesignTemplate>>(getModelToken(DesignTemplate.name));
    const designAssetModel = app.get<Model<DesignAsset>>(getModelToken(DesignAsset.name));

    console.log('🌱 开始种子数据初始化...');

    // 检查现有数据
    console.log('🔍 检查现有数据...');
    const existingTemplatesCount = await designTemplateModel.countDocuments({ isOfficial: true });
    const existingAssetsCount = await designAssetModel.countDocuments({ isOfficial: true });

    console.log(`📊 现有数据: ${existingTemplatesCount} 个模板, ${existingAssetsCount} 个资产`);

    if (existingTemplatesCount > 0 || existingAssetsCount > 0) {
      console.log('ℹ️  检测到现有官方数据，将跳过重复插入');
      console.log('🎉 种子数据已存在！');
      return;
    }

    // 插入设计模板
    console.log('📐 插入设计模板...');
    const insertedTemplates = await designTemplateModel.insertMany(designTemplatesSeed);
    console.log(`✅ 成功插入 ${insertedTemplates.length} 个设计模板`);

    // 插入设计资产
    console.log('🎨 插入设计资产...');
    const insertedAssets = await designAssetModel.insertMany(designAssetsSeed);
    console.log(`✅ 成功插入 ${insertedAssets.length} 个设计资产`);

    console.log('🎉 种子数据初始化完成！');
    console.log(`
📊 数据统计:
   - 设计模板: ${insertedTemplates.length} 个
   - 设计资产: ${insertedAssets.length} 个
   
🔗 可用端点:
   - GET /api/v1/design-tools/templates - 获取设计模板
   - GET /api/v1/design-tools/assets - 获取设计资产
   - POST /api/v1/design-tools/projects - 创建设计项目
    `);

  } catch (error) {
    console.error('❌ 种子数据初始化失败:', error);
  } finally {
    await app.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  seedDesignData().catch(console.error);
}

export { seedDesignData };
