import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { ReviewsService } from './reviews.service';
import {
  CreateReviewDto,
  UpdateReviewDto,
  ReviewQueryDto,
  CreateReviewResponseDto,
  ReviewModerationDto,
} from './dto/create-review.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Review } from './schemas/review.schema';
import { PaginatedResult } from '../../common/interfaces/paginated-result.interface';
import { UserType } from '../users/schemas/user.schema';

@ApiTags('Reviews')
@Controller('reviews')
@UseGuards(ThrottlerGuard)
export class ReviewsController {
  constructor(private readonly reviewsService: ReviewsService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建评价' })
  @ApiResponse({
    status: 201,
    description: '评价创建成功',
    type: Review,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 409,
    description: '已经评价过',
  })
  async create(@Body() createReviewDto: CreateReviewDto, @Request() req: any): Promise<Review> {
    return this.reviewsService.create(createReviewDto, req.user);
  }

  @Get()
  @ApiOperation({ summary: '获取评价列表' })
  @ApiResponse({
    status: 200,
    description: '获取评价列表成功',
    type: [Review],
  })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiQuery({ name: 'type', required: false, description: '评价类型' })
  @ApiQuery({ name: 'status', required: false, description: '评价状态' })
  @ApiQuery({ name: 'revieweeId', required: false, description: '被评价者ID' })
  @ApiQuery({ name: 'projectId', required: false, description: '项目ID' })
  @ApiQuery({ name: 'serviceProviderId', required: false, description: '服务商ID' })
  @ApiQuery({ name: 'minRating', required: false, description: '最低评分' })
  @ApiQuery({ name: 'maxRating', required: false, description: '最高评分' })
  @ApiQuery({ name: 'isRecommended', required: false, description: '是否推荐' })
  @ApiQuery({ name: 'search', required: false, description: '搜索关键词' })
  async findAll(@Query() query: ReviewQueryDto): Promise<PaginatedResult<Review>> {
    return this.reviewsService.findAll(query);
  }

  @Get('stats/:userId')
  @ApiOperation({ summary: '获取用户评价统计' })
  @ApiResponse({
    status: 200,
    description: '获取统计信息成功',
  })
  @ApiParam({ name: 'userId', description: '用户ID' })
  async getUserStats(@Param('userId') userId: string): Promise<any> {
    return this.reviewsService.getUserStats(userId);
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID获取评价详情' })
  @ApiResponse({
    status: 200,
    description: '获取评价详情成功',
    type: Review,
  })
  @ApiResponse({
    status: 404,
    description: '评价不存在',
  })
  @ApiParam({ name: 'id', description: '评价ID' })
  async findOne(@Param('id') id: string): Promise<Review> {
    return this.reviewsService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新评价' })
  @ApiResponse({
    status: 200,
    description: '评价更新成功',
    type: Review,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '评价不存在',
  })
  @ApiParam({ name: 'id', description: '评价ID' })
  async update(
    @Param('id') id: string,
    @Body() updateReviewDto: UpdateReviewDto,
    @Request() req: any,
  ): Promise<Review> {
    return this.reviewsService.update(id, updateReviewDto, req.user);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除评价' })
  @ApiResponse({
    status: 204,
    description: '评价删除成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '评价不存在',
  })
  @ApiParam({ name: 'id', description: '评价ID' })
  async remove(@Param('id') id: string, @Request() req: any): Promise<void> {
    return this.reviewsService.remove(id, req.user);
  }

  @Post(':id/response')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '回复评价' })
  @ApiResponse({
    status: 201,
    description: '回复成功',
    type: Review,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '评价不存在',
  })
  @ApiParam({ name: 'id', description: '评价ID' })
  async addResponse(
    @Param('id') id: string,
    @Body() createResponseDto: CreateReviewResponseDto,
    @Request() req: any,
  ): Promise<Review> {
    return this.reviewsService.addResponse(id, createResponseDto, req.user);
  }

  @Post(':id/like')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '点赞评价' })
  @ApiResponse({
    status: 200,
    description: '点赞成功',
    type: Review,
  })
  @ApiResponse({
    status: 404,
    description: '评价不存在',
  })
  @ApiParam({ name: 'id', description: '评价ID' })
  async likeReview(@Param('id') id: string, @Request() req: any): Promise<Review> {
    return this.reviewsService.likeReview(id, req.user);
  }

  @Post(':id/helpful')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '标记评价为有用' })
  @ApiResponse({
    status: 200,
    description: '标记成功',
    type: Review,
  })
  @ApiResponse({
    status: 404,
    description: '评价不存在',
  })
  @ApiParam({ name: 'id', description: '评价ID' })
  async markHelpful(@Param('id') id: string, @Request() req: any): Promise<Review> {
    return this.reviewsService.markHelpful(id, req.user);
  }

  @Patch(':id/moderate')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserType.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: '审核评价（管理员）' })
  @ApiResponse({
    status: 200,
    description: '审核成功',
    type: Review,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '评价不存在',
  })
  @ApiParam({ name: 'id', description: '评价ID' })
  async moderate(
    @Param('id') id: string,
    @Body() moderationDto: ReviewModerationDto,
    @Request() req: any,
  ): Promise<Review> {
    return this.reviewsService.moderate(id, moderationDto, req.user);
  }
}
