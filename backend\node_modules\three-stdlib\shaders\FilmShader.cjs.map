{"version": 3, "file": "FilmShader.cjs", "sources": ["../../src/shaders/FilmShader.ts"], "sourcesContent": ["/**\n * Film grain & scanlines shader\n *\n * - ported from HLSL to WebGL / GLSL\n * http://www.truevision3d.com/forums/showcase/staticnoise_colorblackwhite_scanline_shaders-t18698.0.html\n *\n * Screen Space Static Postprocessor\n *\n * Produces an analogue noise overlay similar to a film grain / TV static\n *\n * Original implementation and noise algorithm\n * Pat 'Hawthorne' Shearon\n *\n * Optimized scanlines + noise version with intensity scaling\n * Georg '<PERSON><PERSON>' <PERSON>\n *\n * This version is provided under a Creative Commons Attribution 3.0 License\n * http://creativecommons.org/licenses/by/3.0/\n */\n\nexport const FilmShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    time: { value: 0.0 },\n    nIntensity: { value: 0.5 },\n    sIntensity: { value: 0.05 },\n    sCount: { value: 4096 },\n    grayscale: { value: 1 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    #include <common>\n\n    // control parameter\n    uniform float time;\n\n    uniform bool grayscale;\n\n    // noise effect intensity value (0 = no effect, 1 = full effect)\n    uniform float nIntensity;\n\n    // scanlines effect intensity value (0 = no effect, 1 = full effect)\n    uniform float sIntensity;\n\n    // scanlines effect count value (0 = no effect, 4096 = full effect)\n    uniform float sCount;\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    // sample the source\n    \tvec4 cTextureScreen = texture2D( tDiffuse, vUv );\n\n    // make some noise\n    \tfloat dx = rand( vUv + time );\n\n    // add noise\n    \tvec3 cResult = cTextureScreen.rgb + cTextureScreen.rgb * clamp( 0.1 + dx, 0.0, 1.0 );\n\n    // get us a sine and cosine\n    \tvec2 sc = vec2( sin( vUv.y * sCount ), cos( vUv.y * sCount ) );\n\n    // add scanlines\n    \tcResult += cTextureScreen.rgb * vec3( sc.x, sc.y, sc.x ) * sIntensity;\n\n    // interpolate between source and result by intensity\n    \tcResult = cTextureScreen.rgb + clamp( nIntensity, 0.0,1.0 ) * ( cResult - cTextureScreen.rgb );\n\n    // convert to grayscale if desired\n    \tif( grayscale ) {\n\n    \t\tcResult = vec3( cResult.r * 0.3 + cResult.g * 0.59 + cResult.b * 0.11 );\n\n    \t}\n\n    \tgl_FragColor =  vec4( cResult, cTextureScreen.a );\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";;AAoBO,MAAM,aAAa;AAAA,EACxB,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,MAAM,EAAE,OAAO,EAAI;AAAA,IACnB,YAAY,EAAE,OAAO,IAAI;AAAA,IACzB,YAAY,EAAE,OAAO,KAAK;AAAA,IAC1B,QAAQ,EAAE,OAAO,KAAK;AAAA,IACtB,WAAW,EAAE,OAAO,EAAE;AAAA,EACxB;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoD7B;;"}