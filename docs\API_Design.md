# HomeReno API 设计文档

## 1. API 设计原则

### 1.1 RESTful 设计
- 使用标准HTTP方法 (GET, POST, PUT, DELETE)
- 资源导向的URL设计
- 统一的响应格式
- 适当的HTTP状态码

### 1.2 版本控制
- URL路径版本控制: `/api/v1/`
- 向后兼容性保证
- 废弃API的优雅处理

### 1.3 安全性
- JWT Token认证
- API限流
- 请求签名验证
- HTTPS强制使用

## 2. 通用响应格式

### 2.1 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2023-07-03T10:00:00Z"
}
```

### 2.2 错误响应
```json
{
  "success": false,
  "error": {
    "code": "USER_NOT_FOUND",
    "message": "用户不存在",
    "details": {}
  },
  "timestamp": "2023-07-03T10:00:00Z"
}
```

### 2.3 分页响应
```json
{
  "success": true,
  "data": {
    "items": [],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5
    }
  }
}
```

## 3. 认证与授权 API

### 3.1 用户注册
```
POST /api/v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "userType": "homeowner", // homeowner | service_provider
  "profile": {
    "firstName": "John",
    "lastName": "Doe",
    "phone": "+**********",
    "language": "en" // en | zh
  }
}
```

### 3.2 用户登录
```
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

Response:
{
  "success": true,
  "data": {
    "token": "jwt_token_here",
    "refreshToken": "refresh_token_here",
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "userType": "homeowner",
      "profile": {}
    }
  }
}
```

### 3.3 刷新Token
```
POST /api/v1/auth/refresh
Authorization: Bearer refresh_token

Response:
{
  "success": true,
  "data": {
    "token": "new_jwt_token",
    "refreshToken": "new_refresh_token"
  }
}
```

## 4. 用户管理 API

### 4.1 获取用户信息
```
GET /api/v1/users/profile
Authorization: Bearer jwt_token

Response:
{
  "success": true,
  "data": {
    "id": "user_id",
    "email": "<EMAIL>",
    "userType": "homeowner",
    "profile": {
      "firstName": "John",
      "lastName": "Doe",
      "phone": "+**********",
      "address": {},
      "language": "en",
      "avatar": "avatar_url"
    },
    "preferences": {},
    "createdAt": "2023-07-01T00:00:00Z"
  }
}
```

### 4.2 更新用户信息
```
PUT /api/v1/users/profile
Authorization: Bearer jwt_token
Content-Type: application/json

{
  "profile": {
    "firstName": "John",
    "lastName": "Smith",
    "phone": "+**********",
    "address": {
      "street": "123 Main St",
      "city": "Toronto",
      "province": "ON",
      "postalCode": "M5V 3A8"
    }
  }
}
```

## 5. 项目管理 API

### 5.1 创建项目需求
```
POST /api/v1/projects
Authorization: Bearer jwt_token
Content-Type: application/json

{
  "title": "全屋装修",
  "description": "新房全屋装修，现代简约风格",
  "category": "full_renovation", // full_renovation | kitchen | bathroom | etc.
  "budget": {
    "min": 50000,
    "max": 80000,
    "currency": "CAD"
  },
  "timeline": {
    "startDate": "2023-08-01",
    "endDate": "2023-10-31"
  },
  "location": {
    "address": "123 Main St, Toronto, ON",
    "coordinates": {
      "lat": 43.6532,
      "lng": -79.3832
    }
  },
  "requirements": {
    "area": 1200,
    "rooms": 3,
    "style": "modern",
    "specialRequirements": []
  },
  "attachments": ["image_url_1", "image_url_2"]
}
```

### 5.2 获取项目列表
```
GET /api/v1/projects?page=1&limit=20&status=active&category=kitchen
Authorization: Bearer jwt_token

Response:
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "project_id",
        "title": "厨房改造",
        "status": "active", // draft | active | in_progress | completed | cancelled
        "budget": {},
        "timeline": {},
        "location": {},
        "createdAt": "2023-07-01T00:00:00Z",
        "updatedAt": "2023-07-01T00:00:00Z"
      }
    ],
    "pagination": {}
  }
}
```

### 5.3 获取项目详情
```
GET /api/v1/projects/{projectId}
Authorization: Bearer jwt_token

Response:
{
  "success": true,
  "data": {
    "id": "project_id",
    "title": "全屋装修",
    "description": "详细描述",
    "status": "active",
    "owner": {
      "id": "user_id",
      "name": "John Doe",
      "avatar": "avatar_url"
    },
    "budget": {},
    "timeline": {},
    "location": {},
    "requirements": {},
    "attachments": [],
    "bids": [],
    "selectedProvider": null,
    "createdAt": "2023-07-01T00:00:00Z"
  }
}
```

## 6. 服务商匹配 API

### 6.1 搜索服务商
```
GET /api/v1/service-providers/search?
  category=renovation&
  location=Toronto&
  radius=50&
  minRating=4.0&
  page=1&limit=20
Authorization: Bearer jwt_token

Response:
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "provider_id",
        "businessName": "ABC Renovation",
        "rating": 4.8,
        "reviewCount": 156,
        "categories": ["renovation", "kitchen", "bathroom"],
        "location": {
          "city": "Toronto",
          "province": "ON"
        },
        "priceRange": "$$",
        "portfolio": ["image_url_1", "image_url_2"],
        "isVerified": true,
        "responseTime": "2小时内",
        "distance": 15.5
      }
    ],
    "pagination": {}
  }
}
```

### 6.2 获取服务商详情
```
GET /api/v1/service-providers/{providerId}
Authorization: Bearer jwt_token

Response:
{
  "success": true,
  "data": {
    "id": "provider_id",
    "businessName": "ABC Renovation",
    "description": "专业装修公司，10年经验",
    "owner": {
      "name": "Mike Johnson",
      "phone": "+**********",
      "email": "<EMAIL>"
    },
    "rating": 4.8,
    "reviewCount": 156,
    "categories": [],
    "services": [],
    "location": {},
    "priceRange": "$$",
    "portfolio": [],
    "certifications": [],
    "insurance": {},
    "availability": {},
    "reviews": []
  }
}
```

### 6.3 发送项目咨询
```
POST /api/v1/projects/{projectId}/inquiries
Authorization: Bearer jwt_token
Content-Type: application/json

{
  "providerId": "provider_id",
  "message": "我对您的服务感兴趣，请联系我讨论项目详情。",
  "attachments": []
}
```

## 7. 报价与合同 API

### 7.1 创建报价
```
POST /api/v1/projects/{projectId}/quotes
Authorization: Bearer jwt_token (service provider)
Content-Type: application/json

{
  "items": [
    {
      "category": "材料费",
      "description": "地板、瓷砖等",
      "quantity": 1,
      "unitPrice": 15000,
      "total": 15000
    },
    {
      "category": "人工费",
      "description": "施工人工费用",
      "quantity": 1,
      "unitPrice": 20000,
      "total": 20000
    }
  ],
  "subtotal": 35000,
  "tax": 4550,
  "total": 39550,
  "currency": "CAD",
  "validUntil": "2023-08-15",
  "timeline": {
    "startDate": "2023-08-01",
    "duration": 60,
    "milestones": []
  },
  "terms": "报价条款和条件",
  "notes": "备注信息"
}
```

### 7.2 接受报价
```
POST /api/v1/quotes/{quoteId}/accept
Authorization: Bearer jwt_token (homeowner)
Content-Type: application/json

{
  "paymentSchedule": [
    {
      "milestone": "开工",
      "percentage": 30,
      "amount": 11865
    },
    {
      "milestone": "中期验收",
      "percentage": 40,
      "amount": 15820
    },
    {
      "milestone": "完工验收",
      "percentage": 30,
      "amount": 11865
    }
  ],
  "additionalTerms": "额外条款"
}
```

## 8. 支付 API

### 8.1 创建支付
```
POST /api/v1/payments
Authorization: Bearer jwt_token
Content-Type: application/json

{
  "projectId": "project_id",
  "amount": 11865,
  "currency": "CAD",
  "milestone": "开工",
  "paymentMethod": "credit_card",
  "description": "项目首期款"
}

Response:
{
  "success": true,
  "data": {
    "paymentId": "payment_id",
    "clientSecret": "pi_xxx_secret_xxx",
    "status": "requires_payment_method"
  }
}
```

### 8.2 确认支付
```
POST /api/v1/payments/{paymentId}/confirm
Authorization: Bearer jwt_token
Content-Type: application/json

{
  "paymentMethodId": "pm_xxx"
}
```

### 8.3 获取支付历史
```
GET /api/v1/payments?projectId=project_id&page=1&limit=20
Authorization: Bearer jwt_token

Response:
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "payment_id",
        "amount": 11865,
        "currency": "CAD",
        "status": "succeeded",
        "milestone": "开工",
        "createdAt": "2023-07-01T00:00:00Z",
        "paidAt": "2023-07-01T00:05:00Z"
      }
    ],
    "pagination": {}
  }
}
```

## 9. 文件上传 API

### 9.1 获取上传URL
```
POST /api/v1/files/upload-url
Authorization: Bearer jwt_token
Content-Type: application/json

{
  "fileName": "floor_plan.jpg",
  "fileType": "image/jpeg",
  "fileSize": 1024000,
  "category": "project_attachment" // project_attachment | portfolio | avatar | etc.
}

Response:
{
  "success": true,
  "data": {
    "uploadUrl": "https://s3.amazonaws.com/bucket/key?signature=xxx",
    "fileId": "file_id",
    "publicUrl": "https://cdn.example.com/files/file_id"
  }
}
```

### 9.2 确认上传完成
```
POST /api/v1/files/{fileId}/confirm
Authorization: Bearer jwt_token
```

## 10. 通知 API

### 10.1 获取通知列表
```
GET /api/v1/notifications?page=1&limit=20&unreadOnly=true
Authorization: Bearer jwt_token

Response:
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "notification_id",
        "type": "new_quote",
        "title": "收到新报价",
        "message": "ABC装修公司为您的项目提交了报价",
        "data": {
          "projectId": "project_id",
          "quoteId": "quote_id"
        },
        "isRead": false,
        "createdAt": "2023-07-01T00:00:00Z"
      }
    ],
    "pagination": {},
    "unreadCount": 5
  }
}
```

### 10.2 标记通知已读
```
PUT /api/v1/notifications/{notificationId}/read
Authorization: Bearer jwt_token
```

## 11. 错误码定义

| 错误码 | HTTP状态码 | 描述 |
|--------|-----------|------|
| INVALID_REQUEST | 400 | 请求参数无效 |
| UNAUTHORIZED | 401 | 未授权访问 |
| FORBIDDEN | 403 | 禁止访问 |
| NOT_FOUND | 404 | 资源不存在 |
| CONFLICT | 409 | 资源冲突 |
| RATE_LIMITED | 429 | 请求频率超限 |
| INTERNAL_ERROR | 500 | 服务器内部错误 |
| SERVICE_UNAVAILABLE | 503 | 服务不可用 |

## 12. API限流策略

- 认证用户：1000请求/小时
- 未认证用户：100请求/小时
- 文件上传：50请求/小时
- 支付相关：20请求/小时
