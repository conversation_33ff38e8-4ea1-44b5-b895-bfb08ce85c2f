import {
  IsString,
  IsEnum,
  IsOptional,
  IsBoolean,
  IsArray,
  IsMongoId,
  IsNumber,
  ValidateNested,
  <PERSON><PERSON>ength,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ReviewType, ReviewStatus } from '../schemas/review.schema';

export class CreateReviewCriteriaDto {
  @ApiProperty({ description: '评价维度名称' })
  @IsString()
  @MinLength(1)
  @MaxLength(50)
  name: string;

  @ApiProperty({ description: '评分(1-5)', minimum: 1, maximum: 5 })
  @IsNumber()
  @Min(1)
  @Max(5)
  rating: number;

  @ApiPropertyOptional({ description: '评价说明' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  comment?: string;
}

export class CreateReviewDto {
  @ApiProperty({ description: '评价类型', enum: ReviewType })
  @IsEnum(ReviewType)
  type: ReviewType;

  @ApiProperty({ description: '被评价者ID' })
  @IsMongoId()
  revieweeId: string;

  @ApiPropertyOptional({ description: '关联项目ID' })
  @IsOptional()
  @IsMongoId()
  projectId?: string;

  @ApiPropertyOptional({ description: '关联服务商ID' })
  @IsOptional()
  @IsMongoId()
  serviceProviderId?: string;

  @ApiProperty({ description: '总体评分(1-5)', minimum: 1, maximum: 5 })
  @IsNumber()
  @Min(1)
  @Max(5)
  overallRating: number;

  @ApiPropertyOptional({ description: '分项评价', type: [CreateReviewCriteriaDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateReviewCriteriaDto)
  criteria?: CreateReviewCriteriaDto[];

  @ApiProperty({ description: '评价标题' })
  @IsString()
  @MinLength(1)
  @MaxLength(200)
  title: string;

  @ApiProperty({ description: '评价内容' })
  @IsString()
  @MinLength(10)
  @MaxLength(2000)
  content: string;

  @ApiPropertyOptional({ description: '评价图片', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  images?: string[];

  @ApiPropertyOptional({ description: '评价标签', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: '是否推荐', default: true })
  @IsOptional()
  @IsBoolean()
  isRecommended?: boolean = true;

  @ApiPropertyOptional({ description: '是否匿名', default: false })
  @IsOptional()
  @IsBoolean()
  isAnonymous?: boolean = false;
}

export class UpdateReviewDto {
  @ApiPropertyOptional({ description: '总体评分(1-5)', minimum: 1, maximum: 5 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(5)
  overallRating?: number;

  @ApiPropertyOptional({ description: '分项评价', type: [CreateReviewCriteriaDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateReviewCriteriaDto)
  criteria?: CreateReviewCriteriaDto[];

  @ApiPropertyOptional({ description: '评价标题' })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(200)
  title?: string;

  @ApiPropertyOptional({ description: '评价内容' })
  @IsOptional()
  @IsString()
  @MinLength(10)
  @MaxLength(2000)
  content?: string;

  @ApiPropertyOptional({ description: '评价图片', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  images?: string[];

  @ApiPropertyOptional({ description: '评价标签', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: '是否推荐' })
  @IsOptional()
  @IsBoolean()
  isRecommended?: boolean;

  @ApiPropertyOptional({ description: '是否匿名' })
  @IsOptional()
  @IsBoolean()
  isAnonymous?: boolean;
}

export class ReviewQueryDto {
  @ApiPropertyOptional({ description: '页码', default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', default: 20 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number = 20;

  @ApiPropertyOptional({ description: '评价类型', enum: ReviewType })
  @IsOptional()
  @IsEnum(ReviewType)
  type?: ReviewType;

  @ApiPropertyOptional({ description: '评价状态', enum: ReviewStatus })
  @IsOptional()
  @IsEnum(ReviewStatus)
  status?: ReviewStatus;

  @ApiPropertyOptional({ description: '被评价者ID' })
  @IsOptional()
  @IsMongoId()
  revieweeId?: string;

  @ApiPropertyOptional({ description: '项目ID' })
  @IsOptional()
  @IsMongoId()
  projectId?: string;

  @ApiPropertyOptional({ description: '服务商ID' })
  @IsOptional()
  @IsMongoId()
  serviceProviderId?: string;

  @ApiPropertyOptional({ description: '最低评分' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(5)
  minRating?: number;

  @ApiPropertyOptional({ description: '最高评分' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(5)
  maxRating?: number;

  @ApiPropertyOptional({ description: '是否推荐' })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  isRecommended?: boolean;

  @ApiPropertyOptional({ description: '搜索关键词' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: '排序字段', enum: ['createdAt', 'overallRating', 'likeCount', 'helpfulCount'] })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ description: '排序方向', enum: ['asc', 'desc'] })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class CreateReviewResponseDto {
  @ApiProperty({ description: '回复内容' })
  @IsString()
  @MinLength(1)
  @MaxLength(1000)
  content: string;
}

export class ReviewModerationDto {
  @ApiProperty({ description: '审核状态', enum: ReviewStatus })
  @IsEnum(ReviewStatus)
  status: ReviewStatus;

  @ApiPropertyOptional({ description: '审核备注' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  moderationNote?: string;
}
