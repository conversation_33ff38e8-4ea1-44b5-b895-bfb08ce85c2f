{"version": 3, "file": "CompressedArrayTexture.js", "sources": ["../../src/_polyfill/CompressedArrayTexture.js"], "sourcesContent": ["import { CompressedTexture, ClampToEdgeWrapping } from 'three'\n\nclass CompressedArrayTexture extends CompressedTexture {\n  constructor(mipmaps, width, height, depth, format, type) {\n    super(mipmaps, width, height, format, type)\n    this.isCompressedArrayTexture = true\n    this.image.depth = depth\n    this.wrapR = ClampToEdgeWrapping\n  }\n}\n\nexport { CompressedArrayTexture }\n"], "names": [], "mappings": ";AAEA,MAAM,+BAA+B,kBAAkB;AAAA,EACrD,YAAY,SAAS,OAAO,QAAQ,OAAO,QAAQ,MAAM;AACvD,UAAM,SAAS,OAAO,QAAQ,QAAQ,IAAI;AAC1C,SAAK,2BAA2B;AAChC,SAAK,MAAM,QAAQ;AACnB,SAAK,QAAQ;AAAA,EACd;AACH;"}