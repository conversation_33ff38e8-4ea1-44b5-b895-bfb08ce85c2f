import React from 'react';
import { Outlet } from 'react-router-dom';
import { Layout, Typography, Button, Space } from 'antd';

const { Header, Content } = Layout;
const { Title } = Typography;

const SimpleLayout: React.FC = () => {
  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ 
        background: '#fff', 
        padding: '0 24px',
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
          HomeReno
        </Title>
        <Space>
          <Button>Login</Button>
          <Button type="primary">Register</Button>
        </Space>
      </Header>
      
      <Content style={{ padding: '24px' }}>
        <Outlet />
      </Content>
    </Layout>
  );
};

export default SimpleLayout;
