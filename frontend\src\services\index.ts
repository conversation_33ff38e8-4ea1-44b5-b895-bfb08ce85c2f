// 导出所有API服务
export * from './api';
export * from './authService';
export * from './projectService';
export * from './contractorService';

// 导出默认的API客户端
export { default as apiClient } from './api';

// 服务健康检查
export const healthCheck = async (): Promise<boolean> => {
  try {
    const { api } = await import('./api');
    const response = await api.get('/health');
    return response.success;
  } catch (error) {
    console.error('Health check failed:', error);
    return false;
  }
};

// API配置
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  TIMEOUT: 15000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};

// 错误代码映射
export const ERROR_CODES = {
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  VALIDATION_ERROR: 422,
  SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

// 成功状态码
export const SUCCESS_CODES = {
  OK: 200,
  CREATED: 201,
  ACCEPTED: 202,
  NO_CONTENT: 204,
} as const;
