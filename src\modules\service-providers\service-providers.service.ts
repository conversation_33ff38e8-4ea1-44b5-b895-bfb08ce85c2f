import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  ServiceProvider,
  ServiceProviderDocument,
  ServiceProviderStatus,
} from './schemas/service-provider.schema';
import { CreateServiceProviderDto } from './dto/create-service-provider.dto';
import { UpdateServiceProviderDto } from './dto/update-service-provider.dto';
import { ServiceProviderQueryDto } from './dto/service-provider-query.dto';
import { PaginatedResult } from '../../common/interfaces/paginated-result.interface';
import { User, UserType } from '../users/schemas/user.schema';

@Injectable()
export class ServiceProvidersService {
  constructor(
    @InjectModel(ServiceProvider.name)
    private serviceProviderModel: Model<ServiceProviderDocument>,
  ) {}

  /**
   * 创建服务商申请
   */
  async create(
    createServiceProviderDto: CreateServiceProviderDto,
    user: User,
  ): Promise<ServiceProvider> {
    // 检查用户是否已经是服务商
    const existingProvider = await this.serviceProviderModel.findOne({
      userId: user._id,
    });

    if (existingProvider) {
      throw new ConflictException('User is already registered as a service provider');
    }

    // 验证服务区域和服务类别不为空
    if (!createServiceProviderDto.serviceAreas.length) {
      throw new BadRequestException('At least one service area is required');
    }

    if (!createServiceProviderDto.services.length) {
      throw new BadRequestException('At least one service is required');
    }

    const serviceProviderData = {
      ...createServiceProviderDto,
      userId: user._id,
      status: ServiceProviderStatus.PENDING,
      rating: {
        overall: 0,
        quality: 0,
        timeliness: 0,
        communication: 0,
        value: 0,
        totalReviews: 0,
        totalRating: 0,
      },
      stats: {
        projectsCompleted: 0,
        responseTime: 0,
        onTimeRate: 0,
        repeatCustomerRate: 0,
      },
    };

    const createdProvider = new this.serviceProviderModel(serviceProviderData);
    return createdProvider.save();
  }

  /**
   * 获取服务商列表（分页）
   */
  async findAll(query: ServiceProviderQueryDto): Promise<PaginatedResult<ServiceProvider>> {
    const {
      page = 1,
      limit = 20,
      status,
      category,
      province,
      city,
      minRating,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      lat,
      lng,
      radius = 50,
    } = query;

    const filter: any = {};

    // 状态过滤
    if (status) {
      filter.status = status;
    } else {
      // 默认只显示已审核通过的服务商
      filter.status = ServiceProviderStatus.APPROVED;
    }

    // 服务类别过滤
    if (category) {
      filter['services.category'] = category;
    }

    // 地理位置过滤
    if (province) {
      filter['serviceAreas.province'] = new RegExp(province, 'i');
    }
    if (city) {
      filter['serviceAreas.cities'] = new RegExp(city, 'i');
    }

    // 评分过滤
    if (minRating !== undefined) {
      filter['rating.overall'] = { $gte: minRating };
    }

    // 关键词搜索
    if (search) {
      filter.$or = [
        { 'businessInfo.companyName': new RegExp(search, 'i') },
        { 'businessInfo.description': new RegExp(search, 'i') },
        { 'services.name': new RegExp(search, 'i') },
        { 'services.description': new RegExp(search, 'i') },
      ];
    }

    // 地理位置搜索（基于服务区域）
    if (lat && lng && province) {
      // 这里可以实现更复杂的地理位置匹配逻辑
      // 暂时使用省份匹配
    }

    // 排序
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [items, total] = await Promise.all([
      this.serviceProviderModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('userId', 'profile.firstName profile.lastName profile.avatar email')
        .exec(),
      this.serviceProviderModel.countDocuments(filter),
    ]);

    return {
      items,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * 根据ID获取服务商详情
   */
  async findOne(id: string): Promise<ServiceProvider> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid service provider ID');
    }

    const serviceProvider = await this.serviceProviderModel
      .findById(id)
      .populate('userId', 'profile.firstName profile.lastName profile.avatar email profile.phone')
      .populate('reviewedBy', 'profile.firstName profile.lastName')
      .exec();

    if (!serviceProvider) {
      throw new NotFoundException('Service provider not found');
    }

    return serviceProvider;
  }

  /**
   * 根据用户ID获取服务商信息
   */
  async findByUserId(userId: string): Promise<ServiceProvider> {
    if (!Types.ObjectId.isValid(userId)) {
      throw new BadRequestException('Invalid user ID');
    }

    const serviceProvider = await this.serviceProviderModel
      .findOne({ userId })
      .populate('userId', 'profile.firstName profile.lastName profile.avatar email profile.phone')
      .exec();

    if (!serviceProvider) {
      throw new NotFoundException('Service provider not found');
    }

    return serviceProvider;
  }

  /**
   * 更新服务商信息
   */
  async update(
    id: string,
    updateServiceProviderDto: UpdateServiceProviderDto,
    user: User,
  ): Promise<ServiceProvider> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid service provider ID');
    }

    const serviceProvider = await this.serviceProviderModel.findById(id);
    if (!serviceProvider) {
      throw new NotFoundException('Service provider not found');
    }

    // 检查权限
    if (serviceProvider.userId.toString() !== user._id.toString()) {
      throw new ForbiddenException('You can only update your own service provider profile');
    }

    // 如果服务商已被审核通过，某些字段的修改需要重新审核
    if (serviceProvider.status === ServiceProviderStatus.APPROVED) {
      const sensitiveFields = ['businessInfo', 'services', 'certifications', 'insurance'];
      const hasSensitiveChanges = sensitiveFields.some(field => 
        updateServiceProviderDto[field] !== undefined
      );

      if (hasSensitiveChanges) {
        updateServiceProviderDto['status'] = ServiceProviderStatus.PENDING;
      }
    }

    const updatedProvider = await this.serviceProviderModel
      .findByIdAndUpdate(id, updateServiceProviderDto, { new: true })
      .populate('userId', 'profile.firstName profile.lastName profile.avatar email')
      .exec();

    return updatedProvider;
  }

  /**
   * 删除服务商申请
   */
  async remove(id: string, user: User): Promise<void> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid service provider ID');
    }

    const serviceProvider = await this.serviceProviderModel.findById(id);
    if (!serviceProvider) {
      throw new NotFoundException('Service provider not found');
    }

    // 检查权限
    if (serviceProvider.userId.toString() !== user._id.toString()) {
      throw new ForbiddenException('You can only delete your own service provider profile');
    }

    // 检查是否有进行中的项目
    if (serviceProvider.status === ServiceProviderStatus.APPROVED) {
      // 这里应该检查是否有进行中的项目，暂时跳过
      // throw new BadRequestException('Cannot delete active service provider with ongoing projects');
    }

    await this.serviceProviderModel.findByIdAndDelete(id);
  }

  /**
   * 审核服务商申请（管理员）
   */
  async review(
    id: string,
    status: ServiceProviderStatus,
    reviewNotes: string,
    adminUser: User,
  ): Promise<ServiceProvider> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid service provider ID');
    }

    if (adminUser.userType !== UserType.ADMIN) {
      throw new ForbiddenException('Only administrators can review service provider applications');
    }

    const serviceProvider = await this.serviceProviderModel.findById(id);
    if (!serviceProvider) {
      throw new NotFoundException('Service provider not found');
    }

    if (serviceProvider.status !== ServiceProviderStatus.PENDING) {
      throw new BadRequestException('Only pending applications can be reviewed');
    }

    const updatedProvider = await this.serviceProviderModel
      .findByIdAndUpdate(
        id,
        {
          status,
          reviewNotes,
          reviewedBy: adminUser._id,
          reviewedAt: new Date(),
        },
        { new: true }
      )
      .populate('userId', 'profile.firstName profile.lastName profile.avatar email')
      .populate('reviewedBy', 'profile.firstName profile.lastName')
      .exec();

    return updatedProvider;
  }

  /**
   * 获取服务商统计信息
   */
  async getStats(): Promise<any> {
    const stats = await this.serviceProviderModel.aggregate([
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          pending: { $sum: { $cond: [{ $eq: ['$status', ServiceProviderStatus.PENDING] }, 1, 0] } },
          approved: { $sum: { $cond: [{ $eq: ['$status', ServiceProviderStatus.APPROVED] }, 1, 0] } },
          suspended: { $sum: { $cond: [{ $eq: ['$status', ServiceProviderStatus.SUSPENDED] }, 1, 0] } },
          rejected: { $sum: { $cond: [{ $eq: ['$status', ServiceProviderStatus.REJECTED] }, 1, 0] } },
          avgRating: { $avg: '$rating.overall' },
          totalProjects: { $sum: '$stats.projectsCompleted' },
        },
      },
    ]);

    return stats[0] || {
      total: 0,
      pending: 0,
      approved: 0,
      suspended: 0,
      rejected: 0,
      avgRating: 0,
      totalProjects: 0,
    };
  }

  /**
   * 匹配项目的服务商
   */
  async matchProvidersForProject(
    projectCategory: string,
    projectLocation: { province: string; city: string },
    projectBudget: { min: number; max: number },
    limit: number = 10,
  ): Promise<ServiceProvider[]> {
    const filter: any = {
      status: ServiceProviderStatus.APPROVED,
      'services.category': projectCategory,
      'serviceAreas.province': projectLocation.province,
      'serviceAreas.cities': projectLocation.city,
    };

    return this.serviceProviderModel
      .find(filter)
      .sort({ 'rating.overall': -1, 'stats.projectsCompleted': -1 })
      .limit(limit)
      .populate('userId', 'profile.firstName profile.lastName profile.avatar')
      .exec();
  }
}
