# HomeReno Backend API

HomeReno 后端 API 服务，基于 NestJS 框架开发，提供完整的家装平台后端功能。

## 🚀 快速开始

### 环境要求
- Node.js >= 16.x
- MongoDB >= 4.4
- npm >= 8.x

### 安装依赖

```bash
npm install
```

### 环境配置

确保根目录有 `.env.development` 文件，包含以下配置：

```env
# 应用配置
NODE_ENV=development
PORT=3000

# 数据库配置
MONGODB_URI=your-mongodb-connection-string
USE_MOCK_DATABASE=false

# JWT配置
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-refresh-secret
JWT_REFRESH_EXPIRES_IN=7d

# 文件上传
MAX_FILE_SIZE=10485760
UPLOAD_DEST=uploads
```

### 启动开发服务器

```bash
npm run start:dev
```

### 访问应用

- **API服务**: http://localhost:3000
- **API文档**: http://localhost:3000/api
- **健康检查**: http://localhost:3000/health

## 📁 项目结构

```
src/
├── modules/           # 功能模块
│   ├── auth/         # 认证模块
│   ├── users/        # 用户管理
│   ├── projects/     # 项目管理
│   ├── service-providers/ # 服务商管理
│   ├── quotes/       # 报价管理
│   ├── files/        # 文件管理
│   ├── notifications/ # 通知系统
│   ├── reviews/      # 评价系统
│   ├── payments/     # 支付系统
│   ├── tasks/        # 任务管理
│   └── design-tools/ # 3D设计工具
├── shared/           # 共享模块
├── common/           # 通用工具
├── config/           # 配置文件
├── i18n/            # 国际化文件
├── app.module.ts    # 主应用模块
└── main.ts          # 应用入口
```

## 🔧 开发工具

### 代码质量
```bash
npm run lint          # ESLint 检查
npm run format        # Prettier 格式化
```

### 测试
```bash
npm run test          # 单元测试
npm run test:e2e      # 端到端测试
npm run test:cov      # 测试覆盖率
```

### 构建
```bash
npm run build         # 生产构建
npm run start:prod    # 生产环境启动
```

## 🌟 核心功能

### 用户管理
- 用户注册/登录
- JWT认证
- 角色权限管理
- 个人资料管理

### 项目管理
- 项目创建/编辑
- 项目状态跟踪
- 文件上传管理
- 项目匹配算法

### 服务商系统
- 服务商注册认证
- 技能和服务管理
- 作品集展示
- 评价系统

### 3D设计工具
- 3D场景管理
- 设计模板系统
- 设计资源管理
- Three.js 集成

### 多语言支持
- 中英文双语
- 动态语言切换
- 国际化资源管理

## 📊 数据库

### MongoDB 集合

- `users` - 用户信息
- `projects` - 项目数据
- `serviceproviders` - 服务商信息
- `quotes` - 报价数据
- `files` - 文件信息
- `notifications` - 通知记录
- `reviews` - 评价数据
- `payments` - 支付记录
- `tasks` - 任务数据
- `designtemplates` - 设计模板
- `designassets` - 设计资源

## 🔐 API 认证

使用 JWT Bearer Token 认证：

```bash
Authorization: Bearer <your-jwt-token>
```

## 📚 API 文档

启动应用后，访问 http://localhost:3000/api 查看完整的 Swagger API 文档。

## 🚀 部署

### Docker 部署

```bash
# 构建镜像
docker build -t homereno-backend .

# 运行容器
docker run -p 3000:3000 homereno-backend
```

## 🤝 贡献

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 打开 Pull Request
