import React, { useState } from 'react';
import { <PERSON>, Button, Space, Typography, Al<PERSON>, Divider, Row, Col, message } from 'antd';
import { 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  LoadingOutlined,
  ApiOutlined,
  UserOutlined,
  ProjectOutlined,
  TeamOutlined
} from '@ant-design/icons';
import { healthCheck, authService, projectService, contractorService } from '../services';

const { Title, Text, Paragraph } = Typography;

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message?: string;
  duration?: number;
}

const ApiTestPage: React.FC = () => {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);

  const updateResult = (name: string, status: 'success' | 'error', message?: string, duration?: number) => {
    setResults(prev => prev.map(result => 
      result.name === name 
        ? { ...result, status, message, duration }
        : result
    ));
  };

  const runTest = async (name: string, testFn: () => Promise<any>) => {
    const startTime = Date.now();
    try {
      await testFn();
      const duration = Date.now() - startTime;
      updateResult(name, 'success', 'Test passed', duration);
      return true;
    } catch (error: any) {
      const duration = Date.now() - startTime;
      updateResult(name, 'error', error.message || 'Test failed', duration);
      return false;
    }
  };

  const runAllTests = async () => {
    setTesting(true);
    
    // 初始化测试结果
    const testCases = [
      { name: 'Health Check', status: 'pending' as const },
      { name: 'Auth Service - Get Current User', status: 'pending' as const },
      { name: 'Project Service - Get Projects', status: 'pending' as const },
      { name: 'Contractor Service - Get Contractors', status: 'pending' as const },
    ];
    
    setResults(testCases);

    let passedTests = 0;
    const totalTests = testCases.length;

    // 测试1: 健康检查
    const healthPassed = await runTest('Health Check', async () => {
      const isHealthy = await healthCheck();
      if (!isHealthy) {
        throw new Error('Backend server is not responding');
      }
    });
    if (healthPassed) passedTests++;

    // 测试2: 认证服务
    const authPassed = await runTest('Auth Service - Get Current User', async () => {
      try {
        await authService.getCurrentUser();
      } catch (error: any) {
        // 401错误是预期的（未登录），其他错误才是真正的问题
        if (error.status === 401) {
          return; // 这是预期的行为
        }
        throw error;
      }
    });
    if (authPassed) passedTests++;

    // 测试3: 项目服务
    const projectPassed = await runTest('Project Service - Get Projects', async () => {
      await projectService.getProjects({ page: 1, limit: 5 });
    });
    if (projectPassed) passedTests++;

    // 测试4: 服务商服务
    const contractorPassed = await runTest('Contractor Service - Get Contractors', async () => {
      await contractorService.getContractors({ page: 1, limit: 5 });
    });
    if (contractorPassed) passedTests++;

    setTesting(false);

    // 显示测试结果摘要
    if (passedTests === totalTests) {
      message.success(`All ${totalTests} tests passed! API integration is working correctly.`);
    } else {
      message.warning(`${passedTests}/${totalTests} tests passed. Some API endpoints may need attention.`);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <LoadingOutlined style={{ color: '#1890ff' }} />;
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return '#1890ff';
      case 'success':
        return '#52c41a';
      case 'error':
        return '#ff4d4f';
    }
  };

  return (
    <div style={{ padding: '24px', maxWidth: 1200, margin: '0 auto' }}>
      <Card>
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <ApiOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
          <Title level={2}>API Integration Test</Title>
          <Paragraph>
            Test the connection and functionality of all API services to ensure proper integration
            between the frontend and backend systems.
          </Paragraph>
        </div>

        <Row gutter={[16, 16]} style={{ marginBottom: '32px' }}>
          <Col xs={24} sm={8}>
            <Card size="small" style={{ textAlign: 'center' }}>
              <UserOutlined style={{ fontSize: 24, color: '#1890ff', marginBottom: 8 }} />
              <div>Authentication API</div>
              <Text type="secondary">Login, Register, Profile</Text>
            </Card>
          </Col>
          <Col xs={24} sm={8}>
            <Card size="small" style={{ textAlign: 'center' }}>
              <ProjectOutlined style={{ fontSize: 24, color: '#52c41a', marginBottom: 8 }} />
              <div>Project API</div>
              <Text type="secondary">CRUD, Search, Management</Text>
            </Card>
          </Col>
          <Col xs={24} sm={8}>
            <Card size="small" style={{ textAlign: 'center' }}>
              <TeamOutlined style={{ fontSize: 24, color: '#faad14', marginBottom: 8 }} />
              <div>Contractor API</div>
              <Text type="secondary">Profiles, Search, Matching</Text>
            </Card>
          </Col>
        </Row>

        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <Button 
            type="primary" 
            size="large" 
            onClick={runAllTests}
            loading={testing}
            disabled={testing}
          >
            {testing ? 'Running Tests...' : 'Run API Tests'}
          </Button>
        </div>

        {results.length > 0 && (
          <>
            <Divider>Test Results</Divider>
            <Space direction="vertical" style={{ width: '100%' }}>
              {results.map((result, index) => (
                <Card 
                  key={index}
                  size="small"
                  style={{ 
                    borderLeft: `4px solid ${getStatusColor(result.status)}`,
                    backgroundColor: result.status === 'error' ? '#fff2f0' : 
                                   result.status === 'success' ? '#f6ffed' : '#f0f9ff'
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                      {getStatusIcon(result.status)}
                      <div>
                        <Text strong>{result.name}</Text>
                        {result.message && (
                          <div>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              {result.message}
                            </Text>
                          </div>
                        )}
                      </div>
                    </div>
                    {result.duration && (
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {result.duration}ms
                      </Text>
                    )}
                  </div>
                </Card>
              ))}
            </Space>
          </>
        )}

        {results.length > 0 && !testing && (
          <div style={{ marginTop: '24px' }}>
            <Alert
              message="Test Information"
              description={
                <div>
                  <p>• <strong>Health Check</strong>: Verifies backend server connectivity</p>
                  <p>• <strong>Auth Service</strong>: Tests authentication endpoints (401 errors are expected when not logged in)</p>
                  <p>• <strong>Project Service</strong>: Tests project listing and management APIs</p>
                  <p>• <strong>Contractor Service</strong>: Tests contractor search and profile APIs</p>
                </div>
              }
              type="info"
              showIcon
            />
          </div>
        )}
      </Card>
    </div>
  );
};

export default ApiTestPage;
