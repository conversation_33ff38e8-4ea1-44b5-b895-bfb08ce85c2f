import { Modu<PERSON> } from '@nestjs/common';
import { HealthController } from './health.controller';
import { DatabaseAdapterService } from '../../common/services/database-adapter.service';
import { MockDatabaseService } from '../../common/services/mock-database.service';

@Module({
  controllers: [HealthController],
  providers: [DatabaseAdapterService, MockDatabaseService],
  exports: [DatabaseAdapterService],
})
export class HealthModule {}
