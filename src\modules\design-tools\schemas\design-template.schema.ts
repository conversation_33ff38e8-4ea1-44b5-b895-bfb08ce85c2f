import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type DesignTemplateDocument = DesignTemplate & Document;

@Schema({ timestamps: true })
export class DesignTemplate {
  @ApiProperty({ description: '模板ID' })
  _id: Types.ObjectId;

  @ApiProperty({ description: '模板名称' })
  @Prop({ required: true, trim: true })
  name: string;

  @ApiProperty({ description: '模板描述' })
  @Prop({ trim: true })
  description?: string;

  @ApiProperty({ description: '模板类别' })
  @Prop({ 
    required: true,
    enum: ['room', 'furniture', 'layout', 'style', 'complete_design']
  })
  category: string;

  @ApiProperty({ description: '适用房屋类型' })
  @Prop([{ 
    type: String,
    enum: ['apartment', 'house', 'condo', 'townhouse', 'any']
  }])
  houseTypes: string[];

  @ApiProperty({ description: '适用房间类型' })
  @Prop([{ 
    type: String,
    enum: ['bedroom', 'bathroom', 'living_room', 'kitchen', 'dining_room', 'office', 'any']
  }])
  roomTypes: string[];

  @ApiProperty({ description: '设计风格' })
  @Prop({ 
    required: true,
    enum: ['modern', 'traditional', 'contemporary', 'industrial', 'scandinavian', 'minimalist', 'rustic', 'mixed']
  })
  style: string;

  @ApiProperty({ description: '预算范围' })
  @Prop({
    type: {
      min: { type: Number, min: 0 },
      max: { type: Number, min: 0 },
      currency: { type: String, default: 'CAD' }
    }
  })
  budgetRange?: {
    min: number;
    max: number;
    currency: string;
  };

  @ApiProperty({ description: '模板数据' })
  @Prop({
    type: {
      floorPlan: { type: String }, // 平面图模板数据
      scene3D: { type: String }, // 3D场景模板数据
      materials: [{
        type: {
          id: String,
          name: String,
          type: String, // 'wall', 'floor', 'ceiling', 'furniture'
          color: String,
          texture: String,
          properties: Object
        },
        _id: false
      }],
      furniture: [{
        type: {
          id: String,
          name: String,
          category: String,
          position: [Number],
          rotation: [Number],
          scale: [Number],
          modelUrl: String,
          thumbnailUrl: String
        },
        _id: false
      }],
      lighting: {
        type: {
          ambient: { type: Number, default: 0.4 },
          lights: [{
            type: {
              type: String, // 'directional', 'point', 'spot'
              position: [Number],
              intensity: Number,
              color: String
            },
            _id: false
          }]
        },
        default: {}
      }
    },
    required: true
  })
  templateData: {
    floorPlan?: string;
    scene3D?: string;
    materials: Array<{
      id: string;
      name: string;
      type: string;
      color: string;
      texture: string;
      properties: any;
    }>;
    furniture: Array<{
      id: string;
      name: string;
      category: string;
      position: number[];
      rotation: number[];
      scale: number[];
      modelUrl: string;
      thumbnailUrl: string;
    }>;
    lighting: {
      ambient: number;
      lights: Array<{
        type: string;
        position: number[];
        intensity: number;
        color: string;
      }>;
    };
  };

  @ApiProperty({ description: '预览图片' })
  @Prop([String])
  previewImages: string[];

  @ApiProperty({ description: '缩略图' })
  @Prop({ required: true })
  thumbnailUrl: string;

  @ApiProperty({ description: '使用次数' })
  @Prop({ default: 0, min: 0 })
  usageCount: number;

  @ApiProperty({ description: '评分' })
  @Prop({ 
    type: {
      average: { type: Number, min: 0, max: 5, default: 0 },
      count: { type: Number, min: 0, default: 0 }
    },
    default: {}
  })
  rating: {
    average: number;
    count: number;
  };

  @ApiProperty({ description: '标签' })
  @Prop([String])
  tags: string[];

  @ApiProperty({ description: '是否为官方模板' })
  @Prop({ default: false })
  isOfficial: boolean;

  @ApiProperty({ description: '是否免费' })
  @Prop({ default: true })
  isFree: boolean;

  @ApiProperty({ description: '价格(如果付费)' })
  @Prop({
    type: {
      amount: { type: Number, min: 0 },
      currency: { type: String, default: 'CAD' }
    }
  })
  price?: {
    amount: number;
    currency: string;
  };

  @ApiProperty({ description: '创建者ID' })
  @Prop({ type: Types.ObjectId, ref: 'User' })
  createdBy?: Types.ObjectId;

  @ApiProperty({ description: '模板状态' })
  @Prop({ 
    enum: ['draft', 'published', 'archived'],
    default: 'draft'
  })
  status: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export const DesignTemplateSchema = SchemaFactory.createForClass(DesignTemplate);

// 创建索引
DesignTemplateSchema.index({ category: 1, style: 1 });
DesignTemplateSchema.index({ status: 1, isOfficial: -1, 'rating.average': -1 });
DesignTemplateSchema.index({ houseTypes: 1, roomTypes: 1 });
DesignTemplateSchema.index({ tags: 1 });
DesignTemplateSchema.index({ usageCount: -1 });
