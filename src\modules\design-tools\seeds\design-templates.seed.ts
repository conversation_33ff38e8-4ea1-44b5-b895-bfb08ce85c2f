export const designTemplatesSeed = [
  {
    name: 'Modern Living Room',
    description: 'A contemporary living room design with clean lines and neutral colors',
    category: 'complete_design',
    houseTypes: ['apartment', 'condo', 'house'],
    roomTypes: ['living_room'],
    style: 'modern',
    budgetRange: {
      min: 5000,
      max: 15000,
      currency: 'CAD'
    },
    templateData: {
      floorPlan: JSON.stringify({
        walls: [
          { start: [0, 0], end: [6, 0], height: 3 },
          { start: [6, 0], end: [6, 4], height: 3 },
          { start: [6, 4], end: [0, 4], height: 3 },
          { start: [0, 4], end: [0, 0], height: 3 }
        ],
        doors: [
          { position: [3, 0], width: 0.8, type: 'entrance' }
        ],
        windows: [
          { position: [6, 2], width: 1.5, height: 1.2, type: 'standard' }
        ]
      }),
      scene3D: JSON.stringify({
        objects: [
          {
            id: 'floor',
            type: 'plane',
            position: [3, 0, 2],
            rotation: [-Math.PI/2, 0, 0],
            scale: [6, 4, 1],
            material: 'hardwood_oak'
          },
          {
            id: 'ceiling',
            type: 'plane',
            position: [3, 3, 2],
            rotation: [Math.PI/2, 0, 0],
            scale: [6, 4, 1],
            material: 'white_paint'
          }
        ]
      }),
      materials: [
        {
          id: 'hardwood_oak',
          name: 'Oak Hardwood Flooring',
          type: 'floor',
          color: '#D2B48C',
          texture: '/textures/hardwood_oak.jpg',
          properties: {
            roughness: 0.8,
            metalness: 0.0
          }
        },
        {
          id: 'white_paint',
          name: 'White Wall Paint',
          type: 'wall',
          color: '#FFFFFF',
          texture: null,
          properties: {
            roughness: 0.9,
            metalness: 0.0
          }
        }
      ],
      furniture: [
        {
          id: 'sofa_modern',
          name: 'Modern 3-Seat Sofa',
          category: 'sofa',
          position: [2, 0.4, 2],
          rotation: [0, 0, 0],
          scale: [1, 1, 1],
          modelUrl: '/models/furniture/sofa_modern.gltf',
          thumbnailUrl: '/images/furniture/sofa_modern_thumb.jpg'
        },
        {
          id: 'coffee_table',
          name: 'Glass Coffee Table',
          category: 'table',
          position: [3.5, 0.2, 2],
          rotation: [0, 0, 0],
          scale: [1, 1, 1],
          modelUrl: '/models/furniture/coffee_table_glass.gltf',
          thumbnailUrl: '/images/furniture/coffee_table_thumb.jpg'
        },
        {
          id: 'tv_stand',
          name: 'Modern TV Stand',
          category: 'cabinet',
          position: [5.8, 0.3, 2],
          rotation: [0, Math.PI, 0],
          scale: [1, 1, 1],
          modelUrl: '/models/furniture/tv_stand_modern.gltf',
          thumbnailUrl: '/images/furniture/tv_stand_thumb.jpg'
        }
      ],
      lighting: {
        ambient: 0.4,
        lights: [
          {
            type: 'directional',
            position: [10, 10, 5],
            intensity: 1.0,
            color: '#FFFFFF'
          },
          {
            type: 'point',
            position: [3, 2.5, 2],
            intensity: 0.8,
            color: '#FFF8DC'
          }
        ]
      }
    },
    previewImages: [
      '/images/templates/modern_living_room_1.jpg',
      '/images/templates/modern_living_room_2.jpg',
      '/images/templates/modern_living_room_3.jpg'
    ],
    thumbnailUrl: '/images/templates/modern_living_room_thumb.jpg',
    usageCount: 0,
    rating: {
      average: 0,
      count: 0
    },
    tags: ['modern', 'living room', 'neutral', 'minimalist'],
    isOfficial: true,
    isFree: true,
    status: 'published'
  },
  {
    name: 'Scandinavian Bedroom',
    description: 'A cozy Scandinavian-style bedroom with natural materials and soft colors',
    category: 'complete_design',
    houseTypes: ['apartment', 'condo', 'house'],
    roomTypes: ['bedroom'],
    style: 'scandinavian',
    budgetRange: {
      min: 3000,
      max: 8000,
      currency: 'CAD'
    },
    templateData: {
      floorPlan: JSON.stringify({
        walls: [
          { start: [0, 0], end: [4, 0], height: 3 },
          { start: [4, 0], end: [4, 3.5], height: 3 },
          { start: [4, 3.5], end: [0, 3.5], height: 3 },
          { start: [0, 3.5], end: [0, 0], height: 3 }
        ],
        doors: [
          { position: [2, 0], width: 0.8, type: 'interior' }
        ],
        windows: [
          { position: [4, 1.75], width: 1.2, height: 1.2, type: 'standard' }
        ]
      }),
      scene3D: JSON.stringify({
        objects: [
          {
            id: 'floor',
            type: 'plane',
            position: [2, 0, 1.75],
            rotation: [-Math.PI/2, 0, 0],
            scale: [4, 3.5, 1],
            material: 'light_wood'
          }
        ]
      }),
      materials: [
        {
          id: 'light_wood',
          name: 'Light Wood Flooring',
          type: 'floor',
          color: '#F5DEB3',
          texture: '/textures/light_wood.jpg',
          properties: {
            roughness: 0.7,
            metalness: 0.0
          }
        }
      ],
      furniture: [
        {
          id: 'bed_scandinavian',
          name: 'Scandinavian Platform Bed',
          category: 'bed',
          position: [2, 0.3, 2.5],
          rotation: [0, 0, 0],
          scale: [1, 1, 1],
          modelUrl: '/models/furniture/bed_scandinavian.gltf',
          thumbnailUrl: '/images/furniture/bed_scandinavian_thumb.jpg'
        },
        {
          id: 'nightstand',
          name: 'Wooden Nightstand',
          category: 'cabinet',
          position: [3.2, 0.25, 2.5],
          rotation: [0, 0, 0],
          scale: [1, 1, 1],
          modelUrl: '/models/furniture/nightstand_wood.gltf',
          thumbnailUrl: '/images/furniture/nightstand_thumb.jpg'
        }
      ],
      lighting: {
        ambient: 0.5,
        lights: [
          {
            type: 'directional',
            position: [8, 8, 4],
            intensity: 0.8,
            color: '#FFFACD'
          }
        ]
      }
    },
    previewImages: [
      '/images/templates/scandinavian_bedroom_1.jpg',
      '/images/templates/scandinavian_bedroom_2.jpg'
    ],
    thumbnailUrl: '/images/templates/scandinavian_bedroom_thumb.jpg',
    usageCount: 0,
    rating: {
      average: 0,
      count: 0
    },
    tags: ['scandinavian', 'bedroom', 'cozy', 'natural'],
    isOfficial: true,
    isFree: true,
    status: 'published'
  },
  {
    name: 'Industrial Kitchen',
    description: 'A modern industrial kitchen with exposed elements and dark tones',
    category: 'complete_design',
    houseTypes: ['house', 'condo'],
    roomTypes: ['kitchen'],
    style: 'industrial',
    budgetRange: {
      min: 15000,
      max: 35000,
      currency: 'CAD'
    },
    templateData: {
      floorPlan: JSON.stringify({
        walls: [
          { start: [0, 0], end: [5, 0], height: 3 },
          { start: [5, 0], end: [5, 3], height: 3 },
          { start: [5, 3], end: [0, 3], height: 3 },
          { start: [0, 3], end: [0, 0], height: 3 }
        ],
        doors: [
          { position: [2.5, 0], width: 0.9, type: 'entrance' }
        ],
        windows: [
          { position: [5, 1.5], width: 1.0, height: 1.0, type: 'standard' }
        ]
      }),
      scene3D: JSON.stringify({
        objects: [
          {
            id: 'floor',
            type: 'plane',
            position: [2.5, 0, 1.5],
            rotation: [-Math.PI/2, 0, 0],
            scale: [5, 3, 1],
            material: 'concrete_polished'
          }
        ]
      }),
      materials: [
        {
          id: 'concrete_polished',
          name: 'Polished Concrete',
          type: 'floor',
          color: '#696969',
          texture: '/textures/concrete_polished.jpg',
          properties: {
            roughness: 0.3,
            metalness: 0.1
          }
        }
      ],
      furniture: [
        {
          id: 'kitchen_island',
          name: 'Industrial Kitchen Island',
          category: 'cabinet',
          position: [2.5, 0.45, 1.5],
          rotation: [0, 0, 0],
          scale: [1, 1, 1],
          modelUrl: '/models/furniture/kitchen_island_industrial.gltf',
          thumbnailUrl: '/images/furniture/kitchen_island_thumb.jpg'
        }
      ],
      lighting: {
        ambient: 0.3,
        lights: [
          {
            type: 'directional',
            position: [10, 10, 5],
            intensity: 1.2,
            color: '#FFFFFF'
          },
          {
            type: 'spot',
            position: [2.5, 2.8, 1.5],
            intensity: 1.0,
            color: '#FFE4B5'
          }
        ]
      }
    },
    previewImages: [
      '/images/templates/industrial_kitchen_1.jpg',
      '/images/templates/industrial_kitchen_2.jpg'
    ],
    thumbnailUrl: '/images/templates/industrial_kitchen_thumb.jpg',
    usageCount: 0,
    rating: {
      average: 0,
      count: 0
    },
    tags: ['industrial', 'kitchen', 'modern', 'dark'],
    isOfficial: true,
    isFree: false,
    price: {
      amount: 29.99,
      currency: 'CAD'
    },
    status: 'published'
  }
];
