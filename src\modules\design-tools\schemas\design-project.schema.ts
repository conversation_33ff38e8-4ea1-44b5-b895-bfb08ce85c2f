import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type DesignProjectDocument = DesignProject & Document;

@Schema({ timestamps: true })
export class DesignProject {
  @ApiProperty({ description: '设计项目ID' })
  _id: Types.ObjectId;

  @ApiProperty({ description: '项目名称' })
  @Prop({ required: true, trim: true })
  name: string;

  @ApiProperty({ description: '项目描述' })
  @Prop({ trim: true })
  description?: string;

  @ApiProperty({ description: '用户ID' })
  @Prop({ required: true, type: Types.ObjectId, ref: 'User' })
  userId: Types.ObjectId;

  @ApiProperty({ description: '关联的装修项目ID' })
  @Prop({ type: Types.ObjectId, ref: 'Project' })
  projectId?: Types.ObjectId;

  @ApiProperty({ description: '房屋类型' })
  @Prop({ 
    required: true,
    enum: ['apartment', 'house', 'condo', 'townhouse', 'other'],
    default: 'apartment'
  })
  houseType: string;

  @ApiProperty({ description: '房屋面积(平方英尺)' })
  @Prop({ required: true, min: 0 })
  area: number;

  @ApiProperty({ description: '房间数量' })
  @Prop({
    type: {
      bedrooms: { type: Number, min: 0, default: 0 },
      bathrooms: { type: Number, min: 0, default: 0 },
      livingRooms: { type: Number, min: 0, default: 0 },
      kitchens: { type: Number, min: 0, default: 0 },
      others: { type: Number, min: 0, default: 0 }
    },
    default: {}
  })
  rooms: {
    bedrooms: number;
    bathrooms: number;
    livingRooms: number;
    kitchens: number;
    others: number;
  };

  @ApiProperty({ description: '设计风格' })
  @Prop({ 
    enum: ['modern', 'traditional', 'contemporary', 'industrial', 'scandinavian', 'minimalist', 'rustic', 'other'],
    default: 'modern'
  })
  style: string;

  @ApiProperty({ description: '预算范围' })
  @Prop({
    type: {
      min: { type: Number, min: 0 },
      max: { type: Number, min: 0 },
      currency: { type: String, default: 'CAD' }
    },
    required: true
  })
  budget: {
    min: number;
    max: number;
    currency: string;
  };

  @ApiProperty({ description: '3D场景数据' })
  @Prop({
    type: {
      floorPlan: { type: String }, // 平面图数据(JSON字符串)
      scene3D: { type: String }, // 3D场景数据(JSON字符串)
      camera: { 
        type: {
          position: { type: [Number], default: [0, 5, 10] },
          target: { type: [Number], default: [0, 0, 0] },
          fov: { type: Number, default: 75 }
        },
        default: {}
      },
      lighting: {
        type: {
          ambient: { type: Number, default: 0.4 },
          directional: {
            type: {
              intensity: { type: Number, default: 1 },
              position: { type: [Number], default: [10, 10, 5] }
            },
            default: {}
          }
        },
        default: {}
      }
    },
    default: {}
  })
  sceneData: {
    floorPlan?: string;
    scene3D?: string;
    camera: {
      position: number[];
      target: number[];
      fov: number;
    };
    lighting: {
      ambient: number;
      directional: {
        intensity: number;
        position: number[];
      };
    };
  };

  @ApiProperty({ description: '使用的资产列表' })
  @Prop([{ type: Types.ObjectId, ref: 'DesignAsset' }])
  assets: Types.ObjectId[];

  @ApiProperty({ description: '渲染图片URLs' })
  @Prop([String])
  renderedImages: string[];

  @ApiProperty({ description: '项目状态' })
  @Prop({ 
    enum: ['draft', 'in_progress', 'completed', 'shared'],
    default: 'draft'
  })
  status: string;

  @ApiProperty({ description: '是否公开' })
  @Prop({ default: false })
  isPublic: boolean;

  @ApiProperty({ description: '分享设置' })
  @Prop({
    type: {
      shareToken: { type: String },
      expiresAt: { type: Date },
      allowComments: { type: Boolean, default: false }
    }
  })
  shareSettings?: {
    shareToken: string;
    expiresAt: Date;
    allowComments: boolean;
  };

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export const DesignProjectSchema = SchemaFactory.createForClass(DesignProject);

// 创建索引
DesignProjectSchema.index({ userId: 1, createdAt: -1 });
DesignProjectSchema.index({ status: 1 });
DesignProjectSchema.index({ isPublic: 1, createdAt: -1 });
DesignProjectSchema.index({ 'shareSettings.shareToken': 1 });
