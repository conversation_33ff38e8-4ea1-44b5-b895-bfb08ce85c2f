import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { NotificationsService } from './notifications.service';
import {
  CreateNotificationDto,
  NotificationQueryDto,
  BulkNotificationDto,
} from './dto/create-notification.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Notification } from './schemas/notification.schema';
import { PaginatedResult } from '../../common/interfaces/paginated-result.interface';
import { UserType } from '../users/schemas/user.schema';

@ApiTags('Notifications')
@Controller('notifications')
@UseGuards(ThrottlerGuard)
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserType.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建通知（管理员）' })
  @ApiResponse({
    status: 201,
    description: '通知创建成功',
    type: Notification,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  async create(@Body() createNotificationDto: CreateNotificationDto): Promise<Notification> {
    return this.notificationsService.create(createNotificationDto);
  }

  @Post('bulk')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserType.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: '批量创建通知（管理员）' })
  @ApiResponse({
    status: 201,
    description: '批量通知创建成功',
    type: [Notification],
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  async createBulk(@Body() bulkNotificationDto: BulkNotificationDto): Promise<Notification[]> {
    return this.notificationsService.createBulk(bulkNotificationDto);
  }

  @Get('me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取我的通知列表' })
  @ApiResponse({
    status: 200,
    description: '获取通知列表成功',
    type: [Notification],
  })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiQuery({ name: 'type', required: false, description: '通知类型' })
  @ApiQuery({ name: 'priority', required: false, description: '优先级' })
  @ApiQuery({ name: 'isRead', required: false, description: '是否已读' })
  @ApiQuery({ name: 'search', required: false, description: '搜索关键词' })
  async findMyNotifications(
    @Query() query: NotificationQueryDto,
    @Request() req: any,
  ): Promise<PaginatedResult<Notification>> {
    return this.notificationsService.findByUser(req.user._id.toString(), query);
  }

  @Get('me/unread-count')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取未读通知数量' })
  @ApiResponse({
    status: 200,
    description: '获取未读通知数量成功',
    schema: {
      type: 'object',
      properties: {
        count: { type: 'number' },
      },
    },
  })
  async getUnreadCount(@Request() req: any): Promise<{ count: number }> {
    const count = await this.notificationsService.getUnreadCount(req.user._id.toString());
    return { count };
  }

  @Get('me/stats')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取我的通知统计' })
  @ApiResponse({
    status: 200,
    description: '获取通知统计成功',
  })
  async getMyStats(@Request() req: any): Promise<any> {
    return this.notificationsService.getUserStats(req.user._id.toString());
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据ID获取通知详情' })
  @ApiResponse({
    status: 200,
    description: '获取通知详情成功',
    type: Notification,
  })
  @ApiResponse({
    status: 404,
    description: '通知不存在',
  })
  @ApiParam({ name: 'id', description: '通知ID' })
  async findOne(@Param('id') id: string, @Request() req: any): Promise<Notification> {
    return this.notificationsService.findOne(id, req.user);
  }

  @Patch(':id/read')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '标记通知为已读' })
  @ApiResponse({
    status: 200,
    description: '标记成功',
    type: Notification,
  })
  @ApiResponse({
    status: 404,
    description: '通知不存在',
  })
  @ApiParam({ name: 'id', description: '通知ID' })
  async markAsRead(@Param('id') id: string, @Request() req: any): Promise<Notification> {
    return this.notificationsService.markAsRead(id, req.user);
  }

  @Patch('me/read-all')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '标记所有通知为已读' })
  @ApiResponse({
    status: 200,
    description: '批量标记成功',
    schema: {
      type: 'object',
      properties: {
        modifiedCount: { type: 'number' },
      },
    },
  })
  async markAllAsRead(@Request() req: any): Promise<{ modifiedCount: number }> {
    return this.notificationsService.markAllAsRead(req.user._id.toString());
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除通知' })
  @ApiResponse({
    status: 204,
    description: '通知删除成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '通知不存在',
  })
  @ApiParam({ name: 'id', description: '通知ID' })
  async remove(@Param('id') id: string, @Request() req: any): Promise<void> {
    return this.notificationsService.remove(id, req.user);
  }
}
