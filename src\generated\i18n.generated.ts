/* DO NOT EDIT, file generated by nestjs-i18n */

/* eslint-disable */
/* prettier-ignore */
import { Path } from "nestjs-i18n";
/* prettier-ignore */
export type I18nTranslations = {
    "common": {
        "success": string;
        "error": string;
        "warning": string;
        "info": string;
        "loading": string;
        "save": string;
        "cancel": string;
        "delete": string;
        "edit": string;
        "create": string;
        "update": string;
        "search": string;
        "filter": string;
        "sort": string;
        "page": string;
        "of": string;
        "total": string;
        "items": string;
        "noData": string;
        "confirm": string;
        "yes": string;
        "no": string;
        "back": string;
        "next": string;
        "previous": string;
        "submit": string;
        "reset": string;
        "clear": string;
        "close": string;
        "open": string;
        "view": string;
        "download": string;
        "upload": string;
        "required": string;
        "optional": string;
        "name": string;
        "email": string;
        "phone": string;
        "address": string;
        "city": string;
        "province": string;
        "postalCode": string;
        "country": string;
        "date": string;
        "time": string;
        "status": string;
        "type": string;
        "category": string;
        "description": string;
        "title": string;
        "content": string;
        "price": string;
        "amount": string;
        "quantity": string;
        "subtotal": string;
        "tax": string;
        "discount": string;
        "currency": {
            "cad": string;
            "usd": string;
        };
        "validation": {
            "required": string;
            "email": string;
            "phone": string;
            "minLength": string;
            "maxLength": string;
            "min": string;
            "max": string;
            "pattern": string;
            "unique": string;
            "passwordMismatch": string;
            "invalidCredentials": string;
            "accountLocked": string;
            "accountNotVerified": string;
        };
        "errors": {
            "general": string;
            "network": string;
            "unauthorized": string;
            "forbidden": string;
            "notFound": string;
            "conflict": string;
            "validation": string;
            "server": string;
        };
    };
    "project": {
        "title": string;
        "create": string;
        "edit": string;
        "delete": string;
        "view": string;
        "details": string;
        "overview": string;
        "timeline": string;
        "budget": string;
        "documents": string;
        "photos": string;
        "contractors": string;
        "quotes": string;
        "tasks": string;
        "reviews": string;
        "fields": {
            "title": string;
            "description": string;
            "category": string;
            "budget": string;
            "startDate": string;
            "endDate": string;
            "estimatedDuration": string;
            "actualDuration": string;
            "location": string;
            "propertyType": string;
            "propertySize": string;
            "rooms": string;
            "urgency": string;
            "requirements": string;
            "preferences": string;
            "tags": string;
        };
        "categories": {
            "kitchen": string;
            "bathroom": string;
            "basement": string;
            "flooring": string;
            "painting": string;
            "roofing": string;
            "plumbing": string;
            "electrical": string;
            "hvac": string;
            "landscaping": string;
            "addition": string;
            "exterior": string;
            "other": string;
        };
        "propertyTypes": {
            "house": string;
            "condo": string;
            "townhouse": string;
            "apartment": string;
            "commercial": string;
            "other": string;
        };
        "urgency": {
            "low": string;
            "medium": string;
            "high": string;
            "urgent": string;
        };
        "status": {
            "draft": string;
            "published": string;
            "in_progress": string;
            "on_hold": string;
            "completed": string;
            "cancelled": string;
        };
        "actions": {
            "publish": string;
            "unpublish": string;
            "start": string;
            "pause": string;
            "resume": string;
            "complete": string;
            "cancel": string;
            "archive": string;
            "duplicate": string;
            "share": string;
            "export": string;
            "invite": string;
            "requestQuote": string;
            "acceptQuote": string;
            "rejectQuote": string;
        };
        "messages": {
            "created": string;
            "updated": string;
            "deleted": string;
            "published": string;
            "unpublished": string;
            "started": string;
            "paused": string;
            "resumed": string;
            "completed": string;
            "cancelled": string;
            "archived": string;
            "duplicated": string;
            "shared": string;
            "exported": string;
            "invitationSent": string;
            "quoteRequested": string;
            "quoteAccepted": string;
            "quoteRejected": string;
        };
        "filters": {
            "all": string;
            "myProjects": string;
            "active": string;
            "completed": string;
            "draft": string;
            "byCategory": string;
            "byBudget": string;
            "byLocation": string;
            "byDate": string;
        };
        "search": {
            "placeholder": string;
            "noResults": string;
            "results": string;
        };
    };
    "service-provider": {
        "title": string;
        "profile": string;
        "services": {
            "kitchen": string;
            "bathroom": string;
            "flooring": string;
            "painting": string;
            "plumbing": string;
            "electrical": string;
            "roofing": string;
            "landscaping": string;
            "hvac": string;
            "general": string;
        };
        "portfolio": string;
        "reviews": string;
        "contact": string;
        "fields": {
            "companyName": string;
            "businessNumber": string;
            "licenseNumber": string;
            "insuranceNumber": string;
            "yearsInBusiness": string;
            "employeeCount": string;
            "serviceAreas": string;
            "specializations": string;
            "certifications": string;
            "workingHours": string;
            "emergencyService": string;
            "warranty": string;
            "paymentMethods": string;
        };
        "status": {
            "pending": string;
            "verified": string;
            "suspended": string;
            "rejected": string;
        };
        "verification": {
            "title": string;
            "businessLicense": string;
            "insurance": string;
            "certifications": string;
            "references": string;
            "backgroundCheck": string;
            "verified": string;
            "pending": string;
            "rejected": string;
            "expired": string;
        };
        "actions": {
            "register": string;
            "verify": string;
            "updateProfile": string;
            "addService": string;
            "removeService": string;
            "uploadDocument": string;
            "submitForReview": string;
            "contact": string;
            "requestQuote": string;
            "viewPortfolio": string;
            "reportProvider": string;
        };
        "messages": {
            "registrationSuccess": string;
            "verificationSubmitted": string;
            "profileUpdated": string;
            "serviceAdded": string;
            "serviceRemoved": string;
            "documentUploaded": string;
            "contactSent": string;
            "quoteRequested": string;
        };
    };
    "task": {
        "title": string;
        "create": string;
        "edit": string;
        "delete": string;
        "view": string;
        "assign": string;
        "complete": string;
        "fields": {
            "title": string;
            "description": string;
            "type": string;
            "priority": string;
            "status": string;
            "assignee": string;
            "dueDate": string;
            "estimatedHours": string;
            "actualHours": string;
            "project": string;
            "dependencies": string;
            "tags": string;
            "attachments": string;
            "comments": string;
        };
        "types": {
            "project_task": string;
            "milestone": string;
            "inspection": string;
            "approval": string;
            "payment": string;
            "communication": string;
            "documentation": string;
            "other": string;
        };
        "priority": {
            "low": string;
            "medium": string;
            "high": string;
            "urgent": string;
        };
        "status": {
            "todo": string;
            "in_progress": string;
            "review": string;
            "completed": string;
            "cancelled": string;
            "on_hold": string;
        };
        "actions": {
            "start": string;
            "pause": string;
            "resume": string;
            "complete": string;
            "cancel": string;
            "reopen": string;
            "assign": string;
            "unassign": string;
            "addComment": string;
            "addTimeLog": string;
            "uploadFile": string;
            "setDueDate": string;
            "setPriority": string;
            "addDependency": string;
        };
        "timeTracking": {
            "title": string;
            "startTime": string;
            "endTime": string;
            "duration": string;
            "description": string;
            "logTime": string;
            "totalTime": string;
            "todayTime": string;
            "weekTime": string;
        };
        "comments": {
            "title": string;
            "add": string;
            "edit": string;
            "delete": string;
            "reply": string;
            "noComments": string;
        };
        "filters": {
            "all": string;
            "myTasks": string;
            "assignedToMe": string;
            "createdByMe": string;
            "overdue": string;
            "dueToday": string;
            "dueThisWeek": string;
            "completed": string;
            "inProgress": string;
            "byPriority": string;
            "byProject": string;
        };
        "messages": {
            "created": string;
            "updated": string;
            "deleted": string;
            "assigned": string;
            "unassigned": string;
            "started": string;
            "paused": string;
            "resumed": string;
            "completed": string;
            "cancelled": string;
            "reopened": string;
            "commentAdded": string;
            "timeLogged": string;
            "fileUploaded": string;
            "dueDateSet": string;
            "prioritySet": string;
        };
    };
    "user": {
        "profile": {
            "title": string;
            "firstName": string;
            "lastName": string;
            "avatar": string;
            "bio": string;
            "dateOfBirth": string;
            "gender": string;
            "language": string;
            "timezone": string;
            "updateProfile": string;
            "profileUpdated": string;
        };
        "account": {
            "title": string;
            "email": string;
            "phone": string;
            "password": string;
            "currentPassword": string;
            "newPassword": string;
            "confirmPassword": string;
            "changePassword": string;
            "passwordChanged": string;
            "emailVerification": string;
            "phoneVerification": string;
            "twoFactorAuth": string;
            "enable": string;
            "disable": string;
            "verified": string;
            "unverified": string;
        };
        "address": {
            "title": string;
            "street": string;
            "unit": string;
            "city": string;
            "province": string;
            "postalCode": string;
            "country": string;
            "isPrimary": string;
            "addAddress": string;
            "editAddress": string;
            "deleteAddress": string;
            "addressAdded": string;
            "addressUpdated": string;
            "addressDeleted": string;
        };
        "preferences": {
            "title": string;
            "notifications": string;
            "emailNotifications": string;
            "smsNotifications": string;
            "pushNotifications": string;
            "marketing": string;
            "language": string;
            "currency": string;
            "timezone": string;
            "theme": string;
            "light": string;
            "dark": string;
            "auto": string;
        };
        "types": {
            "homeowner": string;
            "serviceProvider": string;
            "admin": string;
        };
        "status": {
            "active": string;
            "inactive": string;
            "suspended": string;
            "pending": string;
        };
        "actions": {
            "register": string;
            "login": string;
            "logout": string;
            "forgotPassword": string;
            "resetPassword": string;
            "verifyEmail": string;
            "verifyPhone": string;
            "resendVerification": string;
            "deactivateAccount": string;
            "deleteAccount": string;
        };
        "messages": {
            "registrationSuccess": string;
            "loginSuccess": string;
            "logoutSuccess": string;
            "passwordResetSent": string;
            "passwordResetSuccess": string;
            "emailVerificationSent": string;
            "emailVerified": string;
            "phoneVerified": string;
            "accountDeactivated": string;
            "accountDeleted": string;
        };
    };
};
/* prettier-ignore */
export type I18nPath = Path<I18nTranslations>;
