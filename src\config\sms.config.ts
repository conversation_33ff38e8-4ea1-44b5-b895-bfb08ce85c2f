import { registerAs } from '@nestjs/config';

export const smsConfig = registerAs('sms', () => ({
  twilio: {
    accountSid: process.env.TWILIO_ACCOUNT_SID,
    authToken: process.env.TWILIO_AUTH_TOKEN,
    phoneNumber: process.env.TWILIO_PHONE_NUMBER,
  },
  templates: {
    verification: 'Your HomeReno verification code is: {{code}}',
    projectUpdate: 'HomeReno: Your project {{projectName}} has been updated.',
    quoteReceived: 'HomeReno: You have received a new quote for your project.',
  },
}));
