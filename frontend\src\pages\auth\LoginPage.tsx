import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  Form, 
  Input, 
  Button, 
  Checkbox, 
  Typography, 
  Alert,
  Space,
  Divider 
} from 'antd';
import { 
  UserOutlined, 
  LockOutlined, 
  EyeInvisibleOutlined, 
  EyeTwoTone 
} from '@ant-design/icons';
import { useAuthStore } from '../../store/authStore';
import { LoginRequest } from '../../types';

const { Title, Text } = Typography;

const LoginPage: React.FC = () => {
  const [form] = Form.useForm();
  const [rememberMe, setRememberMe] = useState(false);
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { login, isLoading, error, clearError } = useAuthStore();

  const handleSubmit = async (values: LoginRequest & { remember: boolean }) => {
    try {
      clearError();
      await login({
        email: values.email,
        password: values.password,
      });
      
      // 登录成功后跳转到仪表板
      navigate('/dashboard');
    } catch (error) {
      // 错误已经在 store 中处理
      console.error('Login failed:', error);
    }
  };

  return (
    <div>
      <div style={{ textAlign: 'center', marginBottom: 32 }}>
        <Title level={2} style={{ marginBottom: 8 }}>
          {t('auth.login.title')}
        </Title>
        <Text type="secondary" style={{ fontSize: '1rem' }}>
          {t('auth.login.subtitle')}
        </Text>
      </div>

      {error && (
        <Alert
          message={error}
          type="error"
          showIcon
          closable
          onClose={clearError}
          style={{ marginBottom: 24 }}
        />
      )}

      <Form
        form={form}
        name="login"
        onFinish={handleSubmit}
        layout="vertical"
        size="large"
        autoComplete="off"
      >
        <Form.Item
          name="email"
          label={t('auth.login.email')}
          rules={[
            {
              required: true,
              message: t('auth.errors.emailRequired'),
            },
            {
              type: 'email',
              message: 'Please enter a valid email address',
            },
          ]}
        >
          <Input
            prefix={<UserOutlined />}
            placeholder={t('auth.login.email')}
            autoComplete="email"
          />
        </Form.Item>

        <Form.Item
          name="password"
          label={t('auth.login.password')}
          rules={[
            {
              required: true,
              message: t('auth.errors.passwordRequired'),
            },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder={t('auth.login.password')}
            iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            autoComplete="current-password"
          />
        </Form.Item>

        <Form.Item>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Form.Item name="remember" valuePropName="checked" noStyle>
              <Checkbox onChange={(e) => setRememberMe(e.target.checked)}>
                {t('auth.login.rememberMe')}
              </Checkbox>
            </Form.Item>
            <Link to="/auth/forgot-password" style={{ color: '#1890ff' }}>
              {t('auth.login.forgotPassword')}
            </Link>
          </div>
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={isLoading}
            block
            style={{ height: 48, fontSize: '1rem' }}
          >
            {t('auth.login.loginButton')}
          </Button>
        </Form.Item>
      </Form>

      <Divider style={{ margin: '32px 0' }}>
        <Text type="secondary">or</Text>
      </Divider>

      <div style={{ textAlign: 'center' }}>
        <Space direction="vertical" size="small">
          <Text type="secondary">
            {t('auth.login.noAccount')}
          </Text>
          <Link to="/auth/register">
            <Button type="link" style={{ padding: 0, fontSize: '1rem' }}>
              {t('auth.login.signUp')}
            </Button>
          </Link>
        </Space>
      </div>
    </div>
  );
};

export default LoginPage;
