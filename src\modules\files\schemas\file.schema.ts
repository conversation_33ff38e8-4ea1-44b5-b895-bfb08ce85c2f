import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type FileDocument = File & Document;

export enum FileType {
  IMAGE = 'image',
  DOCUMENT = 'document',
  VIDEO = 'video',
  AUDIO = 'audio',
  OTHER = 'other',
}

export enum FileStatus {
  UPLOADING = 'uploading',
  UPLOADED = 'uploaded',
  PROCESSING = 'processing',
  PROCESSED = 'processed',
  FAILED = 'failed',
  DELETED = 'deleted',
}

export enum FileCategory {
  AVATAR = 'avatar',
  PROJECT_ATTACHMENT = 'project_attachment',
  QUOTE_ATTACHMENT = 'quote_attachment',
  PORTFOLIO = 'portfolio',
  CERTIFICATION = 'certification',
  INSURANCE = 'insurance',
  CONTRACT = 'contract',
  INVOICE = 'invoice',
  RECEIPT = 'receipt',
  OTHER = 'other',
}

@Schema({ _id: false })
export class FileMetadata {
  @Prop()
  @ApiProperty({ description: '图片宽度' })
  width?: number;

  @Prop()
  @ApiProperty({ description: '图片高度' })
  height?: number;

  @Prop()
  @ApiProperty({ description: '视频/音频时长(秒)' })
  duration?: number;

  @Prop()
  @ApiProperty({ description: '文档页数' })
  pages?: number;

  @Prop()
  @ApiProperty({ description: '颜色模式' })
  colorMode?: string;

  @Prop()
  @ApiProperty({ description: '压缩质量' })
  quality?: number;
}

@Schema({ timestamps: true })
export class File {
  @Prop({ required: true })
  @ApiProperty({ description: '原始文件名' })
  originalName: string;

  @Prop({ required: true })
  @ApiProperty({ description: '存储文件名' })
  filename: string;

  @Prop({ required: true })
  @ApiProperty({ description: '文件路径' })
  path: string;

  @Prop()
  @ApiProperty({ description: '文件URL' })
  url?: string;

  @Prop()
  @ApiProperty({ description: '缩略图URL' })
  thumbnailUrl?: string;

  @Prop({ required: true })
  @ApiProperty({ description: 'MIME类型' })
  mimeType: string;

  @Prop({ required: true })
  @ApiProperty({ description: '文件大小(字节)' })
  size: number;

  @Prop({ enum: FileType, required: true })
  @ApiProperty({ description: '文件类型', enum: FileType })
  type: FileType;

  @Prop({ enum: FileCategory, required: true })
  @ApiProperty({ description: '文件分类', enum: FileCategory })
  category: FileCategory;

  @Prop({ enum: FileStatus, default: FileStatus.UPLOADING })
  @ApiProperty({ description: '文件状态', enum: FileStatus })
  status: FileStatus;

  @Prop({ type: Types.ObjectId, required: true, ref: 'User' })
  @ApiProperty({ description: '上传用户ID' })
  uploadedBy: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Project' })
  @ApiProperty({ description: '关联项目ID' })
  projectId?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Quote' })
  @ApiProperty({ description: '关联报价ID' })
  quoteId?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'ServiceProvider' })
  @ApiProperty({ description: '关联服务商ID' })
  serviceProviderId?: Types.ObjectId;

  @Prop({ type: FileMetadata })
  @ApiProperty({ description: '文件元数据', type: FileMetadata })
  metadata?: FileMetadata;

  @Prop()
  @ApiProperty({ description: '文件描述' })
  description?: string;

  @Prop([String])
  @ApiProperty({ description: '文件标签', type: [String] })
  tags?: string[];

  @Prop({ default: false })
  @ApiProperty({ description: '是否公开' })
  isPublic: boolean;

  @Prop()
  @ApiProperty({ description: '过期时间' })
  expiresAt?: Date;

  @Prop({ default: 0 })
  @ApiProperty({ description: '下载次数' })
  downloadCount: number;

  @Prop()
  @ApiProperty({ description: '最后访问时间' })
  lastAccessedAt?: Date;

  @Prop()
  @ApiProperty({ description: '删除时间' })
  deletedAt?: Date;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  @ApiProperty({ description: '删除用户ID' })
  deletedBy?: Types.ObjectId;
}

export const FileSchema = SchemaFactory.createForClass(File);

// 创建索引
FileSchema.index({ uploadedBy: 1, createdAt: -1 });
FileSchema.index({ projectId: 1, category: 1 });
FileSchema.index({ serviceProviderId: 1, category: 1 });
FileSchema.index({ quoteId: 1 });
FileSchema.index({ type: 1, status: 1 });
FileSchema.index({ category: 1, status: 1 });
FileSchema.index({ expiresAt: 1 });
FileSchema.index({ deletedAt: 1 });

// 虚拟字段
FileSchema.virtual('isExpired').get(function() {
  return this.expiresAt && new Date() > this.expiresAt;
});

FileSchema.virtual('isDeleted').get(function() {
  return !!this.deletedAt;
});

FileSchema.virtual('sizeFormatted').get(function() {
  const bytes = this.size;
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
});

// 设置虚拟字段在JSON序列化时包含
FileSchema.set('toJSON', { virtuals: true });
FileSchema.set('toObject', { virtuals: true });
