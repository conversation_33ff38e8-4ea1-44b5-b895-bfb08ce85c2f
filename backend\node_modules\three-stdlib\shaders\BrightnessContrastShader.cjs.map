{"version": 3, "file": "BrightnessContrastShader.cjs", "sources": ["../../src/shaders/BrightnessContrastShader.ts"], "sourcesContent": ["/**\n * Brightness and contrast adjustment\n * https://github.com/evanw/glfx.js\n * brightness: -1 to 1 (-1 is solid black, 0 is no change, and 1 is solid white)\n * contrast: -1 to 1 (-1 is solid gray, 0 is no change, and 1 is maximum contrast)\n */\n\nexport const BrightnessContrastShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    brightness: { value: 0 },\n    contrast: { value: 0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    uniform float brightness;\n    uniform float contrast;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tgl_FragColor = texture2D( tDiffuse, vUv );\n\n    \tgl_FragColor.rgb += brightness;\n\n    \tif (contrast > 0.0) {\n    \t\tgl_FragColor.rgb = (gl_FragColor.rgb - 0.5) / (1.0 - contrast) + 0.5;\n    \t} else {\n    \t\tgl_FragColor.rgb = (gl_FragColor.rgb - 0.5) * (1.0 + contrast) + 0.5;\n    \t}\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";;AAOO,MAAM,2BAA2B;AAAA,EACtC,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,YAAY,EAAE,OAAO,EAAE;AAAA,IACvB,UAAU,EAAE,OAAO,EAAE;AAAA,EACvB;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqB7B;;"}