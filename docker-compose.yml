version: '3.8'

services:
  mongodb:
    image: mongo:7.0
    container_name: homereno-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: homereno
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - homereno-network

  redis:
    image: redis:7.2-alpine
    container_name: homereno-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass redis123
    volumes:
      - redis_data:/data
    networks:
      - homereno-network

  mongo-express:
    image: mongo-express:1.0.0
    container_name: homereno-mongo-express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_<PERSON>NFIG_MONGODB_ADMINPASSWORD: password123
      ME_CONFIG_MONGODB_URL: *****************************************/
      ME_CONFIG_BASICAUTH: false
    depends_on:
      - mongodb
    networks:
      - homereno-network

volumes:
  mongodb_data:
  redis_data:

networks:
  homereno-network:
    driver: bridge
