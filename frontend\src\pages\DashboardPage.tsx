import React from 'react';
import { Typography } from 'antd';
import { useTranslation } from 'react-i18next';

const { Title } = Typography;

const DashboardPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div>
      <Title level={2}>{t('navigation.dashboard')}</Title>
      <p>Dashboard content will be implemented here.</p>
    </div>
  );
};

export default DashboardPage;
