@echo off
echo Starting HomeReno Backend Development Server...
echo.

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed or not in PATH
    pause
    exit /b 1
)

REM 检查npm是否可用
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: npm is not available
    pause
    exit /b 1
)

REM 安装依赖
echo Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo Error: Failed to install dependencies
    pause
    exit /b 1
)

REM 创建必要的目录
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads

echo.
echo Dependencies installed successfully!
echo Starting development server...
echo.
echo Server will be available at: http://localhost:3000
echo API documentation will be available at: http://localhost:3000/api
echo.

REM 启动开发服务器
call npm run start:dev

pause
