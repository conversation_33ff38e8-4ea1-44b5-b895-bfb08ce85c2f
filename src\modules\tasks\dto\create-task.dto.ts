import {
  <PERSON>S<PERSON>,
  <PERSON><PERSON><PERSON>,
  IsO<PERSON>al,
  IsBoolean,
  IsArray,
  IsMongoId,
  IsNumber,
  IsDateString,
  ValidateNested,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  TaskStatus,
  TaskPriority,
  TaskType,
} from '../schemas/task.schema';

export class CreateTaskAssigneeDto {
  @ApiProperty({ description: '被分配者ID' })
  @IsMongoId()
  userId: string;

  @ApiPropertyOptional({ description: '分配备注' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  note?: string;
}

export class CreateTaskDto {
  @ApiProperty({ description: '任务标题' })
  @IsString()
  @MinLength(1)
  @MaxLength(200)
  title: string;

  @ApiPropertyOptional({ description: '任务描述' })
  @IsOptional()
  @IsString()
  @MaxLength(2000)
  description?: string;

  @ApiPropertyOptional({ description: '任务类型', enum: TaskType, default: TaskType.PROJECT_TASK })
  @IsOptional()
  @IsEnum(TaskType)
  type?: TaskType = TaskType.PROJECT_TASK;

  @ApiPropertyOptional({ description: '任务优先级', enum: TaskPriority, default: TaskPriority.MEDIUM })
  @IsOptional()
  @IsEnum(TaskPriority)
  priority?: TaskPriority = TaskPriority.MEDIUM;

  @ApiPropertyOptional({ description: '关联项目ID' })
  @IsOptional()
  @IsMongoId()
  projectId?: string;

  @ApiPropertyOptional({ description: '关联报价ID' })
  @IsOptional()
  @IsMongoId()
  quoteId?: string;

  @ApiPropertyOptional({ description: '父任务ID' })
  @IsOptional()
  @IsMongoId()
  parentTaskId?: string;

  @ApiPropertyOptional({ description: '任务分配', type: [CreateTaskAssigneeDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateTaskAssigneeDto)
  assignees?: CreateTaskAssigneeDto[];

  @ApiPropertyOptional({ description: '预计开始时间' })
  @IsOptional()
  @IsDateString()
  estimatedStartDate?: string;

  @ApiPropertyOptional({ description: '预计结束时间' })
  @IsOptional()
  @IsDateString()
  estimatedEndDate?: string;

  @ApiPropertyOptional({ description: '预计工时（小时）', default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  estimatedHours?: number = 0;

  @ApiPropertyOptional({ description: '任务标签', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: '附件', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  attachments?: string[];

  @ApiPropertyOptional({ description: '依赖任务ID', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  dependencies?: string[];

  @ApiPropertyOptional({ description: '截止时间' })
  @IsOptional()
  @IsDateString()
  dueDate?: string;

  @ApiPropertyOptional({ description: '是否重要', default: false })
  @IsOptional()
  @IsBoolean()
  isImportant?: boolean = false;

  @ApiPropertyOptional({ description: '是否紧急', default: false })
  @IsOptional()
  @IsBoolean()
  isUrgent?: boolean = false;

  @ApiPropertyOptional({ description: '是否可重复', default: false })
  @IsOptional()
  @IsBoolean()
  isRecurring?: boolean = false;

  @ApiPropertyOptional({ description: '重复规则' })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  recurringRule?: string;

  @ApiPropertyOptional({ description: '备注' })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  notes?: string;
}

export class UpdateTaskDto {
  @ApiPropertyOptional({ description: '任务标题' })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(200)
  title?: string;

  @ApiPropertyOptional({ description: '任务描述' })
  @IsOptional()
  @IsString()
  @MaxLength(2000)
  description?: string;

  @ApiPropertyOptional({ description: '任务类型', enum: TaskType })
  @IsOptional()
  @IsEnum(TaskType)
  type?: TaskType;

  @ApiPropertyOptional({ description: '任务状态', enum: TaskStatus })
  @IsOptional()
  @IsEnum(TaskStatus)
  status?: TaskStatus;

  @ApiPropertyOptional({ description: '任务优先级', enum: TaskPriority })
  @IsOptional()
  @IsEnum(TaskPriority)
  priority?: TaskPriority;

  @ApiPropertyOptional({ description: '任务分配', type: [CreateTaskAssigneeDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateTaskAssigneeDto)
  assignees?: CreateTaskAssigneeDto[];

  @ApiPropertyOptional({ description: '预计开始时间' })
  @IsOptional()
  @IsDateString()
  estimatedStartDate?: string;

  @ApiPropertyOptional({ description: '预计结束时间' })
  @IsOptional()
  @IsDateString()
  estimatedEndDate?: string;

  @ApiPropertyOptional({ description: '实际开始时间' })
  @IsOptional()
  @IsDateString()
  actualStartDate?: string;

  @ApiPropertyOptional({ description: '实际结束时间' })
  @IsOptional()
  @IsDateString()
  actualEndDate?: string;

  @ApiPropertyOptional({ description: '预计工时（小时）' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  estimatedHours?: number;

  @ApiPropertyOptional({ description: '实际工时（小时）' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  actualHours?: number;

  @ApiPropertyOptional({ description: '完成进度（百分比）', minimum: 0, maximum: 100 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  progress?: number;

  @ApiPropertyOptional({ description: '任务标签', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: '附件', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  attachments?: string[];

  @ApiPropertyOptional({ description: '依赖任务ID', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  dependencies?: string[];

  @ApiPropertyOptional({ description: '截止时间' })
  @IsOptional()
  @IsDateString()
  dueDate?: string;

  @ApiPropertyOptional({ description: '是否重要' })
  @IsOptional()
  @IsBoolean()
  isImportant?: boolean;

  @ApiPropertyOptional({ description: '是否紧急' })
  @IsOptional()
  @IsBoolean()
  isUrgent?: boolean;

  @ApiPropertyOptional({ description: '是否可重复' })
  @IsOptional()
  @IsBoolean()
  isRecurring?: boolean;

  @ApiPropertyOptional({ description: '重复规则' })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  recurringRule?: string;

  @ApiPropertyOptional({ description: '备注' })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  notes?: string;
}

export class TaskQueryDto {
  @ApiPropertyOptional({ description: '页码', default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', default: 20 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number = 20;

  @ApiPropertyOptional({ description: '任务类型', enum: TaskType })
  @IsOptional()
  @IsEnum(TaskType)
  type?: TaskType;

  @ApiPropertyOptional({ description: '任务状态', enum: TaskStatus })
  @IsOptional()
  @IsEnum(TaskStatus)
  status?: TaskStatus;

  @ApiPropertyOptional({ description: '任务优先级', enum: TaskPriority })
  @IsOptional()
  @IsEnum(TaskPriority)
  priority?: TaskPriority;

  @ApiPropertyOptional({ description: '创建者ID' })
  @IsOptional()
  @IsMongoId()
  createdBy?: string;

  @ApiPropertyOptional({ description: '分配者ID' })
  @IsOptional()
  @IsMongoId()
  assigneeId?: string;

  @ApiPropertyOptional({ description: '项目ID' })
  @IsOptional()
  @IsMongoId()
  projectId?: string;

  @ApiPropertyOptional({ description: '父任务ID' })
  @IsOptional()
  @IsMongoId()
  parentTaskId?: string;

  @ApiPropertyOptional({ description: '是否重要' })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  isImportant?: boolean;

  @ApiPropertyOptional({ description: '是否紧急' })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  isUrgent?: boolean;

  @ApiPropertyOptional({ description: '是否逾期' })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  isOverdue?: boolean;

  @ApiPropertyOptional({ description: '搜索关键词' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: '排序字段', enum: ['createdAt', 'dueDate', 'priority', 'progress'] })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ description: '排序方向', enum: ['asc', 'desc'] })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class CreateTaskCommentDto {
  @ApiProperty({ description: '评论内容' })
  @IsString()
  @MinLength(1)
  @MaxLength(1000)
  content: string;

  @ApiPropertyOptional({ description: '附件', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  attachments?: string[];
}

export class CreateTimeLogDto {
  @ApiProperty({ description: '开始时间' })
  @IsDateString()
  startTime: string;

  @ApiPropertyOptional({ description: '结束时间' })
  @IsOptional()
  @IsDateString()
  endTime?: string;

  @ApiPropertyOptional({ description: '工作描述' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiPropertyOptional({ description: '工作时长（分钟）' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  duration?: number;
}
