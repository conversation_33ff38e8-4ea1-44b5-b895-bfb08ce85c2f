import {
  IsString,
  IsEnum,
  IsOptional,
  IsNumber,
  IsArray,
  IsBoolean,
  IsDateString,
  ValidateNested,
  Min,
  Max,
  <PERSON>ength,
  <PERSON><PERSON><PERSON><PERSON>,
  IsMongoId,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PaymentScheduleType } from '../schemas/quote.schema';

export class CreateQuoteItemDto {
  @ApiProperty({ description: '项目名称' })
  @IsString()
  @MinLength(1)
  @MaxLength(200)
  name: string;

  @ApiPropertyOptional({ description: '项目描述' })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @ApiProperty({ description: '数量' })
  @IsNumber()
  @Min(0.01)
  quantity: number;

  @ApiPropertyOptional({ description: '单位' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  unit?: string;

  @ApiProperty({ description: '单价' })
  @IsNumber()
  @Min(0)
  unitPrice: number;

  @ApiProperty({ description: '总价' })
  @IsNumber()
  @Min(0)
  totalPrice: number;
}

export class CreatePaymentScheduleDto {
  @ApiProperty({ description: '付款类型', enum: PaymentScheduleType })
  @IsEnum(PaymentScheduleType)
  type: PaymentScheduleType;

  @ApiProperty({ description: '付款描述' })
  @IsString()
  @MinLength(1)
  @MaxLength(200)
  description: string;

  @ApiProperty({ description: '付款金额' })
  @IsNumber()
  @Min(0)
  amount: number;

  @ApiProperty({ description: '付款百分比' })
  @IsNumber()
  @Min(0)
  @Max(100)
  percentage: number;

  @ApiPropertyOptional({ description: '预期付款日期' })
  @IsOptional()
  @IsDateString()
  dueDate?: string;
}

export class CreateQuoteTermsDto {
  @ApiPropertyOptional({ description: '有效期(天)' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(365)
  validityDays?: number;

  @ApiPropertyOptional({ description: '开始日期' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ description: '预计完成日期' })
  @IsOptional()
  @IsDateString()
  estimatedCompletionDate?: string;

  @ApiPropertyOptional({ description: '保修期(月)' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(120)
  warrantyMonths?: number;

  @ApiPropertyOptional({ description: '包含的服务', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  includedServices?: string[];

  @ApiPropertyOptional({ description: '不包含的服务', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  excludedServices?: string[];

  @ApiPropertyOptional({ description: '特殊条款', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  specialTerms?: string[];
}

export class CreateQuoteDto {
  @ApiProperty({ description: '项目ID' })
  @IsMongoId()
  projectId: string;

  @ApiProperty({ description: '报价标题' })
  @IsString()
  @MinLength(1)
  @MaxLength(200)
  title: string;

  @ApiPropertyOptional({ description: '报价描述' })
  @IsOptional()
  @IsString()
  @MaxLength(2000)
  description?: string;

  @ApiProperty({ description: '报价项目', type: [CreateQuoteItemDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateQuoteItemDto)
  items: CreateQuoteItemDto[];

  @ApiProperty({ description: '小计' })
  @IsNumber()
  @Min(0)
  subtotal: number;

  @ApiPropertyOptional({ description: '税费', default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  tax?: number = 0;

  @ApiPropertyOptional({ description: '折扣', default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  discount?: number = 0;

  @ApiProperty({ description: '总金额' })
  @IsNumber()
  @Min(0)
  totalAmount: number;

  @ApiPropertyOptional({ description: '货币单位', default: 'CAD' })
  @IsOptional()
  @IsString()
  currency?: string = 'CAD';

  @ApiPropertyOptional({ description: '付款计划', type: [CreatePaymentScheduleDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreatePaymentScheduleDto)
  paymentSchedule?: CreatePaymentScheduleDto[];

  @ApiPropertyOptional({ description: '报价条款', type: CreateQuoteTermsDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateQuoteTermsDto)
  terms?: CreateQuoteTermsDto;

  @ApiPropertyOptional({ description: '备注' })
  @IsOptional()
  @IsString()
  @MaxLength(2000)
  notes?: string;

  @ApiPropertyOptional({ description: '附件', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  attachments?: string[];
}
