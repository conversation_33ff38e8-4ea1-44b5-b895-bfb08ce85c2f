{"version": 3, "file": "merge.js", "sourceRoot": "", "sources": ["../../src/utils/merge.ts"], "names": [], "mappings": ";;;AAEA,SAAS,QAAQ,CAAC,IAAS;IACzB,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAClE,CAAC;AAED,SAAgB,SAAS,CAAC,MAAuB,EAAE,GAAG,OAAY;IAChE,IAAI,CAAC,OAAO,CAAC,MAAM;QAAE,OAAO,MAAM,CAAC;IACnC,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;IAE/B,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE;QACxC,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE;YACxB,IAAI,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;gBACzB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;oBAAE,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;gBACvD,SAAS,CAAC,MAAM,CAAC,GAAG,CAAoB,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;aACxD;iBAAM;gBACL,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;aAC/C;SACF;KACF;IAED,OAAO,SAAS,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC;AACvC,CAAC;AAhBD,8BAgBC"}