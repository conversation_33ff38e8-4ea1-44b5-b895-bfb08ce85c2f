{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["ES2020"], "moduleResolution": "node", "strict": false, "paths": {"@/*": ["src/*"], "@/common/*": ["src/common/*"], "@/modules/*": ["src/modules/*"], "@/shared/*": ["src/shared/*"], "@/config/*": ["src/config/*"]}}, "include": ["src/**/*", "test/**/*"], "exclude": ["node_modules", "dist"]}