import { registerAs } from '@nestjs/config';

export const paymentConfig = registerAs('payment', () => ({
  stripe: {
    secretKey: process.env.STRIPE_SECRET_KEY,
    publishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
    apiVersion: '2023-10-16',
  },
  paypal: {
    clientId: process.env.PAYPAL_CLIENT_ID,
    clientSecret: process.env.PAYPAL_CLIENT_SECRET,
    mode: process.env.PAYPAL_MODE || 'sandbox', // 'sandbox' | 'live'
  },
  interac: {
    merchantId: process.env.INTERAC_MERCHANT_ID,
    apiKey: process.env.INTERAC_API_KEY,
    environment: process.env.INTERAC_ENVIRONMENT || 'sandbox', // 'sandbox' | 'production'
  },
  escrow: {
    enabled: process.env.ESCROW_ENABLED === 'true',
    holdPeriod: parseInt(process.env.ESCROW_HOLD_PERIOD) || 7, // 天数
    autoRelease: process.env.ESCROW_AUTO_RELEASE === 'true',
  },
  fees: {
    processingFeeRate: parseFloat(process.env.PROCESSING_FEE_RATE) || 0.029, // 2.9%
    fixedFee: parseFloat(process.env.PROCESSING_FIXED_FEE) || 0.30, // $0.30
    currency: process.env.DEFAULT_CURRENCY || 'CAD',
  },
  limits: {
    minAmount: parseFloat(process.env.MIN_PAYMENT_AMOUNT) || 1.00,
    maxAmount: parseFloat(process.env.MAX_PAYMENT_AMOUNT) || 50000.00,
    dailyLimit: parseFloat(process.env.DAILY_PAYMENT_LIMIT) || 10000.00,
  },
  security: {
    requireCvv: process.env.REQUIRE_CVV === 'true',
    require3ds: process.env.REQUIRE_3DS === 'true',
    fraudDetection: process.env.FRAUD_DETECTION_ENABLED === 'true',
  },
  notifications: {
    emailEnabled: process.env.PAYMENT_EMAIL_NOTIFICATIONS === 'true',
    smsEnabled: process.env.PAYMENT_SMS_NOTIFICATIONS === 'true',
    webhookEnabled: process.env.PAYMENT_WEBHOOK_NOTIFICATIONS === 'true',
  },
  refunds: {
    enabled: process.env.REFUNDS_ENABLED === 'true',
    autoRefundPeriod: parseInt(process.env.AUTO_REFUND_PERIOD) || 30, // 天数
    partialRefundsEnabled: process.env.PARTIAL_REFUNDS_ENABLED === 'true',
  },
}));
