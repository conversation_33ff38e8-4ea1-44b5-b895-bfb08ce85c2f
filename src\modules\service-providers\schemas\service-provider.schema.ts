import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type ServiceProviderDocument = ServiceProvider & Document;

export enum ServiceProviderStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  SUSPENDED = 'suspended',
  REJECTED = 'rejected',
}

export enum BusinessType {
  INDIVIDUAL = 'individual',
  COMPANY = 'company',
}

export enum ServiceCategory {
  FULL_RENOVATION = 'full_renovation',
  KITCHEN = 'kitchen',
  BATHROOM = 'bathroom',
  FLOORING = 'flooring',
  PAINTING = 'painting',
  ELECTRICAL = 'electrical',
  PLUMBING = 'plumbing',
  ROOFING = 'roofing',
  LANDSCAPING = 'landscaping',
  HVAC = 'hvac',
  CARPENTRY = 'carpentry',
  TILING = 'tiling',
  DRYWALL = 'drywall',
  INSULATION = 'insulation',
  WINDOWS_DOORS = 'windows_doors',
  OTHER = 'other',
}

@Schema({ _id: false })
export class BusinessInfo {
  @Prop({ required: true })
  @ApiProperty({ description: '公司名称' })
  companyName: string;

  @Prop({ enum: BusinessType, required: true })
  @ApiProperty({ description: '业务类型', enum: BusinessType })
  businessType: BusinessType;

  @Prop()
  @ApiProperty({ description: '营业执照号' })
  licenseNumber?: string;

  @Prop()
  @ApiProperty({ description: '税号' })
  taxNumber?: string;

  @Prop()
  @ApiProperty({ description: '成立年份' })
  establishedYear?: number;

  @Prop()
  @ApiProperty({ description: '员工数量' })
  employeeCount?: number;

  @Prop()
  @ApiProperty({ description: '公司描述' })
  description?: string;

  @Prop()
  @ApiProperty({ description: '公司网站' })
  website?: string;

  @Prop([String])
  @ApiProperty({ description: '营业执照图片', type: [String] })
  licenseImages?: string[];
}

@Schema({ _id: false })
export class ServiceArea {
  @Prop({ required: true })
  @ApiProperty({ description: '省份' })
  province: string;

  @Prop([String])
  @ApiProperty({ description: '服务城市', type: [String] })
  cities: string[];

  @Prop()
  @ApiProperty({ description: '服务半径(公里)' })
  radius?: number;
}

@Schema({ _id: false })
export class ServiceOffered {
  @Prop({ enum: ServiceCategory, required: true })
  @ApiProperty({ description: '服务类别', enum: ServiceCategory })
  category: ServiceCategory;

  @Prop({ required: true })
  @ApiProperty({ description: '服务名称' })
  name: string;

  @Prop()
  @ApiProperty({ description: '服务描述' })
  description?: string;

  @Prop()
  @ApiProperty({ description: '起始价格' })
  startingPrice?: number;

  @Prop()
  @ApiProperty({ description: '价格单位' })
  priceUnit?: string;

  @Prop([String])
  @ApiProperty({ description: '服务图片', type: [String] })
  images?: string[];
}

@Schema({ _id: false })
export class Certification {
  @Prop({ required: true })
  @ApiProperty({ description: '认证名称' })
  name: string;

  @Prop({ required: true })
  @ApiProperty({ description: '颁发机构' })
  issuingOrganization: string;

  @Prop()
  @ApiProperty({ description: '认证编号' })
  certificationNumber?: string;

  @Prop()
  @ApiProperty({ description: '获得日期' })
  issuedDate?: Date;

  @Prop()
  @ApiProperty({ description: '过期日期' })
  expiryDate?: Date;

  @Prop()
  @ApiProperty({ description: '认证文件URL' })
  documentUrl?: string;

  @Prop({ default: false })
  @ApiProperty({ description: '是否已验证' })
  verified: boolean;
}

@Schema({ _id: false })
export class Insurance {
  @Prop({ required: true })
  @ApiProperty({ description: '保险类型' })
  type: string;

  @Prop({ required: true })
  @ApiProperty({ description: '保险公司' })
  provider: string;

  @Prop()
  @ApiProperty({ description: '保单号' })
  policyNumber?: string;

  @Prop()
  @ApiProperty({ description: '保险金额' })
  coverageAmount?: number;

  @Prop()
  @ApiProperty({ description: '生效日期' })
  effectiveDate?: Date;

  @Prop()
  @ApiProperty({ description: '过期日期' })
  expiryDate?: Date;

  @Prop()
  @ApiProperty({ description: '保单文件URL' })
  documentUrl?: string;

  @Prop({ default: false })
  @ApiProperty({ description: '是否已验证' })
  verified: boolean;
}

@Schema({ _id: false })
export class Portfolio {
  @Prop({ required: true })
  @ApiProperty({ description: '项目标题' })
  title: string;

  @Prop()
  @ApiProperty({ description: '项目描述' })
  description?: string;

  @Prop({ enum: ServiceCategory })
  @ApiProperty({ description: '项目类别', enum: ServiceCategory })
  category?: ServiceCategory;

  @Prop([String])
  @ApiProperty({ description: '项目图片', type: [String] })
  images: string[];

  @Prop()
  @ApiProperty({ description: '项目成本' })
  cost?: number;

  @Prop()
  @ApiProperty({ description: '项目时长(天)' })
  duration?: number;

  @Prop()
  @ApiProperty({ description: '完成日期' })
  completedDate?: Date;

  @Prop()
  @ApiProperty({ description: '客户评价' })
  clientTestimonial?: string;
}

@Schema({ _id: false })
export class Rating {
  @Prop({ default: 0 })
  @ApiProperty({ description: '总评分' })
  overall: number;

  @Prop({ default: 0 })
  @ApiProperty({ description: '质量评分' })
  quality: number;

  @Prop({ default: 0 })
  @ApiProperty({ description: '时效评分' })
  timeliness: number;

  @Prop({ default: 0 })
  @ApiProperty({ description: '沟通评分' })
  communication: number;

  @Prop({ default: 0 })
  @ApiProperty({ description: '价值评分' })
  value: number;

  @Prop({ default: 0 })
  @ApiProperty({ description: '评价总数' })
  totalReviews: number;

  @Prop({ default: 0 })
  @ApiProperty({ description: '总评分数' })
  totalRating: number;
}

@Schema({ _id: false })
export class BusinessStats {
  @Prop({ default: 0 })
  @ApiProperty({ description: '完成项目数' })
  projectsCompleted: number;

  @Prop({ default: 0 })
  @ApiProperty({ description: '平均响应时间(小时)' })
  responseTime: number;

  @Prop({ default: 0 })
  @ApiProperty({ description: '按时完成率' })
  onTimeRate: number;

  @Prop({ default: 0 })
  @ApiProperty({ description: '回头客率' })
  repeatCustomerRate: number;
}

@Schema({ timestamps: true })
export class ServiceProvider {
  @Prop({ type: Types.ObjectId, required: true, ref: 'User' })
  @ApiProperty({ description: '用户ID' })
  userId: Types.ObjectId;

  @Prop({ enum: ServiceProviderStatus, default: ServiceProviderStatus.PENDING })
  @ApiProperty({ description: '服务商状态', enum: ServiceProviderStatus })
  status: ServiceProviderStatus;

  @Prop({ type: BusinessInfo, required: true })
  @ApiProperty({ description: '业务信息', type: BusinessInfo })
  businessInfo: BusinessInfo;

  @Prop([ServiceArea])
  @ApiProperty({ description: '服务区域', type: [ServiceArea] })
  serviceAreas: ServiceArea[];

  @Prop([ServiceOffered])
  @ApiProperty({ description: '提供的服务', type: [ServiceOffered] })
  services: ServiceOffered[];

  @Prop([Certification])
  @ApiProperty({ description: '认证信息', type: [Certification] })
  certifications?: Certification[];

  @Prop([Insurance])
  @ApiProperty({ description: '保险信息', type: [Insurance] })
  insurance?: Insurance[];

  @Prop([Portfolio])
  @ApiProperty({ description: '作品集', type: [Portfolio] })
  portfolio?: Portfolio[];

  @Prop({ type: Rating })
  @ApiProperty({ description: '评分信息', type: Rating })
  rating?: Rating;

  @Prop({ type: BusinessStats })
  @ApiProperty({ description: '业务统计', type: BusinessStats })
  stats?: BusinessStats;

  @Prop()
  @ApiProperty({ description: '审核时间' })
  reviewedAt?: Date;

  @Prop()
  @ApiProperty({ description: '审核备注' })
  reviewNotes?: string;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  @ApiProperty({ description: '审核人ID' })
  reviewedBy?: Types.ObjectId;
}

export const ServiceProviderSchema = SchemaFactory.createForClass(ServiceProvider);

// 创建索引
ServiceProviderSchema.index({ userId: 1 }, { unique: true });
ServiceProviderSchema.index({ status: 1 });
ServiceProviderSchema.index({ 'businessInfo.companyName': 'text', 'businessInfo.description': 'text' });
ServiceProviderSchema.index({ 'serviceAreas.province': 1, 'serviceAreas.cities': 1 });
ServiceProviderSchema.index({ 'services.category': 1 });
ServiceProviderSchema.index({ 'rating.overall': -1 });
ServiceProviderSchema.index({ 'stats.projectsCompleted': -1 });

// 虚拟字段
ServiceProviderSchema.virtual('isApproved').get(function() {
  return this.status === ServiceProviderStatus.APPROVED;
});

ServiceProviderSchema.virtual('averageRating').get(function() {
  return this.rating?.totalReviews > 0 ? this.rating.totalRating / this.rating.totalReviews : 0;
});

// 设置虚拟字段在JSON序列化时包含
ServiceProviderSchema.set('toJSON', { virtuals: true });
ServiceProviderSchema.set('toObject', { virtuals: true });
