import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { DesignToolsService } from './design-tools.service';
import { CreateDesignProjectDto } from './dto/create-design-project.dto';
import { UpdateDesignProjectDto } from './dto/update-design-project.dto';
import { QueryDesignProjectsDto } from './dto/query-design-projects.dto';
import { DesignProject } from './schemas/design-project.schema';
import { DesignTemplate } from './schemas/design-template.schema';
import { DesignAsset } from './schemas/design-asset.schema';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { OptionalJwtAuthGuard } from '../auth/guards/optional-jwt-auth.guard';

@ApiTags('Design Tools')
@Controller('design-tools')
@UseGuards(ThrottlerGuard)
export class DesignToolsController {
  constructor(private readonly designToolsService: DesignToolsService) {}

  // 设计项目相关端点
  @Post('projects')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建设计项目' })
  @ApiResponse({
    status: 201,
    description: '设计项目创建成功',
    type: DesignProject,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  async createProject(
    @Body() createProjectDto: CreateDesignProjectDto,
    @Request() req: any,
  ): Promise<DesignProject> {
    return this.designToolsService.createProject(createProjectDto, req.user);
  }

  @Get('projects')
  @UseGuards(OptionalJwtAuthGuard)
  @ApiOperation({ summary: '获取设计项目列表' })
  @ApiResponse({
    status: 200,
    description: '获取设计项目列表成功',
    schema: {
      type: 'object',
      properties: {
        projects: {
          type: 'array',
          items: { $ref: '#/components/schemas/DesignProject' }
        },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
        totalPages: { type: 'number' }
      }
    }
  })
  async findAllProjects(
    @Query() query: QueryDesignProjectsDto,
    @Request() req: any,
  ) {
    return this.designToolsService.findAllProjects(query, req.user);
  }

  @Get('projects/:id')
  @UseGuards(OptionalJwtAuthGuard)
  @ApiOperation({ summary: '获取设计项目详情' })
  @ApiParam({ name: 'id', description: '项目ID' })
  @ApiResponse({
    status: 200,
    description: '获取设计项目详情成功',
    type: DesignProject,
  })
  @ApiResponse({
    status: 404,
    description: '项目不存在',
  })
  @ApiResponse({
    status: 403,
    description: '无权限访问',
  })
  async findProjectById(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<DesignProject> {
    return this.designToolsService.findProjectById(id, req.user);
  }

  @Patch('projects/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新设计项目' })
  @ApiParam({ name: 'id', description: '项目ID' })
  @ApiResponse({
    status: 200,
    description: '设计项目更新成功',
    type: DesignProject,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '项目不存在',
  })
  async updateProject(
    @Param('id') id: string,
    @Body() updateProjectDto: UpdateDesignProjectDto,
    @Request() req: any,
  ): Promise<DesignProject> {
    return this.designToolsService.updateProject(id, updateProjectDto, req.user);
  }

  @Delete('projects/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除设计项目' })
  @ApiParam({ name: 'id', description: '项目ID' })
  @ApiResponse({
    status: 204,
    description: '设计项目删除成功',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '项目不存在',
  })
  async deleteProject(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<void> {
    return this.designToolsService.deleteProject(id, req.user);
  }

  @Post('projects/:id/duplicate')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '复制设计项目' })
  @ApiParam({ name: 'id', description: '项目ID' })
  @ApiResponse({
    status: 201,
    description: '项目复制成功',
    type: DesignProject,
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 404,
    description: '项目不存在',
  })
  async duplicateProject(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<DesignProject> {
    return this.designToolsService.duplicateProject(id, req.user);
  }

  @Post('projects/:id/share')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '分享设计项目' })
  @ApiParam({ name: 'id', description: '项目ID' })
  @ApiQuery({ name: 'expiresInDays', description: '分享链接有效期(天)', required: false })
  @ApiResponse({
    status: 200,
    description: '项目分享成功',
    schema: {
      type: 'object',
      properties: {
        shareToken: { type: 'string' },
        shareUrl: { type: 'string' }
      }
    }
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '项目不存在',
  })
  async shareProject(
    @Param('id') id: string,
    @Query('expiresInDays') expiresInDays: number = 30,
    @Request() req: any,
  ) {
    return this.designToolsService.shareProject(id, req.user, expiresInDays);
  }

  @Get('shared/:shareToken')
  @ApiOperation({ summary: '通过分享链接获取设计项目' })
  @ApiParam({ name: 'shareToken', description: '分享令牌' })
  @ApiResponse({
    status: 200,
    description: '获取分享项目成功',
    type: DesignProject,
  })
  @ApiResponse({
    status: 404,
    description: '分享项目不存在',
  })
  @ApiResponse({
    status: 403,
    description: '分享链接已过期',
  })
  async getSharedProject(
    @Param('shareToken') shareToken: string,
  ): Promise<DesignProject> {
    return this.designToolsService.getSharedProject(shareToken);
  }

  // 设计模板相关端点
  @Get('templates')
  @ApiOperation({ summary: '获取设计模板列表' })
  @ApiQuery({ name: 'category', description: '模板类别', required: false })
  @ApiQuery({ name: 'style', description: '设计风格', required: false })
  @ApiQuery({ name: 'houseTypes', description: '适用房屋类型', required: false })
  @ApiQuery({ name: 'roomTypes', description: '适用房间类型', required: false })
  @ApiResponse({
    status: 200,
    description: '获取设计模板列表成功',
    type: [DesignTemplate],
  })
  async findAllTemplates(@Query() filters: any): Promise<DesignTemplate[]> {
    return this.designToolsService.findAllTemplates(filters);
  }

  @Get('templates/:id')
  @ApiOperation({ summary: '获取设计模板详情' })
  @ApiParam({ name: 'id', description: '模板ID' })
  @ApiResponse({
    status: 200,
    description: '获取设计模板详情成功',
    type: DesignTemplate,
  })
  @ApiResponse({
    status: 404,
    description: '模板不存在',
  })
  async findTemplateById(@Param('id') id: string): Promise<DesignTemplate> {
    return this.designToolsService.findTemplateById(id);
  }

  // 设计资产相关端点
  @Get('assets')
  @ApiOperation({ summary: '获取设计资产列表' })
  @ApiQuery({ name: 'type', description: '资产类型', required: false })
  @ApiQuery({ name: 'category', description: '资产分类', required: false })
  @ApiQuery({ name: 'suitableRooms', description: '适用房间', required: false })
  @ApiQuery({ name: 'styles', description: '设计风格', required: false })
  @ApiQuery({ name: 'isFree', description: '是否免费', required: false })
  @ApiResponse({
    status: 200,
    description: '获取设计资产列表成功',
    type: [DesignAsset],
  })
  async findAllAssets(@Query() filters: any): Promise<DesignAsset[]> {
    return this.designToolsService.findAllAssets(filters);
  }

  @Get('assets/:id')
  @ApiOperation({ summary: '获取设计资产详情' })
  @ApiParam({ name: 'id', description: '资产ID' })
  @ApiResponse({
    status: 200,
    description: '获取设计资产详情成功',
    type: DesignAsset,
  })
  @ApiResponse({
    status: 404,
    description: '资产不存在',
  })
  async findAssetById(@Param('id') id: string): Promise<DesignAsset> {
    return this.designToolsService.findAssetById(id);
  }
}
