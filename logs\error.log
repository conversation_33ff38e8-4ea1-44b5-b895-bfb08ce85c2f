{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:47:53.683Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:47:53.684Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:48:23.684Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:48:56.709Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:49:35.838Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:50:08.852Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:50:33.580Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:50:33.581Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:51:03.567Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:51:03.568Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:51:33.573Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:52:06.597Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:52:39.627Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:52:41.374Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:52:41.374Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:01.157Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:54:01.165Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:54:01.166Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:04.164Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:07.181Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:10.189Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:13.204Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:16.211Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:19.219Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:22.233Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:25.254Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:30.965Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:57:30.971Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:57:30.971Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:33.979Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:36.993Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:39.999Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:43.003Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:46.018Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:49.029Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:52.041Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:55.050Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:59:05.018Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:59:05.019Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:59:10.027Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:59:18.048Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:59:26.073Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:59:44.527Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:59:44.528Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:59:49.526Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:59:57.555Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:59:59.761Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:59:59.761Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:00:04.765Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:00:12.772Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:00:20.787Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:00:28.795Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:00:36.815Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:00:44.835Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:00:52.863Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T08:01:29.619Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T08:01:29.621Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:01:34.625Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:01:42.644Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:01:50.663Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:01:58.681Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:02:06.701Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:02:14.707Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:02:22.720Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:02:30.742Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:02:38.765Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T08:08:14.199Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T08:08:14.199Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:08:19.209Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:08:30.045Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:08:38.074Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:08:46.093Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:08:54.103Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:09:02.132Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:09:10.159Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:09:18.178Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T08:09:27.067Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T08:09:27.067Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:09:32.073Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:09:40.091Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T08:09:47.819Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T08:09:47.820Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:09:52.830Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:00.849Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:08.859Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:16.867Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:24.882Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:32.897Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:40.915Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:48.940Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:56.961Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T08:13:23.919Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T08:13:23.921Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:13:28.920Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:13:36.948Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:13:44.965Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:13:52.983Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:14:01.013Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:14:09.030Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:14:17.051Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T08:14:29.019Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T08:14:29.019Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:14:34.020Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:14:42.041Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:14:50.063Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:14:58.081Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:15:06.103Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:15:14.115Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:15:22.131Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:15:30.145Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:15:38.163Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T08:15:55.005Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T08:15:55.006Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:16:00.009Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:16:08.023Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:16:16.044Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:16:24.065Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:16:32.083Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:16:40.110Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:16:48.137Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:16:54.830Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:16:57.843Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:17:00.854Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:17:03.870Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:17:06.884Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:17:09.889Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:17:12.901Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:17:15.910Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:17:18.917Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:18:29.718Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:18:32.725Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:18:35.728Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:18:38.739Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:18:41.904Z"}
