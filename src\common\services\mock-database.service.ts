import { Injectable } from '@nestjs/common';
import { Types } from 'mongoose';

/**
 * 内存数据库服务 - 用于开发环境无数据库测试
 */
@Injectable()
export class MockDatabaseService {
  private collections: Map<string, Map<string, any>> = new Map();
  private counters: Map<string, number> = new Map();

  constructor() {
    // 初始化集合
    this.initializeCollections();
  }

  private initializeCollections() {
    const collectionNames = [
      'users',
      'projects', 
      'serviceproviders',
      'quotes',
      'notifications',
      'reviews',
      'payments',
      'tasks',
      'files'
    ];

    collectionNames.forEach(name => {
      this.collections.set(name, new Map());
      this.counters.set(name, 0);
    });

    // 添加一些示例数据
    this.seedData();
  }

  private seedData() {
    // 创建示例用户
    const userId1 = new Types.ObjectId().toString();
    const userId2 = new Types.ObjectId().toString();

    this.insert('users', {
      _id: userId1,
      email: '<EMAIL>',
      passwordHash: '$2b$10$example.hash.for.password123',
      userType: 'homeowner',
      profile: {
        firstName: '<PERSON>',
        lastName: 'Doe',
        phone: '******-555-0123'
      },
      isActive: true,
      isEmailVerified: true,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    this.insert('users', {
      _id: userId2,
      email: '<EMAIL>',
      passwordHash: '$2b$10$example.hash.for.password123',
      userType: 'service_provider',
      profile: {
        firstName: 'Jane',
        lastName: 'Smith',
        phone: '******-555-0124'
      },
      isActive: true,
      isEmailVerified: true,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // 创建示例项目
    const projectId = new Types.ObjectId().toString();
    this.insert('projects', {
      _id: projectId,
      homeownerId: userId1,
      title: '厨房装修项目',
      description: '现代化厨房装修，包括橱柜、台面和电器安装',
      category: 'kitchen',
      budget: {
        min: 15000,
        max: 25000,
        currency: 'CAD'
      },
      location: {
        address: '123 Main St, Toronto, ON',
        city: 'Toronto',
        province: 'ON',
        postalCode: 'M5V 3A8',
        coordinates: {
          lat: 43.6532,
          lng: -79.3832
        }
      },
      status: 'open',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    console.log('✅ Mock database initialized with sample data');
    console.log(`👤 Sample users: <EMAIL>, <EMAIL>`);
    console.log(`🏠 Sample project: Kitchen renovation`);
  }

  // 基础CRUD操作
  insert(collection: string, document: any): any {
    const coll = this.getCollection(collection);
    const id = document._id || new Types.ObjectId().toString();
    document._id = id;
    document.createdAt = document.createdAt || new Date();
    document.updatedAt = document.updatedAt || new Date();
    
    coll.set(id, { ...document });
    this.incrementCounter(collection);
    return document;
  }

  findById(collection: string, id: string): any {
    const coll = this.getCollection(collection);
    return coll.get(id) || null;
  }

  findOne(collection: string, query: any): any {
    const coll = this.getCollection(collection);
    for (const doc of coll.values()) {
      if (this.matchesQuery(doc, query)) {
        return doc;
      }
    }
    return null;
  }

  find(collection: string, query: any = {}, options: any = {}): any[] {
    const coll = this.getCollection(collection);
    let results: any[] = [];
    
    for (const doc of coll.values()) {
      if (this.matchesQuery(doc, query)) {
        results.push(doc);
      }
    }

    // 应用排序
    if (options.sort) {
      results = this.applySorting(results, options.sort);
    }

    // 应用分页
    if (options.skip) {
      results = results.slice(options.skip);
    }
    if (options.limit) {
      results = results.slice(0, options.limit);
    }

    return results;
  }

  updateById(collection: string, id: string, update: any): any {
    const coll = this.getCollection(collection);
    const doc = coll.get(id);
    if (!doc) return null;

    const updatedDoc = { ...doc, ...update, updatedAt: new Date() };
    coll.set(id, updatedDoc);
    return updatedDoc;
  }

  deleteById(collection: string, id: string): boolean {
    const coll = this.getCollection(collection);
    return coll.delete(id);
  }

  count(collection: string, query: any = {}): number {
    return this.find(collection, query).length;
  }

  // 辅助方法
  private getCollection(name: string): Map<string, any> {
    if (!this.collections.has(name)) {
      this.collections.set(name, new Map());
    }
    return this.collections.get(name)!;
  }

  private incrementCounter(collection: string): void {
    const current = this.counters.get(collection) || 0;
    this.counters.set(collection, current + 1);
  }

  private matchesQuery(document: any, query: any): boolean {
    if (!query || Object.keys(query).length === 0) return true;

    for (const [key, value] of Object.entries(query)) {
      if (key === '_id' && document._id !== value) return false;
      if (key === 'email' && document.email !== value) return false;
      if (key === 'userType' && document.userType !== value) return false;
      if (key === 'isActive' && document.isActive !== value) return false;
      // 添加更多查询条件匹配逻辑
    }

    return true;
  }

  private applySorting(results: any[], sort: any): any[] {
    return results.sort((a, b) => {
      for (const [key, direction] of Object.entries(sort)) {
        const aVal = a[key];
        const bVal = b[key];
        const dir = direction === -1 ? -1 : 1;

        if (aVal < bVal) return -1 * dir;
        if (aVal > bVal) return 1 * dir;
      }
      return 0;
    });
  }

  // 获取统计信息
  getStats(): any {
    const stats: any = {};
    for (const [name, coll] of this.collections.entries()) {
      stats[name] = {
        count: coll.size,
        total: this.counters.get(name) || 0
      };
    }
    return stats;
  }

  // 清空所有数据
  clear(): void {
    this.collections.clear();
    this.counters.clear();
    this.initializeCollections();
  }
}
