# HomeReno 开发环境启动脚本
Write-Host "🚀 启动 HomeReno 开发环境..." -ForegroundColor Green

# 设置环境变量
$env:NODE_ENV = "development"
$env:USE_MOCK_DATABASE = "false"

Write-Host "📋 环境变量设置:" -ForegroundColor Yellow
Write-Host "  NODE_ENV: $env:NODE_ENV"
Write-Host "  USE_MOCK_DATABASE: $env:USE_MOCK_DATABASE"

# 检查 .env.development 文件
if (Test-Path ".env.development") {
    Write-Host "✅ 找到 .env.development 配置文件" -ForegroundColor Green
} else {
    Write-Host "❌ 未找到 .env.development 配置文件" -ForegroundColor Red
    exit 1
}

# 安装依赖
Write-Host "📦 检查依赖..." -ForegroundColor Yellow
if (!(Test-Path "node_modules")) {
    Write-Host "📦 安装依赖..." -ForegroundColor Yellow
    npm install
}

# 启动应用
Write-Host "🚀 启动应用..." -ForegroundColor Green
npm run start:dev
