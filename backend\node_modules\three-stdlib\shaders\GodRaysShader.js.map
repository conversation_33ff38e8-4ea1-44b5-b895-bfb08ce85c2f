{"version": 3, "file": "GodRaysShader.js", "sources": ["../../src/shaders/GodRaysShader.ts"], "sourcesContent": ["import { Color, Vector3 } from 'three'\n\n/**\n * God-rays (crepuscular rays)\n *\n * Similar implementation to the one used by Crytek for CryEngine 2 [Sousa2008].\n * Blurs a mask generated from the depth map along radial lines emanating from the light\n * source. The blur repeatedly applies a blur filter of increasing support but constant\n * sample count to produce a blur filter with large support.\n *\n * My implementation performs 3 passes, similar to the implementation from Sousa. I found\n * just 6 samples per pass produced acceptible results. The blur is applied three times,\n * with decreasing filter support. The result is equivalent to a single pass with\n * 6*6*6 = 216 samples.\n *\n * References:\n *\n * Sousa2008 - Crysis Next Gen Effects, GDC2008, http://www.crytek.com/sites/default/files/GDC08_SousaT_CrysisEffects.ppt\n */\n\nexport const GodRaysDepthMaskShader = {\n  uniforms: {\n    tInput: {\n      value: null,\n    },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n     vUv = uv;\n     gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    varying vec2 vUv;\n\n    uniform sampler2D tInput;\n\n    void main() {\n\n    \tgl_FragColor = vec4( 1.0 ) - texture2D( tInput, vUv );\n\n    }\n  `,\n}\n\n/**\n * The god-ray generation shader.\n *\n * First pass:\n *\n * The depth map is blurred along radial lines towards the \"sun\". The\n * output is written to a temporary render target (I used a 1/4 sized\n * target).\n *\n * Pass two & three:\n *\n * The results of the previous pass are re-blurred, each time with a\n * decreased distance between samples.\n */\n\nexport const GodRaysGenerateShader = {\n  uniforms: {\n    tInput: {\n      value: null,\n    },\n    fStepSize: {\n      value: 1.0,\n    },\n    vSunPositionScreenSpace: {\n      value: /* @__PURE__ */ new Vector3(),\n    },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n     vUv = uv;\n     gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    #define TAPS_PER_PASS 6.0\n\n    varying vec2 vUv;\n\n    uniform sampler2D tInput;\n\n    uniform vec3 vSunPositionScreenSpace;\n    uniform float fStepSize; // filter step size\n\n    void main() {\n\n    // delta from current pixel to \"sun\" position\n\n    \tvec2 delta = vSunPositionScreenSpace.xy - vUv;\n    \tfloat dist = length( delta );\n\n    // Step vector (uv space)\n\n    \tvec2 stepv = fStepSize * delta / dist;\n\n    // Number of iterations between pixel and sun\n\n    \tfloat iters = dist/fStepSize;\n\n    \tvec2 uv = vUv.xy;\n    \tfloat col = 0.0;\n\n    // This breaks ANGLE in Chrome 22\n    //\t- see http://code.google.com/p/chromium/issues/detail?id=153105\n\n    /*\n\t\t// Unrolling didnt do much on my hardware (ATI Mobility Radeon 3450),\n\t\t// so ive just left the loop\n\n\t\tfor ( float i = 0.0; i < TAPS_PER_PASS; i += 1.0 ) {\n\n\t\t// Accumulate samples, making sure we dont walk past the light source.\n\n\t\t// The check for uv.y < 1 would not be necessary with \"border\" UV wrap\n\t\t// mode, with a black border color. I dont think this is currently\n\t\t// exposed by three.js. As a result there might be artifacts when the\n\t\t// sun is to the left, right or bottom of screen as these cases are\n\t\t// not specifically handled.\n\n\t\tcol += ( i <= iters && uv.y < 1.0 ? texture2D( tInput, uv ).r : 0.0 );\n\t\tuv += stepv;\n\n\t\t}\n\t\t*/\n\n    // Unrolling loop manually makes it work in ANGLE\n\n    \tfloat f = min( 1.0, max( vSunPositionScreenSpace.z / 1000.0, 0.0 ) ); // used to fade out godrays\n\n    \tif ( 0.0 <= iters && uv.y < 1.0 ) col += texture2D( tInput, uv ).r * f;\n    \tuv += stepv;\n\n    \tif ( 1.0 <= iters && uv.y < 1.0 ) col += texture2D( tInput, uv ).r * f;\n    \tuv += stepv;\n\n    \tif ( 2.0 <= iters && uv.y < 1.0 ) col += texture2D( tInput, uv ).r * f;\n    \tuv += stepv;\n\n    \tif ( 3.0 <= iters && uv.y < 1.0 ) col += texture2D( tInput, uv ).r * f;\n    \tuv += stepv;\n\n    \tif ( 4.0 <= iters && uv.y < 1.0 ) col += texture2D( tInput, uv ).r * f;\n    \tuv += stepv;\n\n    \tif ( 5.0 <= iters && uv.y < 1.0 ) col += texture2D( tInput, uv ).r * f;\n    \tuv += stepv;\n\n    // Should technically be dividing by iters but TAPS_PER_PASS smooths out\n    // objectionable artifacts, in particular near the sun position. The side\n    // effect is that the result is darker than it should be around the sun, as\n    // TAPS_PER_PASS is greater than the number of samples actually accumulated.\n    // When the result is inverted (in the shader godrays_combine this produces\n    // a slight bright spot at the position of the sun, even when it is occluded.\n\n    \tgl_FragColor = vec4( col/TAPS_PER_PASS );\n    \tgl_FragColor.a = 1.0;\n\n    }\n  `,\n}\n\n/**\n * Additively applies god rays from texture tGodRays to a background (tColors).\n * fGodRayIntensity attenuates the god rays.\n */\n\nexport const GodRaysCombineShader = {\n  uniforms: {\n    tColors: {\n      value: null,\n    },\n\n    tGodRays: {\n      value: null,\n    },\n\n    fGodRayIntensity: {\n      value: 0.69,\n    },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    varying vec2 vUv;\n\n    uniform sampler2D tColors;\n    uniform sampler2D tGodRays;\n\n    uniform float fGodRayIntensity;\n\n    void main() {\n\n    // Since THREE.MeshDepthMaterial renders foreground objects white and background\n    // objects black, the god-rays will be white streaks. Therefore value is inverted\n    // before being combined with tColors\n\n    \tgl_FragColor = texture2D( tColors, vUv ) + fGodRayIntensity * vec4( 1.0 - texture2D( tGodRays, vUv ).r );\n    \tgl_FragColor.a = 1.0;\n\n    }\n  `,\n}\n\n/**\n * A dodgy sun/sky shader. Makes a bright spot at the sun location. Would be\n * cheaper/faster/simpler to implement this as a simple sun sprite.\n */\n\nexport const GodRaysFakeSunShader = {\n  uniforms: {\n    vSunPositionScreenSpace: {\n      value: /* @__PURE__ */ new Vector3(),\n    },\n\n    fAspect: {\n      value: 1.0,\n    },\n\n    sunColor: {\n      value: /* @__PURE__ */ new Color(0xffee00),\n    },\n\n    bgColor: {\n      value: /* @__PURE__ */ new Color(0x000000),\n    },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    varying vec2 vUv;\n\n    uniform vec3 vSunPositionScreenSpace;\n    uniform float fAspect;\n\n    uniform vec3 sunColor;\n    uniform vec3 bgColor;\n\n    void main() {\n\n    \tvec2 diff = vUv - vSunPositionScreenSpace.xy;\n\n    // Correct for aspect ratio\n\n    \tdiff.x *= fAspect;\n\n    \tfloat prop = clamp( length( diff ) / 0.5, 0.0, 1.0 );\n    \tprop = 0.35 * pow( 1.0 - prop, 3.0 );\n\n    \tgl_FragColor.xyz = ( vSunPositionScreenSpace.z > 0.0 ) ? mix( sunColor, bgColor, 1.0 - prop ) : bgColor;\n    \tgl_FragColor.w = 1.0;\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";AAoBO,MAAM,yBAAyB;AAAA,EACpC,UAAU;AAAA,IACR,QAAQ;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAW7B;AAiBO,MAAM,wBAAwB;AAAA,EACnC,UAAU;AAAA,IACR,QAAQ;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,yBAAyB;AAAA,MACvB,2BAA2B,QAAQ;AAAA,IACrC;AAAA,EACF;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqF7B;AAOO,MAAM,uBAAuB;AAAA,EAClC,UAAU;AAAA,IACR,SAAS;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IAEA,UAAU;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IAEA,kBAAkB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmB7B;AAOO,MAAM,uBAAuB;AAAA,EAClC,UAAU;AAAA,IACR,yBAAyB;AAAA,MACvB,2BAA2B,QAAQ;AAAA,IACrC;AAAA,IAEA,SAAS;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IAEA,UAAU;AAAA,MACR,OAA2B,oBAAA,MAAM,QAAQ;AAAA,IAC3C;AAAA,IAEA,SAAS;AAAA,MACP,OAA2B,oBAAA,MAAM,CAAQ;AAAA,IAC3C;AAAA,EACF;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyB7B;"}