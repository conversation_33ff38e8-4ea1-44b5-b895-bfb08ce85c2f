import { registerAs } from '@nestjs/config';

export const fileConfig = registerAs('file', () => ({
  upload: {
    maxSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
    allowedTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ],
    destination: process.env.UPLOAD_DESTINATION || './uploads',
  },
  aws: {
    region: process.env.AWS_REGION || 'us-east-1',
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    bucket: process.env.AWS_S3_BUCKET,
    cloudFront: process.env.AWS_CLOUDFRONT_DOMAIN,
  },
  storage: {
    provider: process.env.STORAGE_PROVIDER || 'local', // 'local' | 'aws'
  },
}));
