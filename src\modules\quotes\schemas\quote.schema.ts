import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type QuoteDocument = Quote & Document;

export enum QuoteStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  ACCEPTED = 'accepted',
  DECLINED = 'declined',
  EXPIRED = 'expired',
  WITHDRAWN = 'withdrawn',
}

export enum PaymentScheduleType {
  UPFRONT = 'upfront',
  MILESTONE = 'milestone',
  COMPLETION = 'completion',
  CUSTOM = 'custom',
}

@Schema({ _id: false })
export class QuoteItem {
  @Prop({ required: true })
  @ApiProperty({ description: '项目名称' })
  name: string;

  @Prop()
  @ApiProperty({ description: '项目描述' })
  description?: string;

  @Prop({ required: true })
  @ApiProperty({ description: '数量' })
  quantity: number;

  @Prop()
  @ApiProperty({ description: '单位' })
  unit?: string;

  @Prop({ required: true })
  @ApiProperty({ description: '单价' })
  unitPrice: number;

  @Prop({ required: true })
  @ApiProperty({ description: '总价' })
  totalPrice: number;
}

@Schema({ _id: false })
export class PaymentSchedule {
  @Prop({ enum: PaymentScheduleType, required: true })
  @ApiProperty({ description: '付款类型', enum: PaymentScheduleType })
  type: PaymentScheduleType;

  @Prop({ required: true })
  @ApiProperty({ description: '付款描述' })
  description: string;

  @Prop({ required: true })
  @ApiProperty({ description: '付款金额' })
  amount: number;

  @Prop({ required: true })
  @ApiProperty({ description: '付款百分比' })
  percentage: number;

  @Prop()
  @ApiProperty({ description: '预期付款日期' })
  dueDate?: Date;

  @Prop({ default: false })
  @ApiProperty({ description: '是否已付款' })
  isPaid: boolean;

  @Prop()
  @ApiProperty({ description: '付款日期' })
  paidDate?: Date;
}

@Schema({ _id: false })
export class QuoteTerms {
  @Prop()
  @ApiProperty({ description: '有效期(天)' })
  validityDays?: number;

  @Prop()
  @ApiProperty({ description: '开始日期' })
  startDate?: Date;

  @Prop()
  @ApiProperty({ description: '预计完成日期' })
  estimatedCompletionDate?: Date;

  @Prop()
  @ApiProperty({ description: '保修期(月)' })
  warrantyMonths?: number;

  @Prop([String])
  @ApiProperty({ description: '包含的服务', type: [String] })
  includedServices?: string[];

  @Prop([String])
  @ApiProperty({ description: '不包含的服务', type: [String] })
  excludedServices?: string[];

  @Prop([String])
  @ApiProperty({ description: '特殊条款', type: [String] })
  specialTerms?: string[];
}

@Schema({ timestamps: true })
export class Quote {
  @Prop({ type: Types.ObjectId, required: true, ref: 'Project' })
  @ApiProperty({ description: '项目ID' })
  projectId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, required: true, ref: 'ServiceProvider' })
  @ApiProperty({ description: '服务商ID' })
  providerId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, required: true, ref: 'User' })
  @ApiProperty({ description: '客户ID' })
  customerId: Types.ObjectId;

  @Prop({ enum: QuoteStatus, default: QuoteStatus.DRAFT })
  @ApiProperty({ description: '报价状态', enum: QuoteStatus })
  status: QuoteStatus;

  @Prop({ required: true })
  @ApiProperty({ description: '报价标题' })
  title: string;

  @Prop()
  @ApiProperty({ description: '报价描述' })
  description?: string;

  @Prop([QuoteItem])
  @ApiProperty({ description: '报价项目', type: [QuoteItem] })
  items: QuoteItem[];

  @Prop({ required: true })
  @ApiProperty({ description: '小计' })
  subtotal: number;

  @Prop({ default: 0 })
  @ApiProperty({ description: '税费' })
  tax: number;

  @Prop({ default: 0 })
  @ApiProperty({ description: '折扣' })
  discount: number;

  @Prop({ required: true })
  @ApiProperty({ description: '总金额' })
  totalAmount: number;

  @Prop({ default: 'CAD' })
  @ApiProperty({ description: '货币单位' })
  currency: string;

  @Prop([PaymentSchedule])
  @ApiProperty({ description: '付款计划', type: [PaymentSchedule] })
  paymentSchedule?: PaymentSchedule[];

  @Prop({ type: QuoteTerms })
  @ApiProperty({ description: '报价条款', type: QuoteTerms })
  terms?: QuoteTerms;

  @Prop()
  @ApiProperty({ description: '备注' })
  notes?: string;

  @Prop([String])
  @ApiProperty({ description: '附件', type: [String] })
  attachments?: string[];

  @Prop()
  @ApiProperty({ description: '提交时间' })
  submittedAt?: Date;

  @Prop()
  @ApiProperty({ description: '过期时间' })
  expiresAt?: Date;

  @Prop()
  @ApiProperty({ description: '接受时间' })
  acceptedAt?: Date;

  @Prop()
  @ApiProperty({ description: '拒绝时间' })
  declinedAt?: Date;

  @Prop()
  @ApiProperty({ description: '拒绝原因' })
  declineReason?: string;
}

export const QuoteSchema = SchemaFactory.createForClass(Quote);

// 创建索引
QuoteSchema.index({ projectId: 1, providerId: 1 });
QuoteSchema.index({ customerId: 1, status: 1 });
QuoteSchema.index({ providerId: 1, status: 1 });
QuoteSchema.index({ status: 1, createdAt: -1 });
QuoteSchema.index({ expiresAt: 1 });

// 虚拟字段
QuoteSchema.virtual('isExpired').get(function() {
  return this.expiresAt && new Date() > this.expiresAt;
});

QuoteSchema.virtual('isActive').get(function() {
  const isExpired = this.expiresAt && new Date() > this.expiresAt;
  return this.status === QuoteStatus.SUBMITTED && !isExpired;
});

// 设置虚拟字段在JSON序列化时包含
QuoteSchema.set('toJSON', { virtuals: true });
QuoteSchema.set('toObject', { virtuals: true });
