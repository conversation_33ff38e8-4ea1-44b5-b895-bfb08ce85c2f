import { PartialType, ApiProperty } from '@nestjs/swagger';
import { CreateDesignProjectDto } from './create-design-project.dto';
import { IsEnum, IsOptional, IsArray, IsString } from 'class-validator';

export class UpdateDesignProjectDto extends PartialType(CreateDesignProjectDto) {
  @ApiProperty({ 
    description: '项目状态',
    enum: ['draft', 'in_progress', 'completed', 'shared'],
    required: false
  })
  @IsEnum(['draft', 'in_progress', 'completed', 'shared'])
  @IsOptional()
  status?: string;

  @ApiProperty({ description: '渲染图片URLs', type: [String], required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  renderedImages?: string[];
}
