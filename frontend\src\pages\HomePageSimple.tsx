import React from 'react';
import { Typo<PERSON>, <PERSON><PERSON>, Card, Row, Col, Space } from 'antd';
import { ProjectOutlined, TeamOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const HomePageSimple: React.FC = () => {
  return (
    <div style={{ padding: '20px' }}>
      <Title level={1}>Welcome to HomeReno</Title>
      <Paragraph>Canada's Top Renovation Platform</Paragraph>
      
      <Card style={{ marginBottom: '20px', textAlign: 'center' }}>
        <Title level={2}>Transform Your Home</Title>
        <Paragraph>Connect with certified professionals across Canada</Paragraph>
        <Space>
          <Button type="primary" size="large">
            Start Project
          </Button>
          <Button size="large">
            Find Contractors
          </Button>
        </Space>
      </Card>

      <Row gutter={16}>
        <Col span={12}>
          <Card>
            <TeamOutlined style={{ fontSize: 24, color: '#1890ff' }} />
            <Title level={4}>Certified Professionals</Title>
            <Paragraph>Licensed contractors across Canada</Paragraph>
          </Card>
        </Col>
        <Col span={12}>
          <Card>
            <ProjectOutlined style={{ fontSize: 24, color: '#52c41a' }} />
            <Title level={4}>3D Visualization</Title>
            <Paragraph>See your project come to life</Paragraph>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default HomePageSimple;
