import React from 'react';
import { Typography, But<PERSON>, Card, Row, Col, Space, Statistic, Divider } from 'antd';
import {
  ProjectOutlined,
  TeamOutlined,
  HomeOutlined,
  ToolOutlined,
  SafetyOutlined,
  StarOutlined,
  CheckCircleOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  UserOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { Title, Paragraph } = Typography;

const HomePageSimple: React.FC = () => {
  const navigate = useNavigate();

  const stats = [
    { title: 'Certified Contractors', value: 3200, suffix: '+' },
    { title: 'Completed Projects', value: 15800, suffix: '+' },
    { title: 'Canadian Cities', value: 180, suffix: '+' },
    { title: 'Customer Satisfaction', value: 98, suffix: '%' }
  ];

  const projectTypes = [
    { icon: <HomeOutlined />, title: 'Kitchen Renovation', description: 'Modern kitchen designs with professional installation' },
    { icon: <ToolOutlined />, title: 'Bathroom Remodel', description: 'Complete bathroom makeovers with expert plumbing' },
    { icon: <ProjectOutlined />, title: 'Home Addition', description: 'Expand your living space with quality construction' },
    { icon: <SafetyOutlined />, title: 'Roofing & Siding', description: 'Protect your home with professional roofing services' }
  ];

  return (
    <div style={{ width: '100%' }}>
      {/* Hero Section */}
      <div style={{
        textAlign: 'center',
        padding: '80px 0',
        background: 'linear-gradient(135deg, #1890ff 0%, #52c41a 100%)',
        color: 'white',
        borderRadius: '12px',
        marginBottom: '60px'
      }}>
        <Title level={1} style={{ color: 'white', fontSize: '3.5rem', marginBottom: 24 }}>
          Transform Your Home with Canada's #1 Renovation Platform
        </Title>
        <Paragraph style={{
          fontSize: '1.3rem',
          color: 'white',
          marginBottom: 40,
          maxWidth: 800,
          margin: '0 auto 40px'
        }}>
          Connect homeowners with certified contractors. Get quotes, manage projects, and bring your dream home to life with confidence.
        </Paragraph>

        {/* Dual CTA for different user types */}
        <Row gutter={32} justify="center">
          <Col>
            <Card style={{
              textAlign: 'center',
              padding: '20px',
              background: 'rgba(255,255,255,0.95)',
              border: 'none'
            }}>
              <UserOutlined style={{ fontSize: 32, color: '#1890ff', marginBottom: 16 }} />
              <Title level={4} style={{ color: '#1890ff', marginBottom: 16 }}>For Homeowners</Title>
              <Paragraph style={{ color: '#666', marginBottom: 20 }}>
                Find trusted contractors and manage your renovation project
              </Paragraph>
              <Button
                type="primary"
                size="large"
                onClick={() => navigate('/projects/create')}
                style={{ width: '100%' }}
              >
                Start Your Project
              </Button>
            </Card>
          </Col>
          <Col>
            <Card style={{
              textAlign: 'center',
              padding: '20px',
              background: 'rgba(255,255,255,0.95)',
              border: 'none'
            }}>
              <TeamOutlined style={{ fontSize: 32, color: '#52c41a', marginBottom: 16 }} />
              <Title level={4} style={{ color: '#52c41a', marginBottom: 16 }}>For Contractors</Title>
              <Paragraph style={{ color: '#666', marginBottom: 20 }}>
                Grow your business and connect with quality clients
              </Paragraph>
              <Button
                type="primary"
                size="large"
                onClick={() => navigate('/contractors/register')}
                style={{ width: '100%', backgroundColor: '#52c41a', borderColor: '#52c41a' }}
              >
                Join as Professional
              </Button>
            </Card>
          </Col>
        </Row>
      </div>

      {/* Statistics Section */}
      <div style={{ marginBottom: '60px' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: '40px' }}>
          Trusted by Thousands Across Canada
        </Title>
        <Row gutter={[32, 32]}>
          {stats.map((stat, index) => (
            <Col xs={12} sm={6} key={index}>
              <Card style={{ textAlign: 'center' }}>
                <Statistic
                  title={stat.title}
                  value={stat.value}
                  suffix={stat.suffix}
                  valueStyle={{ color: '#1890ff', fontSize: '2.5rem' }}
                />
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* Popular Project Types */}
      <div style={{ marginBottom: '60px' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: '40px' }}>
          Popular Renovation Projects
        </Title>
        <Row gutter={[24, 24]}>
          {projectTypes.map((project, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <Card
                hoverable
                style={{ textAlign: 'center', height: '100%' }}
                cover={
                  <div style={{
                    height: 120,
                    background: `linear-gradient(135deg, ${['#1890ff', '#52c41a', '#faad14', '#eb2f96'][index]} 0%, ${['#722ed1', '#13c2c2', '#fa8c16', '#f759ab'][index]} 100%)`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <div style={{ fontSize: 48, color: 'white' }}>
                      {project.icon}
                    </div>
                  </div>
                }
              >
                <Title level={4}>{project.title}</Title>
                <Paragraph style={{ color: '#666' }}>{project.description}</Paragraph>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* How It Works */}
      <div style={{ marginBottom: '60px' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: '40px' }}>
          How HomeReno Works
        </Title>
        <Row gutter={[32, 32]}>
          <Col xs={24} md={8}>
            <Card style={{ textAlign: 'center', height: '100%' }}>
              <div style={{
                width: 80,
                height: 80,
                borderRadius: '50%',
                background: '#1890ff',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 20px'
              }}>
                <ProjectOutlined style={{ fontSize: 32, color: 'white' }} />
              </div>
              <Title level={4}>1. Post Your Project</Title>
              <Paragraph>Describe your renovation needs and get matched with qualified contractors</Paragraph>
            </Card>
          </Col>
          <Col xs={24} md={8}>
            <Card style={{ textAlign: 'center', height: '100%' }}>
              <div style={{
                width: 80,
                height: 80,
                borderRadius: '50%',
                background: '#52c41a',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 20px'
              }}>
                <StarOutlined style={{ fontSize: 32, color: 'white' }} />
              </div>
              <Title level={4}>2. Compare Quotes</Title>
              <Paragraph>Receive detailed quotes from verified professionals and compare their proposals</Paragraph>
            </Card>
          </Col>
          <Col xs={24} md={8}>
            <Card style={{ textAlign: 'center', height: '100%' }}>
              <div style={{
                width: 80,
                height: 80,
                borderRadius: '50%',
                background: '#faad14',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 20px'
              }}>
                <CheckCircleOutlined style={{ fontSize: 32, color: 'white' }} />
              </div>
              <Title level={4}>3. Complete Project</Title>
              <Paragraph>Work with your chosen contractor and track progress through our platform</Paragraph>
            </Card>
          </Col>
        </Row>
      </div>

      {/* Why Choose HomeReno */}
      <div style={{ marginBottom: '60px' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: '40px' }}>
          Why Choose HomeReno?
        </Title>
        <Row gutter={[24, 24]}>
          <Col xs={24} sm={12} lg={6}>
            <Card style={{ textAlign: 'center', height: '100%' }}>
              <SafetyOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
              <Title level={4}>Verified Contractors</Title>
              <Paragraph>All contractors are licensed, insured, and background-checked</Paragraph>
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card style={{ textAlign: 'center', height: '100%' }}>
              <DollarOutlined style={{ fontSize: 48, color: '#52c41a', marginBottom: 16 }} />
              <Title level={4}>Secure Payments</Title>
              <Paragraph>Milestone-based payments with escrow protection</Paragraph>
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card style={{ textAlign: 'center', height: '100%' }}>
              <ClockCircleOutlined style={{ fontSize: 48, color: '#faad14', marginBottom: 16 }} />
              <Title level={4}>Project Management</Title>
              <Paragraph>Track progress, communicate, and manage timelines in one place</Paragraph>
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card style={{ textAlign: 'center', height: '100%' }}>
              <StarOutlined style={{ fontSize: 48, color: '#eb2f96', marginBottom: 16 }} />
              <Title level={4}>Quality Guarantee</Title>
              <Paragraph>Every project backed by our satisfaction guarantee</Paragraph>
            </Card>
          </Col>
        </Row>
      </div>

      {/* Final CTA */}
      <div style={{
        textAlign: 'center',
        padding: '60px 40px',
        background: '#f8f9fa',
        borderRadius: '12px'
      }}>
        <Title level={2} style={{ marginBottom: '24px' }}>
          Ready to Start Your Renovation Journey?
        </Title>
        <Paragraph style={{ fontSize: '1.1rem', color: '#666', marginBottom: '40px', maxWidth: 600, margin: '0 auto 40px' }}>
          Join thousands of Canadian homeowners and contractors who trust HomeReno for their renovation projects
        </Paragraph>
        <Space size="large">
          <Button
            type="primary"
            size="large"
            onClick={() => navigate('/projects/create')}
            style={{ height: 50, fontSize: '1.1rem', padding: '0 40px' }}
          >
            Start Your Project Today
          </Button>
          <Button
            size="large"
            onClick={() => navigate('/contractors')}
            style={{ height: 50, fontSize: '1.1rem', padding: '0 40px' }}
          >
            Browse Contractors
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default HomePageSimple;
