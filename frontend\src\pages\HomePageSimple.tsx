import React from 'react';
import { Typography, <PERSON><PERSON>, Card, Row, Col, Space } from 'antd';
import { ProjectOutlined, TeamOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const HomePageSimple: React.FC = () => {
  return (
    <div style={{ width: '100%' }}>
      {/* Hero Section */}
      <div style={{
        textAlign: 'center',
        padding: '60px 0',
        background: 'linear-gradient(135deg, #1890ff 0%, #722ed1 100%)',
        color: 'white',
        borderRadius: '12px',
        marginBottom: '40px'
      }}>
        <Title level={1} style={{ color: 'white', fontSize: '3rem', marginBottom: 16 }}>
          Welcome to HomeReno
        </Title>
        <Paragraph style={{
          fontSize: '1.2rem',
          color: 'white',
          marginBottom: 32,
          maxWidth: 600,
          margin: '0 auto 32px'
        }}>
          Canada's Top Renovation Platform - Connect with certified professionals across Canada
        </Paragraph>
        <Space size="large">
          <Button type="primary" size="large" style={{
            backgroundColor: 'white',
            color: '#1890ff',
            border: 'none',
            height: 48
          }}>
            Start Your Project
          </Button>
          <Button size="large" style={{
            backgroundColor: 'transparent',
            borderColor: 'white',
            color: 'white',
            height: 48
          }}>
            Find Professionals
          </Button>
        </Space>
      </div>

      {/* Features Section */}
      <Row gutter={[24, 24]}>
        <Col xs={24} sm={12} lg={6}>
          <Card style={{ textAlign: 'center', height: '100%' }}>
            <TeamOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
            <Title level={4}>Certified Professionals</Title>
            <Paragraph>Licensed contractors and designers across Canada</Paragraph>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card style={{ textAlign: 'center', height: '100%' }}>
            <ProjectOutlined style={{ fontSize: 48, color: '#52c41a', marginBottom: 16 }} />
            <Title level={4}>3D Visualization</Title>
            <Paragraph>See your renovation project come to life with advanced 3D tools</Paragraph>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card style={{ textAlign: 'center', height: '100%' }}>
            <TeamOutlined style={{ fontSize: 48, color: '#faad14', marginBottom: 16 }} />
            <Title level={4}>Quality Guarantee</Title>
            <Paragraph>Every project backed by our quality assurance program</Paragraph>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card style={{ textAlign: 'center', height: '100%' }}>
            <ProjectOutlined style={{ fontSize: 48, color: '#eb2f96', marginBottom: 16 }} />
            <Title level={4}>Secure Payments</Title>
            <Paragraph>Protected milestone payments with escrow service</Paragraph>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default HomePageSimple;
