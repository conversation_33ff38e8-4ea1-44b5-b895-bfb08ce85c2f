import {
  Controller,
  Get,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
} from '@nestjs/swagger';
import { I18nService } from 'nestjs-i18n';
import { Lang } from './decorators/lang.decorator';
import { I18nResponseInterceptor } from './interceptors/i18n-response.interceptor';

@ApiTags('I18n')
@Controller('i18n')
@UseInterceptors(I18nResponseInterceptor)
export class I18nController {
  constructor(private readonly i18n: I18nService) {}

  @Get('languages')
  @ApiOperation({ summary: '获取支持的语言列表' })
  @ApiResponse({
    status: 200,
    description: '支持的语言列表',
    schema: {
      type: 'object',
      properties: {
        languages: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              code: { type: 'string', example: 'en' },
              name: { type: 'string', example: 'English' },
              nativeName: { type: 'string', example: 'English' },
            },
          },
        },
        default: { type: 'string', example: 'en' },
      },
    },
  })
  getSupportedLanguages() {
    return {
      languages: [
        {
          code: 'en',
          name: 'English',
          nativeName: 'English',
        },
        {
          code: 'zh',
          name: 'Chinese',
          nativeName: '中文',
        },
      ],
      default: 'en',
    };
  }

  @Get('translate')
  @ApiOperation({ summary: '翻译指定的键' })
  @ApiResponse({
    status: 200,
    description: '翻译结果',
    schema: {
      type: 'object',
      properties: {
        key: { type: 'string' },
        translation: { type: 'string' },
        language: { type: 'string' },
      },
    },
  })
  @ApiQuery({ name: 'key', description: '翻译键', example: 'common.success' })
  @ApiQuery({ name: 'lang', required: false, description: '目标语言', example: 'zh' })
  translate(
    @Query('key') key: string,
    @Lang() lang: string,
  ) {
    const translation = this.i18n.translate(key, { lang });
    
    return {
      key,
      translation,
      language: lang,
    };
  }

  @Get('demo')
  @ApiOperation({ summary: '多语言演示' })
  @ApiResponse({
    status: 200,
    description: '多语言演示数据',
  })
  @ApiQuery({ name: 'lang', required: false, description: '语言', example: 'zh' })
  getDemo(@Lang() lang: string) {
    return {
      message: this.i18n.translate('common.success', { lang }),
      welcome: this.i18n.translate('user.messages.loginSuccess', { lang }),
      project: {
        title: this.i18n.translate('project.title', { lang }),
        create: this.i18n.translate('project.create', { lang }),
        status: {
          draft: this.i18n.translate('project.status.draft', { lang }),
          published: this.i18n.translate('project.status.published', { lang }),
          completed: this.i18n.translate('project.status.completed', { lang }),
        },
      },
      user: {
        profile: this.i18n.translate('user.profile.title', { lang }),
        account: this.i18n.translate('user.account.title', { lang }),
        preferences: this.i18n.translate('user.preferences.title', { lang }),
      },
      common: {
        save: this.i18n.translate('common.save', { lang }),
        cancel: this.i18n.translate('common.cancel', { lang }),
        delete: this.i18n.translate('common.delete', { lang }),
        edit: this.i18n.translate('common.edit', { lang }),
        create: this.i18n.translate('common.create', { lang }),
      },
      language: lang,
      timestamp: new Date().toISOString(),
    };
  }
}
