# HomeReno 数据库设计文档

## 1. 数据库架构概述

### 1.1 数据库选择
- **主数据库**: MongoDB 6.0+ (文档数据库，适合复杂嵌套数据)
- **缓存数据库**: Redis 7.0+ (内存数据库，用于缓存和会话)
- **搜索引擎**: Elasticsearch 8.0+ (全文搜索和复杂查询)

### 1.2 数据分布策略
- **地理分片**: 按省份/地区分片
- **功能分片**: 按业务模块分片
- **时间分片**: 历史数据按时间分片

## 2. 核心数据模型

### 2.1 用户模型 (Users Collection)

```javascript
{
  _id: ObjectId,
  email: String, // 唯一索引
  passwordHash: String,
  userType: String, // "homeowner" | "service_provider" | "admin"
  status: String, // "active" | "inactive" | "suspended"
  
  profile: {
    firstName: String,
    lastName: String,
    phone: String,
    avatar: String, // 头像URL
    language: String, // "en" | "zh" | "fr"
    timezone: String,
    
    // 地址信息
    address: {
      street: String,
      city: String,
      province: String,
      postalCode: String,
      country: String, // 默认 "CA"
      coordinates: {
        lat: Number,
        lng: Number
      }
    }
  },
  
  // 用户偏好设置
  preferences: {
    notifications: {
      email: Boolean,
      sms: Boolean,
      push: Boolean
    },
    privacy: {
      showProfile: Boolean,
      showLocation: Boolean
    }
  },
  
  // 认证相关
  auth: {
    emailVerified: Boolean,
    phoneVerified: Boolean,
    lastLogin: Date,
    loginAttempts: Number,
    lockUntil: Date
  },
  
  createdAt: Date,
  updatedAt: Date
}
```

### 2.2 服务商扩展信息 (ServiceProviders Collection)

```javascript
{
  _id: ObjectId,
  userId: ObjectId, // 关联Users表
  
  businessInfo: {
    businessName: String,
    businessNumber: String, // 营业执照号
    description: String,
    website: String,
    establishedYear: Number,
    employeeCount: Number
  },
  
  services: [{
    category: String, // "renovation" | "kitchen" | "bathroom" | "flooring" 等
    subcategories: [String],
    priceRange: String, // "$" | "$$" | "$$$" | "$$$$"
    description: String
  }],
  
  serviceAreas: [{
    city: String,
    province: String,
    radius: Number // 服务半径(km)
  }],
  
  // 资质认证
  certifications: [{
    name: String,
    issuer: String,
    number: String,
    issuedDate: Date,
    expiryDate: Date,
    documentUrl: String
  }],
  
  // 保险信息
  insurance: {
    liability: {
      provider: String,
      policyNumber: String,
      coverage: Number,
      expiryDate: Date
    },
    workersComp: {
      provider: String,
      policyNumber: String,
      expiryDate: Date
    }
  },
  
  // 作品集
  portfolio: [{
    title: String,
    description: String,
    category: String,
    images: [String], // 图片URL数组
    beforeAfter: {
      before: [String],
      after: [String]
    },
    completedDate: Date,
    budget: {
      min: Number,
      max: Number
    }
  }],
  
  // 评价统计
  ratings: {
    overall: Number, // 总体评分
    quality: Number, // 质量评分
    communication: Number, // 沟通评分
    timeliness: Number, // 时效评分
    value: Number, // 性价比评分
    reviewCount: Number,
    totalRating: Number // 用于计算平均分
  },
  
  // 业务统计
  stats: {
    projectsCompleted: Number,
    responseTime: Number, // 平均响应时间(小时)
    onTimeRate: Number, // 按时完成率
    repeatCustomerRate: Number
  },
  
  // 可用性
  availability: {
    workingHours: {
      monday: { start: String, end: String, available: Boolean },
      tuesday: { start: String, end: String, available: Boolean },
      // ... 其他工作日
    },
    busyPeriods: [{
      startDate: Date,
      endDate: Date,
      reason: String
    }]
  },
  
  isVerified: Boolean,
  verificationDate: Date,
  status: String, // "active" | "inactive" | "suspended"
  
  createdAt: Date,
  updatedAt: Date
}
```

### 2.3 项目模型 (Projects Collection)

```javascript
{
  _id: ObjectId,
  title: String,
  description: String,
  category: String, // "full_renovation" | "kitchen" | "bathroom" | "flooring" 等
  status: String, // "draft" | "active" | "in_progress" | "completed" | "cancelled"
  
  // 项目所有者
  owner: {
    userId: ObjectId,
    name: String,
    email: String,
    phone: String
  },
  
  // 预算信息
  budget: {
    min: Number,
    max: Number,
    currency: String, // "CAD"
    flexibility: String // "firm" | "flexible" | "negotiable"
  },
  
  // 时间安排
  timeline: {
    preferredStartDate: Date,
    expectedEndDate: Date,
    flexibility: String, // "firm" | "flexible"
    urgency: String // "low" | "medium" | "high"
  },
  
  // 位置信息
  location: {
    address: String,
    city: String,
    province: String,
    postalCode: String,
    coordinates: {
      lat: Number,
      lng: Number
    },
    accessNotes: String // 进入说明
  },
  
  // 项目要求
  requirements: {
    area: Number, // 面积(平方英尺)
    rooms: Number,
    style: String, // "modern" | "traditional" | "contemporary" 等
    specialRequirements: [String],
    materials: {
      preferences: [String],
      restrictions: [String]
    },
    permits: {
      required: Boolean,
      obtained: Boolean,
      notes: String
    }
  },
  
  // 附件
  attachments: [{
    type: String, // "image" | "document" | "floorplan"
    url: String,
    filename: String,
    description: String,
    uploadedAt: Date
  }],
  
  // 匹配的服务商
  matches: [{
    providerId: ObjectId,
    matchScore: Number, // 匹配度分数
    matchedAt: Date,
    status: String // "matched" | "contacted" | "declined"
  }],
  
  // 收到的报价
  quotes: [{
    quoteId: ObjectId,
    providerId: ObjectId,
    status: String, // "pending" | "accepted" | "declined" | "expired"
    submittedAt: Date
  }],
  
  // 选中的服务商
  selectedProvider: {
    providerId: ObjectId,
    quoteId: ObjectId,
    selectedAt: Date,
    contractId: ObjectId
  },
  
  // 项目进度
  progress: {
    currentPhase: String,
    completionPercentage: Number,
    milestones: [{
      name: String,
      status: String, // "pending" | "in_progress" | "completed"
      scheduledDate: Date,
      completedDate: Date
    }]
  },
  
  createdAt: Date,
  updatedAt: Date,
  publishedAt: Date,
  completedAt: Date
}
```

### 2.4 报价模型 (Quotes Collection)

```javascript
{
  _id: ObjectId,
  projectId: ObjectId,
  providerId: ObjectId,
  
  // 报价详情
  items: [{
    category: String, // "材料费" | "人工费" | "设备费" 等
    description: String,
    quantity: Number,
    unit: String,
    unitPrice: Number,
    total: Number
  }],
  
  pricing: {
    subtotal: Number,
    tax: Number,
    discount: Number,
    total: Number,
    currency: String
  },
  
  // 时间安排
  timeline: {
    startDate: Date,
    duration: Number, // 工期(天)
    milestones: [{
      name: String,
      description: String,
      scheduledDate: Date,
      duration: Number
    }]
  },
  
  // 支付计划
  paymentSchedule: [{
    milestone: String,
    percentage: Number,
    amount: Number,
    dueDate: Date
  }],
  
  // 条款和条件
  terms: {
    warranty: String,
    materials: String,
    changes: String,
    cancellation: String,
    additional: String
  },
  
  // 附件
  attachments: [{
    type: String,
    url: String,
    filename: String,
    description: String
  }],
  
  status: String, // "draft" | "submitted" | "accepted" | "declined" | "expired"
  validUntil: Date,
  notes: String,
  
  submittedAt: Date,
  respondedAt: Date,
  createdAt: Date,
  updatedAt: Date
}
```

### 2.5 合同模型 (Contracts Collection)

```javascript
{
  _id: ObjectId,
  projectId: ObjectId,
  quoteId: ObjectId,
  homeownerId: ObjectId,
  providerId: ObjectId,
  
  contractNumber: String, // 合同编号
  
  // 合同条款
  terms: {
    scope: String, // 工作范围
    timeline: {
      startDate: Date,
      endDate: Date,
      milestones: [Object]
    },
    pricing: Object, // 从报价复制
    paymentSchedule: [Object],
    warranty: String,
    materials: String,
    changes: String,
    cancellation: String,
    disputes: String
  },
  
  // 签名信息
  signatures: {
    homeowner: {
      signed: Boolean,
      signedAt: Date,
      signature: String, // 电子签名
      ipAddress: String
    },
    provider: {
      signed: Boolean,
      signedAt: Date,
      signature: String,
      ipAddress: String
    }
  },
  
  status: String, // "draft" | "pending_signatures" | "active" | "completed" | "cancelled"
  
  createdAt: Date,
  updatedAt: Date,
  signedAt: Date,
  completedAt: Date
}
```

### 2.6 支付模型 (Payments Collection)

```javascript
{
  _id: ObjectId,
  projectId: ObjectId,
  contractId: ObjectId,
  payerId: ObjectId, // 付款人
  payeeId: ObjectId, // 收款人
  
  amount: Number,
  currency: String,
  
  // 支付信息
  paymentInfo: {
    milestone: String,
    description: String,
    dueDate: Date,
    paymentMethod: String, // "credit_card" | "bank_transfer" | "check"
  },
  
  // 第三方支付信息
  gateway: {
    provider: String, // "stripe" | "paypal"
    transactionId: String,
    paymentIntentId: String,
    clientSecret: String
  },
  
  // 托管信息
  escrow: {
    held: Boolean,
    releaseConditions: String,
    releasedAt: Date,
    releasedBy: ObjectId
  },
  
  status: String, // "pending" | "processing" | "succeeded" | "failed" | "cancelled" | "refunded"
  
  // 时间戳
  createdAt: Date,
  updatedAt: Date,
  paidAt: Date,
  releasedAt: Date
}
```

## 3. 索引设计

### 3.1 Users Collection 索引
```javascript
// 唯一索引
db.users.createIndex({ "email": 1 }, { unique: true })

// 复合索引
db.users.createIndex({ "userType": 1, "status": 1 })
db.users.createIndex({ "profile.address.city": 1, "profile.address.province": 1 })

// 地理位置索引
db.users.createIndex({ "profile.address.coordinates": "2dsphere" })
```

### 3.2 ServiceProviders Collection 索引
```javascript
// 复合索引
db.serviceproviders.createIndex({ "userId": 1 }, { unique: true })
db.serviceproviders.createIndex({ "services.category": 1, "status": 1 })
db.serviceproviders.createIndex({ "ratings.overall": -1, "ratings.reviewCount": -1 })

// 地理位置索引
db.serviceproviders.createIndex({ "serviceAreas.coordinates": "2dsphere" })

// 文本搜索索引
db.serviceproviders.createIndex({
  "businessInfo.businessName": "text",
  "businessInfo.description": "text",
  "services.description": "text"
})
```

### 3.3 Projects Collection 索引
```javascript
// 复合索引
db.projects.createIndex({ "owner.userId": 1, "status": 1 })
db.projects.createIndex({ "category": 1, "status": 1, "createdAt": -1 })
db.projects.createIndex({ "status": 1, "publishedAt": -1 })

// 地理位置索引
db.projects.createIndex({ "location.coordinates": "2dsphere" })

// 预算范围索引
db.projects.createIndex({ "budget.min": 1, "budget.max": 1 })
```

## 4. 数据关系图

```mermaid
erDiagram
    USERS ||--o{ PROJECTS : creates
    USERS ||--o| SERVICE_PROVIDERS : extends
    USERS ||--o{ REVIEWS : writes
    USERS ||--o{ PAYMENTS : makes
    
    SERVICE_PROVIDERS ||--o{ QUOTES : submits
    SERVICE_PROVIDERS ||--o{ CONTRACTS : signs
    SERVICE_PROVIDERS ||--o{ REVIEWS : receives
    
    PROJECTS ||--o{ QUOTES : receives
    PROJECTS ||--|| CONTRACTS : generates
    PROJECTS ||--o{ PAYMENTS : requires
    PROJECTS ||--o{ TASKS : contains
    
    QUOTES ||--o| CONTRACTS : becomes
    
    CONTRACTS ||--o{ PAYMENTS : schedules
    CONTRACTS ||--o{ TASKS : defines
    
    PAYMENTS ||--o{ TRANSACTIONS : records
```

## 5. 数据验证规则

### 5.1 用户数据验证
- Email格式验证
- 密码强度验证(最少8位，包含大小写字母和数字)
- 电话号码格式验证
- 邮政编码格式验证

### 5.2 项目数据验证
- 预算范围合理性验证(min <= max)
- 时间范围合理性验证(startDate <= endDate)
- 地址格式验证
- 附件大小和格式限制

### 5.3 支付数据验证
- 金额必须为正数
- 货币代码验证
- 支付方式枚举验证

### 2.7 评价模型 (Reviews Collection)

```javascript
{
  _id: ObjectId,
  projectId: ObjectId,
  reviewerId: ObjectId, // 评价人
  revieweeId: ObjectId, // 被评价人
  revieweeType: String, // "service_provider" | "homeowner"

  // 评分
  ratings: {
    overall: Number, // 1-5
    quality: Number,
    communication: Number,
    timeliness: Number,
    value: Number, // 性价比
    professionalism: Number
  },

  // 评价内容
  content: {
    title: String,
    description: String,
    pros: [String], // 优点
    cons: [String], // 缺点
    recommendation: Boolean // 是否推荐
  },

  // 附件
  attachments: [{
    type: String, // "image" | "video"
    url: String,
    description: String
  }],

  // 回复
  response: {
    content: String,
    respondedAt: Date,
    respondedBy: ObjectId
  },

  status: String, // "published" | "hidden" | "flagged"
  isVerified: Boolean, // 是否已验证真实性

  createdAt: Date,
  updatedAt: Date
}
```

### 2.8 任务模型 (Tasks Collection)

```javascript
{
  _id: ObjectId,
  projectId: ObjectId,
  contractId: ObjectId,

  title: String,
  description: String,
  category: String, // "demolition" | "electrical" | "plumbing" | "painting" 等

  // 任务状态
  status: String, // "pending" | "in_progress" | "completed" | "on_hold"
  priority: String, // "low" | "medium" | "high" | "urgent"

  // 时间安排
  schedule: {
    startDate: Date,
    endDate: Date,
    estimatedHours: Number,
    actualHours: Number
  },

  // 负责人
  assignee: {
    userId: ObjectId,
    name: String,
    role: String // "contractor" | "subcontractor" | "inspector"
  },

  // 依赖关系
  dependencies: [{
    taskId: ObjectId,
    type: String // "finish_to_start" | "start_to_start"
  }],

  // 材料需求
  materials: [{
    name: String,
    quantity: Number,
    unit: String,
    status: String // "ordered" | "delivered" | "used"
  }],

  // 进度更新
  updates: [{
    content: String,
    images: [String],
    updatedBy: ObjectId,
    updatedAt: Date,
    progressPercentage: Number
  }],

  // 质量检查
  qualityChecks: [{
    checklistId: ObjectId,
    checkedBy: ObjectId,
    checkedAt: Date,
    passed: Boolean,
    notes: String
  }],

  createdAt: Date,
  updatedAt: Date,
  completedAt: Date
}
```

### 2.9 通知模型 (Notifications Collection)

```javascript
{
  _id: ObjectId,
  userId: ObjectId,

  type: String, // "new_quote" | "payment_due" | "task_completed" 等
  title: String,
  message: String,

  // 通知数据
  data: {
    projectId: ObjectId,
    quoteId: ObjectId,
    taskId: ObjectId,
    // 其他相关ID
  },

  // 通知渠道
  channels: {
    inApp: Boolean,
    email: Boolean,
    sms: Boolean,
    push: Boolean
  },

  // 状态
  status: String, // "pending" | "sent" | "delivered" | "failed"
  isRead: Boolean,
  readAt: Date,

  // 发送状态
  delivery: {
    email: {
      sent: Boolean,
      sentAt: Date,
      delivered: Boolean,
      deliveredAt: Date
    },
    sms: {
      sent: Boolean,
      sentAt: Date,
      delivered: Boolean,
      deliveredAt: Date
    },
    push: {
      sent: Boolean,
      sentAt: Date,
      delivered: Boolean,
      deliveredAt: Date
    }
  },

  createdAt: Date,
  updatedAt: Date
}
```

### 2.10 文件模型 (Files Collection)

```javascript
{
  _id: ObjectId,
  filename: String,
  originalName: String,
  mimeType: String,
  size: Number,

  // 存储信息
  storage: {
    provider: String, // "aws_s3" | "azure_blob"
    bucket: String,
    key: String,
    url: String,
    cdnUrl: String
  },

  // 文件分类
  category: String, // "avatar" | "project_attachment" | "portfolio" | "document"

  // 关联信息
  relatedTo: {
    entityType: String, // "user" | "project" | "quote" | "task"
    entityId: ObjectId
  },

  // 上传信息
  uploadedBy: ObjectId,
  uploadedAt: Date,

  // 处理状态
  processing: {
    status: String, // "pending" | "processing" | "completed" | "failed"
    thumbnails: [{
      size: String, // "small" | "medium" | "large"
      url: String
    }],
    metadata: Object // 图片EXIF信息等
  },

  // 访问控制
  access: {
    public: Boolean,
    allowedUsers: [ObjectId]
  },

  createdAt: Date,
  updatedAt: Date
}
```

## 3. 扩展索引设计

### 3.4 Reviews Collection 索引
```javascript
db.reviews.createIndex({ "revieweeId": 1, "status": 1, "createdAt": -1 })
db.reviews.createIndex({ "projectId": 1 })
db.reviews.createIndex({ "ratings.overall": -1 })
```

### 3.5 Tasks Collection 索引
```javascript
db.tasks.createIndex({ "projectId": 1, "status": 1 })
db.tasks.createIndex({ "assignee.userId": 1, "status": 1 })
db.tasks.createIndex({ "schedule.startDate": 1, "schedule.endDate": 1 })
```

### 3.6 Notifications Collection 索引
```javascript
db.notifications.createIndex({ "userId": 1, "isRead": 1, "createdAt": -1 })
db.notifications.createIndex({ "type": 1, "status": 1 })
db.notifications.createIndex({ "createdAt": 1 }, { expireAfterSeconds: 2592000 }) // 30天后自动删除
```

## 4. 数据分片策略

### 4.1 分片键选择
- **Users**: 按province分片
- **Projects**: 按location.province分片
- **ServiceProviders**: 按serviceAreas.province分片
- **Reviews**: 按revieweeId分片
- **Payments**: 按projectId分片

### 4.2 分片配置
```javascript
// 启用分片
sh.enableSharding("homereno")

// 配置分片键
sh.shardCollection("homereno.users", { "profile.address.province": 1 })
sh.shardCollection("homereno.projects", { "location.province": 1 })
sh.shardCollection("homereno.serviceproviders", { "serviceAreas.province": 1 })
```

## 5. 数据生命周期管理

### 5.1 数据归档策略
- **已完成项目**: 2年后归档到冷存储
- **通知记录**: 30天后自动删除
- **日志数据**: 90天后归档
- **文件数据**: 根据项目状态决定保留期

### 5.2 数据清理规则
```javascript
// 自动删除过期通知
db.notifications.createIndex(
  { "createdAt": 1 },
  { expireAfterSeconds: 2592000 }
)

// 自动删除过期会话
db.sessions.createIndex(
  { "expiresAt": 1 },
  { expireAfterSeconds: 0 }
)
```

## 6. 数据备份策略

### 6.1 备份频率
- 全量备份：每日凌晨2点
- 增量备份：每4小时
- 事务日志备份：每15分钟

### 6.2 备份保留
- 日备份：保留30天
- 周备份：保留12周
- 月备份：保留12个月
- 年备份：永久保留

### 6.3 灾难恢复
- RTO (恢复时间目标)：4小时
- RPO (恢复点目标)：15分钟
- 异地备份：每日同步到备用数据中心

## 7. 性能优化建议

### 7.1 查询优化
- 使用复合索引优化常用查询
- 避免全表扫描
- 使用聚合管道优化复杂查询
- 实施查询结果缓存

### 7.2 写入优化
- 批量写入操作
- 使用upsert减少查询次数
- 合理设置写关注级别
- 避免热点分片

### 7.3 存储优化
- 定期压缩集合
- 清理无用索引
- 监控存储使用情况
- 实施数据归档策略
