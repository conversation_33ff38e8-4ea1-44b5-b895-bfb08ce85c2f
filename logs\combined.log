{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:47:53.614Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:47:53.652Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:47:53.653Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:47:53.653Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:47:53.654Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:47:53.655Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:47:53.655Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:47:53.655Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:47:53.656Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:47:53.657Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:47:53.658Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:47:53.658Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:47:53.659Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:47:53.659Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:47:53.679Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:47:53.680Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:47:53.680Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:47:53.681Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:47:53.683Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:47:53.684Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:47:53.686Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:47:53.686Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:48:23.685Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:48:56.709Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:49:35.839Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:50:08.852Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:50:33.512Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:50:33.550Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:50:33.551Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:50:33.551Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:50:33.552Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:50:33.552Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:50:33.552Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:50:33.553Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:50:33.553Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:50:33.555Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:50:33.555Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:50:33.556Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:50:33.556Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:50:33.556Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:50:33.574Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:50:33.575Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:50:33.576Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:50:33.576Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:50:33.580Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:50:33.581Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:50:33.582Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:50:33.583Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:51:03.485Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:51:03.533Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:51:03.534Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:51:03.535Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:51:03.535Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:51:03.535Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:51:03.539Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:51:03.539Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:51:03.540Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:51:03.541Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:51:03.542Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:51:03.542Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:51:03.542Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:51:03.542Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:51:03.563Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:51:03.564Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:51:03.564Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:51:03.564Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:51:03.567Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:51:03.568Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:51:03.570Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:51:03.570Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:51:33.573Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:52:06.598Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:52:39.627Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:52:41.305Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:52:41.343Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:52:41.344Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:52:41.348Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:52:41.348Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:52:41.349Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:52:41.349Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:52:41.349Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:52:41.349Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:52:41.351Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:52:41.351Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:52:41.352Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:52:41.353Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:52:41.353Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:52:41.370Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:52:41.371Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:52:41.372Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:52:41.372Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:52:41.374Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:52:41.374Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:52:41.375Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:52:41.378Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:54:01.098Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:54:01.136Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:54:01.137Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:54:01.138Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:54:01.138Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:54:01.138Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:54:01.139Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:54:01.139Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:54:01.139Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:54:01.140Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:54:01.141Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:54:01.141Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:54:01.142Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:54:01.142Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:54:01.142Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:54:01.150Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:01.157Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:54:01.158Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:54:01.158Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:54:01.159Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:54:01.165Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:54:01.166Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:54:01.167Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:54:01.167Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:04.164Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:07.181Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:10.189Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:13.205Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:16.212Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:19.219Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:22.233Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:25.254Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:57:30.901Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:57:30.942Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:57:30.942Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:57:30.943Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:57:30.944Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:57:30.945Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:57:30.945Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:57:30.945Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:57:30.946Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:57:30.946Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:57:30.948Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:57:30.948Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:57:30.949Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:57:30.949Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:57:30.949Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:57:30.955Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:30.965Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:57:30.966Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:57:30.966Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:57:30.966Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:57:30.971Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:57:30.971Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:57:30.972Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:57:30.972Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:33.979Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:36.993Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:39.999Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:43.003Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:46.018Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:49.029Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:52.041Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:55.050Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:59:04.959Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:59:04.991Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:59:04.992Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:59:04.992Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:59:04.992Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:59:04.993Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:59:04.993Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:59:04.993Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:59:04.994Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:59:04.994Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:59:04.995Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:59:04.996Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:59:04.996Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:59:04.996Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:59:04.996Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:59:05.013Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:59:05.013Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:59:05.014Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:59:05.014Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:59:05.018Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:59:05.019Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:59:05.020Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:59:05.023Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:59:10.027Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:59:18.048Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:59:26.073Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:59:44.454Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:59:44.494Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:59:44.495Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:59:44.497Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:59:44.498Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:59:44.498Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:59:44.498Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:59:44.499Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:59:44.499Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:59:44.501Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:59:44.501Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:59:44.501Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:59:44.502Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:59:44.502Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:59:44.523Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:59:44.524Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:59:44.524Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:59:44.525Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:59:44.528Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:59:44.528Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:59:44.529Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:59:44.529Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:59:49.526Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:59:57.555Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:59:59.700Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:59:59.734Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:59:59.735Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:59:59.735Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:59:59.736Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:59:59.736Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:59:59.736Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:59:59.737Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:59:59.737Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:59:59.738Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:59:59.739Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:59:59.739Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:59:59.739Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:59:59.739Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:59:59.757Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:59:59.757Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:59:59.758Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:59:59.759Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:59:59.761Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:59:59.761Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:59:59.762Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:59:59.762Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:00:04.765Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:00:12.772Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:00:20.787Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:00:28.795Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:00:36.815Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:00:44.836Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:00:52.863Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:01:29.550Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:01:29.589Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:01:29.591Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:01:29.591Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:01:29.591Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:01:29.592Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:01:29.592Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:01:29.593Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:01:29.593Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:01:29.595Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:01:29.595Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:01:29.595Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:01:29.595Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:01:29.595Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:01:29.615Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:01:29.615Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:01:29.616Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:01:29.616Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T08:01:29.620Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T08:01:29.621Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:01:29.622Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:01:29.623Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:01:34.625Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:01:42.644Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:01:50.663Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:01:58.681Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:02:06.701Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:02:14.707Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:02:22.720Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:02:30.742Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:02:38.765Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:08:14.129Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:08:14.166Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:08:14.167Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:08:14.167Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:08:14.168Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:08:14.168Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:08:14.168Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:08:14.169Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:08:14.169Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:08:14.171Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:08:14.171Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:08:14.171Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:08:14.171Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:08:14.172Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:08:14.192Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:08:14.193Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:08:14.195Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:08:14.197Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T08:08:14.199Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T08:08:14.199Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:08:14.200Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:08:14.201Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:08:19.209Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:08:30.045Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:08:38.074Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:08:46.093Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:08:54.103Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:09:02.133Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:09:10.159Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:09:18.178Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:09:27.002Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:09:27.037Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:09:27.038Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:09:27.039Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:09:27.039Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:09:27.039Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:09:27.040Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:09:27.040Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:09:27.041Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:09:27.042Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:09:27.042Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:09:27.043Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:09:27.043Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:09:27.044Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:09:27.063Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:09:27.064Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:09:27.064Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:09:27.065Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T08:09:27.067Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T08:09:27.067Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:09:27.068Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:09:27.068Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:09:32.073Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:09:40.091Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:09:47.754Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:09:47.790Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:09:47.794Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:09:47.795Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:09:47.795Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:09:47.795Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:09:47.796Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:09:47.796Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:09:47.796Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:09:47.798Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:09:47.798Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:09:47.799Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:09:47.799Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:09:47.799Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:09:47.816Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:09:47.816Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:09:47.817Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:09:47.817Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T08:09:47.819Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T08:09:47.820Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:09:47.824Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:09:47.824Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:09:52.830Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:00.849Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:08.859Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:16.867Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:24.883Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:32.898Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:40.915Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:48.940Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:56.961Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:13:23.846Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:13:23.882Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:13:23.883Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:13:23.883Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:13:23.884Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:13:23.884Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:13:23.884Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:13:23.885Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:13:23.885Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:13:23.887Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:13:23.889Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:13:23.889Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:13:23.890Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:13:23.890Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:13:23.913Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:13:23.914Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:13:23.915Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:13:23.916Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T08:13:23.919Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T08:13:23.921Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:13:23.922Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:13:23.923Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:13:28.920Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:13:36.948Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:13:44.966Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:13:52.983Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:14:01.013Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:14:09.030Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:14:17.052Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:14:28.952Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:14:28.987Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:14:28.988Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:14:28.988Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:14:28.989Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:14:28.990Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:14:28.990Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:14:28.991Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:14:28.991Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:14:28.994Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:14:28.994Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:14:28.995Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:14:28.995Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:14:28.996Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:14:29.015Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:14:29.016Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:14:29.016Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:14:29.017Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T08:14:29.019Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T08:14:29.019Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:14:29.020Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:14:29.021Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:14:34.020Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:14:42.041Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:14:50.063Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:14:58.081Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:15:06.103Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:15:14.115Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:15:22.132Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:15:30.145Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:15:38.163Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:15:54.939Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:15:54.972Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:15:54.973Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:15:54.973Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:15:54.973Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:15:54.974Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:15:54.974Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:15:54.974Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:15:54.975Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:15:54.976Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:15:54.977Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:15:54.977Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:15:54.980Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:15:54.981Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:15:55.001Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:15:55.001Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:15:55.002Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:15:55.003Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T08:15:55.005Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T08:15:55.006Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:15:55.007Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:15:55.007Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:16:00.009Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:16:08.024Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:16:16.044Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:16:24.065Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:16:32.083Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:16:40.110Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:16:48.137Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:16:54.774Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:16:54.814Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:16:54.814Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:16:54.815Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:16:54.815Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:16:54.815Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:16:54.816Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:16:54.816Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:16:54.816Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:16:54.818Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:16:54.818Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:16:54.818Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:16:54.819Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:16:54.819Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:16:54.823Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:16:54.830Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:16:54.831Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:16:54.831Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:16:54.832Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:16:54.841Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:16:54.844Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:16:57.843Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:17:00.854Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:17:03.870Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:17:06.884Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:17:09.889Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:17:12.901Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:17:15.910Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:17:18.917Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:18:29.663Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:18:29.700Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:18:29.700Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:18:29.701Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:18:29.701Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:18:29.702Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:18:29.702Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:18:29.702Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:18:29.703Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:18:29.705Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:18:29.706Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:18:29.706Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:18:29.706Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:18:29.707Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:18:29.711Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:18:29.718Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:18:29.719Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:18:29.722Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:18:29.723Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:18:29.732Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:18:29.732Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:18:32.725Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:18:35.728Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:18:38.740Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:18:41.829Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:18:41.881Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:18:41.882Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:18:41.882Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:18:41.882Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:18:41.883Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:18:41.883Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:18:41.887Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:18:41.887Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:18:41.889Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:18:41.889Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:18:41.890Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:18:41.890Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:18:41.890Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:18:41.895Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:18:41.904Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:18:41.906Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:18:41.908Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:18:41.908Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:18:41.918Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:18:41.919Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:18:44.913Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:18:47.924Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:18:50.938Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:18:53.953Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:18:56.966Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:18:59.970Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:19:05.443Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:19:08.451Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:20:27.075Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:20:27.110Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:20:27.111Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:20:27.112Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:20:27.113Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:20:27.113Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:20:27.113Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:20:27.114Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:20:27.114Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:20:27.116Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:20:27.116Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:20:27.116Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:20:27.117Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:20:27.117Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:20:27.135Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:20:27.138Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:20:27.139Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:20:27.139Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:20:27.149Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:20:27.150Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongoServerError: Authentication failed.\n    at Connection.onMessage (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\connection.ts:413:18)\n    at MessageStream.<anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\connection.ts:243:56)\n    at MessageStream.emit (node:events:518:28)\n    at processIncomingData (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\message_stream.ts:193:12)\n    at MessageStream._write (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\message_stream.ts:74:5)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at Socket.ondata (node:internal/streams/readable:1009:22)\n    at Socket.emit (node:events:518:28)"],"timestamp":"2025-07-03T08:20:27.311Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongoServerError: Authentication failed.\n    at Connection.onMessage (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\connection.ts:413:18)\n    at MessageStream.<anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\connection.ts:243:56)\n    at MessageStream.emit (node:events:518:28)\n    at processIncomingData (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\message_stream.ts:193:12)\n    at MessageStream._write (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\message_stream.ts:74:5)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at Socket.ondata (node:internal/streams/readable:1009:22)\n    at Socket.emit (node:events:518:28)"],"timestamp":"2025-07-03T08:20:30.453Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongoServerError: Authentication failed.\n    at Connection.onMessage (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\connection.ts:413:18)\n    at MessageStream.<anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\connection.ts:243:56)\n    at MessageStream.emit (node:events:518:28)\n    at processIncomingData (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\message_stream.ts:193:12)\n    at MessageStream._write (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\message_stream.ts:74:5)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at Socket.ondata (node:internal/streams/readable:1009:22)\n    at Socket.emit (node:events:518:28)"],"timestamp":"2025-07-03T08:20:34.024Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongoServerError: Authentication failed.\n    at Connection.onMessage (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\connection.ts:413:18)\n    at MessageStream.<anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\connection.ts:243:56)\n    at MessageStream.emit (node:events:518:28)\n    at processIncomingData (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\message_stream.ts:193:12)\n    at MessageStream._write (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\message_stream.ts:74:5)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at Socket.ondata (node:internal/streams/readable:1009:22)\n    at Socket.emit (node:events:518:28)"],"timestamp":"2025-07-03T08:20:37.180Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongoServerError: Authentication failed.\n    at Connection.onMessage (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\connection.ts:413:18)\n    at MessageStream.<anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\connection.ts:243:56)\n    at MessageStream.emit (node:events:518:28)\n    at processIncomingData (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\message_stream.ts:193:12)\n    at MessageStream._write (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\message_stream.ts:74:5)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at Socket.ondata (node:internal/streams/readable:1009:22)\n    at Socket.emit (node:events:518:28)"],"timestamp":"2025-07-03T08:20:40.325Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongoServerError: Authentication failed.\n    at Connection.onMessage (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\connection.ts:413:18)\n    at MessageStream.<anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\connection.ts:243:56)\n    at MessageStream.emit (node:events:518:28)\n    at processIncomingData (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\message_stream.ts:193:12)\n    at MessageStream._write (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\message_stream.ts:74:5)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at Socket.ondata (node:internal/streams/readable:1009:22)\n    at Socket.emit (node:events:518:28)"],"timestamp":"2025-07-03T08:20:43.465Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongoServerError: Authentication failed.\n    at Connection.onMessage (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\connection.ts:413:18)\n    at MessageStream.<anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\connection.ts:243:56)\n    at MessageStream.emit (node:events:518:28)\n    at processIncomingData (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\message_stream.ts:193:12)\n    at MessageStream._write (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\message_stream.ts:74:5)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at Socket.ondata (node:internal/streams/readable:1009:22)\n    at Socket.emit (node:events:518:28)"],"timestamp":"2025-07-03T08:20:46.606Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongoServerError: Authentication failed.\n    at Connection.onMessage (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\connection.ts:413:18)\n    at MessageStream.<anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\connection.ts:243:56)\n    at MessageStream.emit (node:events:518:28)\n    at processIncomingData (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\message_stream.ts:193:12)\n    at MessageStream._write (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\message_stream.ts:74:5)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at Socket.ondata (node:internal/streams/readable:1009:22)\n    at Socket.emit (node:events:518:28)"],"timestamp":"2025-07-03T08:20:49.748Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongoServerError: Authentication failed.\n    at Connection.onMessage (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\connection.ts:413:18)\n    at MessageStream.<anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\connection.ts:243:56)\n    at MessageStream.emit (node:events:518:28)\n    at processIncomingData (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\message_stream.ts:193:12)\n    at MessageStream._write (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\cmap\\message_stream.ts:74:5)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at Socket.ondata (node:internal/streams/readable:1009:22)\n    at Socket.emit (node:events:518:28)"],"timestamp":"2025-07-03T08:20:52.878Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:21:34.618Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:21:34.654Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:21:34.654Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:21:34.655Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:21:34.656Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:21:34.656Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:21:34.657Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:21:34.657Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:21:34.657Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:21:34.659Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:21:34.659Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:21:34.660Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:21:34.660Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:21:34.661Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:21:34.679Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:21:34.680Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:21:34.682Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:21:34.683Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:21:34.693Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:21:34.694Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseCoreModule dependencies initialized","timestamp":"2025-07-03T08:21:34.827Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:22:03.791Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:22:03.830Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:03.831Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:22:03.832Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:22:03.832Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:22:03.833Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:22:03.833Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:22:03.834Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:22:03.838Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:22:03.840Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:22:03.841Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:22:03.841Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:22:03.842Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:22:03.842Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:22:03.863Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:22:03.864Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:22:03.866Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:22:03.869Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:22:03.880Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:22:03.881Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseCoreModule dependencies initialized","timestamp":"2025-07-03T08:22:04.028Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:04.044Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:04.045Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:04.045Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:04.046Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:04.046Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:04.047Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:04.047Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:04.047Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:04.048Z"}
{"context":"InstanceLoader","level":"info","message":"UsersModule dependencies initialized","timestamp":"2025-07-03T08:22:04.053Z"}
{"context":"InstanceLoader","level":"info","message":"ProjectsModule dependencies initialized","timestamp":"2025-07-03T08:22:04.054Z"}
{"context":"InstanceLoader","level":"info","message":"ServiceProvidersModule dependencies initialized","timestamp":"2025-07-03T08:22:04.054Z"}
{"context":"InstanceLoader","level":"info","message":"QuotesModule dependencies initialized","timestamp":"2025-07-03T08:22:04.055Z"}
{"context":"InstanceLoader","level":"info","message":"FilesModule dependencies initialized","timestamp":"2025-07-03T08:22:04.055Z"}
{"context":"InstanceLoader","level":"info","message":"NotificationsModule dependencies initialized","timestamp":"2025-07-03T08:22:04.055Z"}
{"context":"InstanceLoader","level":"info","message":"ReviewsModule dependencies initialized","timestamp":"2025-07-03T08:22:04.055Z"}
{"context":"InstanceLoader","level":"info","message":"PaymentsModule dependencies initialized","timestamp":"2025-07-03T08:22:04.056Z"}
{"context":"InstanceLoader","level":"info","message":"TasksModule dependencies initialized","timestamp":"2025-07-03T08:22:04.056Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-07-03T08:22:04.056Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-07-03T08:22:04.117Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/register, POST} route","timestamp":"2025-07-03T08:22:04.119Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/login, POST} route","timestamp":"2025-07-03T08:22:04.119Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-07-03T08:22:04.120Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/forgot-password, POST} route","timestamp":"2025-07-03T08:22:04.120Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/reset-password, POST} route","timestamp":"2025-07-03T08:22:04.121Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-email, POST} route","timestamp":"2025-07-03T08:22:04.121Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/resend-verification, POST} route","timestamp":"2025-07-03T08:22:04.121Z"}
{"context":"RoutesResolver","level":"info","message":"UsersController {/api/v1/users}:","timestamp":"2025-07-03T08:22:04.122Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route","timestamp":"2025-07-03T08:22:04.122Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route","timestamp":"2025-07-03T08:22:04.122Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/me, GET} route","timestamp":"2025-07-03T08:22:04.123Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/stats, GET} route","timestamp":"2025-07-03T08:22:04.123Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/nearby, GET} route","timestamp":"2025-07-03T08:22:04.123Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, GET} route","timestamp":"2025-07-03T08:22:04.123Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/me, PATCH} route","timestamp":"2025-07-03T08:22:04.124Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route","timestamp":"2025-07-03T08:22:04.124Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/me/change-password, POST} route","timestamp":"2025-07-03T08:22:04.124Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id/verify-email, POST} route","timestamp":"2025-07-03T08:22:04.124Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id/verify-phone, POST} route","timestamp":"2025-07-03T08:22:04.125Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/me, DELETE} route","timestamp":"2025-07-03T08:22:04.125Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route","timestamp":"2025-07-03T08:22:04.125Z"}
{"context":"RoutesResolver","level":"info","message":"ProjectsController {/api/v1/projects}:","timestamp":"2025-07-03T08:22:04.125Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects, POST} route","timestamp":"2025-07-03T08:22:04.125Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects, GET} route","timestamp":"2025-07-03T08:22:04.126Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/me, GET} route","timestamp":"2025-07-03T08:22:04.126Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/stats, GET} route","timestamp":"2025-07-03T08:22:04.128Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/me/stats, GET} route","timestamp":"2025-07-03T08:22:04.128Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/nearby, GET} route","timestamp":"2025-07-03T08:22:04.128Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/:id, GET} route","timestamp":"2025-07-03T08:22:04.129Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/:id, PATCH} route","timestamp":"2025-07-03T08:22:04.129Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/:id, DELETE} route","timestamp":"2025-07-03T08:22:04.129Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/:id/publish, POST} route","timestamp":"2025-07-03T08:22:04.130Z"}
{"context":"RoutesResolver","level":"info","message":"ServiceProvidersController {/api/v1/service-providers}:","timestamp":"2025-07-03T08:22:04.130Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers, POST} route","timestamp":"2025-07-03T08:22:04.130Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers, GET} route","timestamp":"2025-07-03T08:22:04.130Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/me, GET} route","timestamp":"2025-07-03T08:22:04.131Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/stats, GET} route","timestamp":"2025-07-03T08:22:04.131Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/:id, GET} route","timestamp":"2025-07-03T08:22:04.131Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/:id, PATCH} route","timestamp":"2025-07-03T08:22:04.131Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/:id, DELETE} route","timestamp":"2025-07-03T08:22:04.132Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/:id/review, POST} route","timestamp":"2025-07-03T08:22:04.132Z"}
{"context":"RoutesResolver","level":"info","message":"QuotesController {/api/v1/quotes}:","timestamp":"2025-07-03T08:22:04.132Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes, POST} route","timestamp":"2025-07-03T08:22:04.132Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes, GET} route","timestamp":"2025-07-03T08:22:04.132Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/project/:projectId, GET} route","timestamp":"2025-07-03T08:22:04.133Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/provider/:providerId, GET} route","timestamp":"2025-07-03T08:22:04.133Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/customer/:customerId, GET} route","timestamp":"2025-07-03T08:22:04.133Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id, GET} route","timestamp":"2025-07-03T08:22:04.133Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id, PATCH} route","timestamp":"2025-07-03T08:22:04.134Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id, DELETE} route","timestamp":"2025-07-03T08:22:04.134Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id/submit, POST} route","timestamp":"2025-07-03T08:22:04.134Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id/accept, POST} route","timestamp":"2025-07-03T08:22:04.134Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id/decline, POST} route","timestamp":"2025-07-03T08:22:04.134Z"}
{"context":"RoutesResolver","level":"info","message":"FilesController {/api/v1/files}:","timestamp":"2025-07-03T08:22:04.135Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files/upload, POST} route","timestamp":"2025-07-03T08:22:04.135Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files, GET} route","timestamp":"2025-07-03T08:22:04.135Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files/stats, GET} route","timestamp":"2025-07-03T08:22:04.135Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files/:id, GET} route","timestamp":"2025-07-03T08:22:04.136Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files/:id/download, GET} route","timestamp":"2025-07-03T08:22:04.136Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files/:id, DELETE} route","timestamp":"2025-07-03T08:22:04.136Z"}
{"context":"RoutesResolver","level":"info","message":"NotificationsController {/api/v1/notifications}:","timestamp":"2025-07-03T08:22:04.137Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications, POST} route","timestamp":"2025-07-03T08:22:04.137Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/bulk, POST} route","timestamp":"2025-07-03T08:22:04.137Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/me, GET} route","timestamp":"2025-07-03T08:22:04.138Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/me/unread-count, GET} route","timestamp":"2025-07-03T08:22:04.138Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/me/stats, GET} route","timestamp":"2025-07-03T08:22:04.138Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/:id, GET} route","timestamp":"2025-07-03T08:22:04.139Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/:id/read, PATCH} route","timestamp":"2025-07-03T08:22:04.139Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/me/read-all, PATCH} route","timestamp":"2025-07-03T08:22:04.139Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/:id, DELETE} route","timestamp":"2025-07-03T08:22:04.140Z"}
{"context":"RoutesResolver","level":"info","message":"ReviewsController {/api/v1/reviews}:","timestamp":"2025-07-03T08:22:04.140Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews, POST} route","timestamp":"2025-07-03T08:22:04.140Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews, GET} route","timestamp":"2025-07-03T08:22:04.140Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/stats/:userId, GET} route","timestamp":"2025-07-03T08:22:04.140Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id, GET} route","timestamp":"2025-07-03T08:22:04.141Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id, PATCH} route","timestamp":"2025-07-03T08:22:04.141Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id, DELETE} route","timestamp":"2025-07-03T08:22:04.141Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id/response, POST} route","timestamp":"2025-07-03T08:22:04.141Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id/like, POST} route","timestamp":"2025-07-03T08:22:04.142Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id/helpful, POST} route","timestamp":"2025-07-03T08:22:04.142Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id/moderate, PATCH} route","timestamp":"2025-07-03T08:22:04.142Z"}
{"context":"RoutesResolver","level":"info","message":"PaymentsController {/api/v1/payments}:","timestamp":"2025-07-03T08:22:04.144Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments, POST} route","timestamp":"2025-07-03T08:22:04.144Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments, GET} route","timestamp":"2025-07-03T08:22:04.144Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/stats/:userId, GET} route","timestamp":"2025-07-03T08:22:04.144Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id, GET} route","timestamp":"2025-07-03T08:22:04.145Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/process, POST} route","timestamp":"2025-07-03T08:22:04.145Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/confirm, POST} route","timestamp":"2025-07-03T08:22:04.145Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/refund, POST} route","timestamp":"2025-07-03T08:22:04.145Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/release-escrow, POST} route","timestamp":"2025-07-03T08:22:04.146Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/cancel, POST} route","timestamp":"2025-07-03T08:22:04.146Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/status, PATCH} route","timestamp":"2025-07-03T08:22:04.146Z"}
{"context":"RoutesResolver","level":"info","message":"TasksController {/api/v1/tasks}:","timestamp":"2025-07-03T08:22:04.147Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks, POST} route","timestamp":"2025-07-03T08:22:04.147Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks, GET} route","timestamp":"2025-07-03T08:22:04.147Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/stats/:userId, GET} route","timestamp":"2025-07-03T08:22:04.147Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/project-stats/:projectId, GET} route","timestamp":"2025-07-03T08:22:04.148Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id, GET} route","timestamp":"2025-07-03T08:22:04.148Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id, PATCH} route","timestamp":"2025-07-03T08:22:04.148Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id, DELETE} route","timestamp":"2025-07-03T08:22:04.148Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id/comments, POST} route","timestamp":"2025-07-03T08:22:04.148Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id/time-logs, POST} route","timestamp":"2025-07-03T08:22:04.149Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id/status, PATCH} route","timestamp":"2025-07-03T08:22:04.149Z"}
{"context":"RoutesResolver","level":"info","message":"I18nController {/api/v1/i18n}:","timestamp":"2025-07-03T08:22:04.149Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/i18n/languages, GET} route","timestamp":"2025-07-03T08:22:04.149Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/i18n/translate, GET} route","timestamp":"2025-07-03T08:22:04.150Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/i18n/demo, GET} route","timestamp":"2025-07-03T08:22:04.150Z"}
{"context":"RoutesResolver","level":"info","message":"HealthController {/api/v1/health}:","timestamp":"2025-07-03T08:22:04.150Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health, GET} route","timestamp":"2025-07-03T08:22:04.150Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health/database, GET} route","timestamp":"2025-07-03T08:22:04.151Z"}
{"context":"I18nService","level":"info","message":"Checking translation changes","timestamp":"2025-07-03T08:22:04.347Z"}
{"code":"ENOENT","context":"I18nService","errno":-4058,"error":{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\generated\\i18n.generated.ts","syscall":"open"},"level":"error","message":"ENOENT: no such file or directory, open 'D:\\Tommi\\Desktop\\test\\generated\\i18n.generated.ts'","path":"D:\\Tommi\\Desktop\\test\\generated\\i18n.generated.ts","stack":["Error: ENOENT: no such file or directory, open 'D:\\Tommi\\Desktop\\test\\generated\\i18n.generated.ts'\n    at Object.readFileSync (node:fs:442:20)\n    at Object.next (D:\\Tommi\\Desktop\\test\\node_modules\\nestjs-i18n\\src\\i18n.module.ts:121:39)"],"syscall":"open","timestamp":"2025-07-03T08:22:04.357Z"}
{"context":"I18nService","level":"info","message":"Types generated in: D:\\Tommi\\Desktop\\test\\generated\\i18n.generated.ts.\n                Please also add it to ignore files of your linter and formatter to avoid linting and formatting it\n                ","timestamp":"2025-07-03T08:22:04.358Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-07-03T08:22:04.360Z"}
{"level":"info","message":"Application is running on: http://localhost:3000","timestamp":"2025-07-03T08:22:04.362Z"}
{"level":"info","message":"Swagger documentation: http://localhost:3000/api/docs","timestamp":"2025-07-03T08:22:04.363Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:22:23.816Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:22:23.863Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:23.863Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:22:23.864Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:22:23.865Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:22:23.866Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:22:23.866Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:22:23.867Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:22:23.871Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:22:23.872Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:22:23.873Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:22:23.874Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:22:23.874Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:22:23.875Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:22:23.893Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:22:23.894Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:22:23.894Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:22:23.895Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:22:23.905Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:22:23.907Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseCoreModule dependencies initialized","timestamp":"2025-07-03T08:22:24.062Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:24.078Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:24.078Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:24.079Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:24.080Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:24.081Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:24.082Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:24.082Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:24.083Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:24.085Z"}
{"context":"InstanceLoader","level":"info","message":"UsersModule dependencies initialized","timestamp":"2025-07-03T08:22:24.091Z"}
{"context":"InstanceLoader","level":"info","message":"ProjectsModule dependencies initialized","timestamp":"2025-07-03T08:22:24.091Z"}
{"context":"InstanceLoader","level":"info","message":"ServiceProvidersModule dependencies initialized","timestamp":"2025-07-03T08:22:24.092Z"}
{"context":"InstanceLoader","level":"info","message":"QuotesModule dependencies initialized","timestamp":"2025-07-03T08:22:24.092Z"}
{"context":"InstanceLoader","level":"info","message":"FilesModule dependencies initialized","timestamp":"2025-07-03T08:22:24.093Z"}
{"context":"InstanceLoader","level":"info","message":"NotificationsModule dependencies initialized","timestamp":"2025-07-03T08:22:24.093Z"}
{"context":"InstanceLoader","level":"info","message":"ReviewsModule dependencies initialized","timestamp":"2025-07-03T08:22:24.094Z"}
{"context":"InstanceLoader","level":"info","message":"PaymentsModule dependencies initialized","timestamp":"2025-07-03T08:22:24.095Z"}
{"context":"InstanceLoader","level":"info","message":"TasksModule dependencies initialized","timestamp":"2025-07-03T08:22:24.095Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-07-03T08:22:24.095Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-07-03T08:22:24.173Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/register, POST} route","timestamp":"2025-07-03T08:22:24.176Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/login, POST} route","timestamp":"2025-07-03T08:22:24.180Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-07-03T08:22:24.183Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/forgot-password, POST} route","timestamp":"2025-07-03T08:22:24.184Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/reset-password, POST} route","timestamp":"2025-07-03T08:22:24.185Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-email, POST} route","timestamp":"2025-07-03T08:22:24.186Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/resend-verification, POST} route","timestamp":"2025-07-03T08:22:24.186Z"}
{"context":"RoutesResolver","level":"info","message":"UsersController {/api/v1/users}:","timestamp":"2025-07-03T08:22:24.187Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route","timestamp":"2025-07-03T08:22:24.188Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route","timestamp":"2025-07-03T08:22:24.188Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/me, GET} route","timestamp":"2025-07-03T08:22:24.189Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/stats, GET} route","timestamp":"2025-07-03T08:22:24.192Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/nearby, GET} route","timestamp":"2025-07-03T08:22:24.194Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, GET} route","timestamp":"2025-07-03T08:22:24.195Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/me, PATCH} route","timestamp":"2025-07-03T08:22:24.196Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route","timestamp":"2025-07-03T08:22:24.196Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/me/change-password, POST} route","timestamp":"2025-07-03T08:22:24.198Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id/verify-email, POST} route","timestamp":"2025-07-03T08:22:24.199Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id/verify-phone, POST} route","timestamp":"2025-07-03T08:22:24.199Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/me, DELETE} route","timestamp":"2025-07-03T08:22:24.200Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route","timestamp":"2025-07-03T08:22:24.201Z"}
{"context":"RoutesResolver","level":"info","message":"ProjectsController {/api/v1/projects}:","timestamp":"2025-07-03T08:22:24.202Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects, POST} route","timestamp":"2025-07-03T08:22:24.202Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects, GET} route","timestamp":"2025-07-03T08:22:24.203Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/me, GET} route","timestamp":"2025-07-03T08:22:24.203Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/stats, GET} route","timestamp":"2025-07-03T08:22:24.204Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/me/stats, GET} route","timestamp":"2025-07-03T08:22:24.204Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/nearby, GET} route","timestamp":"2025-07-03T08:22:24.205Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/:id, GET} route","timestamp":"2025-07-03T08:22:24.206Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/:id, PATCH} route","timestamp":"2025-07-03T08:22:24.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/:id, DELETE} route","timestamp":"2025-07-03T08:22:24.211Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/:id/publish, POST} route","timestamp":"2025-07-03T08:22:24.211Z"}
{"context":"RoutesResolver","level":"info","message":"ServiceProvidersController {/api/v1/service-providers}:","timestamp":"2025-07-03T08:22:24.212Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers, POST} route","timestamp":"2025-07-03T08:22:24.212Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers, GET} route","timestamp":"2025-07-03T08:22:24.212Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/me, GET} route","timestamp":"2025-07-03T08:22:24.213Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/stats, GET} route","timestamp":"2025-07-03T08:22:24.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/:id, GET} route","timestamp":"2025-07-03T08:22:24.215Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/:id, PATCH} route","timestamp":"2025-07-03T08:22:24.216Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/:id, DELETE} route","timestamp":"2025-07-03T08:22:24.217Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/:id/review, POST} route","timestamp":"2025-07-03T08:22:24.217Z"}
{"context":"RoutesResolver","level":"info","message":"QuotesController {/api/v1/quotes}:","timestamp":"2025-07-03T08:22:24.218Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes, POST} route","timestamp":"2025-07-03T08:22:24.219Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes, GET} route","timestamp":"2025-07-03T08:22:24.219Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/project/:projectId, GET} route","timestamp":"2025-07-03T08:22:24.220Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/provider/:providerId, GET} route","timestamp":"2025-07-03T08:22:24.221Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/customer/:customerId, GET} route","timestamp":"2025-07-03T08:22:24.221Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id, GET} route","timestamp":"2025-07-03T08:22:24.226Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id, PATCH} route","timestamp":"2025-07-03T08:22:24.228Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id, DELETE} route","timestamp":"2025-07-03T08:22:24.229Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id/submit, POST} route","timestamp":"2025-07-03T08:22:24.229Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id/accept, POST} route","timestamp":"2025-07-03T08:22:24.230Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id/decline, POST} route","timestamp":"2025-07-03T08:22:24.231Z"}
{"context":"RoutesResolver","level":"info","message":"FilesController {/api/v1/files}:","timestamp":"2025-07-03T08:22:24.231Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files/upload, POST} route","timestamp":"2025-07-03T08:22:24.232Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files, GET} route","timestamp":"2025-07-03T08:22:24.233Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files/stats, GET} route","timestamp":"2025-07-03T08:22:24.234Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files/:id, GET} route","timestamp":"2025-07-03T08:22:24.235Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files/:id/download, GET} route","timestamp":"2025-07-03T08:22:24.236Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files/:id, DELETE} route","timestamp":"2025-07-03T08:22:24.238Z"}
{"context":"RoutesResolver","level":"info","message":"NotificationsController {/api/v1/notifications}:","timestamp":"2025-07-03T08:22:24.241Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications, POST} route","timestamp":"2025-07-03T08:22:24.241Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/bulk, POST} route","timestamp":"2025-07-03T08:22:24.242Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/me, GET} route","timestamp":"2025-07-03T08:22:24.243Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/me/unread-count, GET} route","timestamp":"2025-07-03T08:22:24.244Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/me/stats, GET} route","timestamp":"2025-07-03T08:22:24.244Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/:id, GET} route","timestamp":"2025-07-03T08:22:24.245Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/:id/read, PATCH} route","timestamp":"2025-07-03T08:22:24.245Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/me/read-all, PATCH} route","timestamp":"2025-07-03T08:22:24.246Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/:id, DELETE} route","timestamp":"2025-07-03T08:22:24.246Z"}
{"context":"RoutesResolver","level":"info","message":"ReviewsController {/api/v1/reviews}:","timestamp":"2025-07-03T08:22:24.248Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews, POST} route","timestamp":"2025-07-03T08:22:24.250Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews, GET} route","timestamp":"2025-07-03T08:22:24.251Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/stats/:userId, GET} route","timestamp":"2025-07-03T08:22:24.251Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id, GET} route","timestamp":"2025-07-03T08:22:24.252Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id, PATCH} route","timestamp":"2025-07-03T08:22:24.256Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id, DELETE} route","timestamp":"2025-07-03T08:22:24.259Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id/response, POST} route","timestamp":"2025-07-03T08:22:24.261Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id/like, POST} route","timestamp":"2025-07-03T08:22:24.261Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id/helpful, POST} route","timestamp":"2025-07-03T08:22:24.262Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id/moderate, PATCH} route","timestamp":"2025-07-03T08:22:24.262Z"}
{"context":"RoutesResolver","level":"info","message":"PaymentsController {/api/v1/payments}:","timestamp":"2025-07-03T08:22:24.263Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments, POST} route","timestamp":"2025-07-03T08:22:24.263Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments, GET} route","timestamp":"2025-07-03T08:22:24.265Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/stats/:userId, GET} route","timestamp":"2025-07-03T08:22:24.266Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id, GET} route","timestamp":"2025-07-03T08:22:24.268Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/process, POST} route","timestamp":"2025-07-03T08:22:24.270Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/confirm, POST} route","timestamp":"2025-07-03T08:22:24.270Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/refund, POST} route","timestamp":"2025-07-03T08:22:24.270Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/release-escrow, POST} route","timestamp":"2025-07-03T08:22:24.270Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/cancel, POST} route","timestamp":"2025-07-03T08:22:24.271Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/status, PATCH} route","timestamp":"2025-07-03T08:22:24.271Z"}
{"context":"RoutesResolver","level":"info","message":"TasksController {/api/v1/tasks}:","timestamp":"2025-07-03T08:22:24.271Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks, POST} route","timestamp":"2025-07-03T08:22:24.271Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks, GET} route","timestamp":"2025-07-03T08:22:24.272Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/stats/:userId, GET} route","timestamp":"2025-07-03T08:22:24.273Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/project-stats/:projectId, GET} route","timestamp":"2025-07-03T08:22:24.274Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id, GET} route","timestamp":"2025-07-03T08:22:24.274Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id, PATCH} route","timestamp":"2025-07-03T08:22:24.274Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id, DELETE} route","timestamp":"2025-07-03T08:22:24.275Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id/comments, POST} route","timestamp":"2025-07-03T08:22:24.275Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id/time-logs, POST} route","timestamp":"2025-07-03T08:22:24.276Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id/status, PATCH} route","timestamp":"2025-07-03T08:22:24.277Z"}
{"context":"RoutesResolver","level":"info","message":"I18nController {/api/v1/i18n}:","timestamp":"2025-07-03T08:22:24.277Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/i18n/languages, GET} route","timestamp":"2025-07-03T08:22:24.277Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/i18n/translate, GET} route","timestamp":"2025-07-03T08:22:24.277Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/i18n/demo, GET} route","timestamp":"2025-07-03T08:22:24.278Z"}
{"context":"RoutesResolver","level":"info","message":"HealthController {/api/v1/health}:","timestamp":"2025-07-03T08:22:24.278Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health, GET} route","timestamp":"2025-07-03T08:22:24.278Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health/database, GET} route","timestamp":"2025-07-03T08:22:24.278Z"}
{"context":"I18nService","level":"info","message":"Checking translation changes","timestamp":"2025-07-03T08:22:24.489Z"}
{"context":"I18nService","level":"info","message":"No changes detected","timestamp":"2025-07-03T08:22:24.501Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-07-03T08:22:24.507Z"}
{"level":"info","message":"Application is running on: http://localhost:3000","timestamp":"2025-07-03T08:22:24.510Z"}
{"level":"info","message":"Swagger documentation: http://localhost:3000/api/docs","timestamp":"2025-07-03T08:22:24.511Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:22:45.219Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:22:45.261Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:45.262Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:22:45.262Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:22:45.262Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:22:45.263Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:22:45.265Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:22:45.265Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:22:45.266Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:22:45.268Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:22:45.270Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:22:45.270Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:22:45.271Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:22:45.271Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:22:45.290Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:22:45.291Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:22:45.292Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:22:45.292Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:22:45.303Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:22:45.304Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseCoreModule dependencies initialized","timestamp":"2025-07-03T08:22:45.446Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:45.462Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:45.463Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:45.467Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:45.467Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:45.467Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:45.467Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:45.468Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:45.468Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:45.468Z"}
{"context":"InstanceLoader","level":"info","message":"UsersModule dependencies initialized","timestamp":"2025-07-03T08:22:45.474Z"}
{"context":"InstanceLoader","level":"info","message":"ProjectsModule dependencies initialized","timestamp":"2025-07-03T08:22:45.474Z"}
{"context":"InstanceLoader","level":"info","message":"ServiceProvidersModule dependencies initialized","timestamp":"2025-07-03T08:22:45.475Z"}
{"context":"InstanceLoader","level":"info","message":"QuotesModule dependencies initialized","timestamp":"2025-07-03T08:22:45.476Z"}
{"context":"InstanceLoader","level":"info","message":"FilesModule dependencies initialized","timestamp":"2025-07-03T08:22:45.476Z"}
{"context":"InstanceLoader","level":"info","message":"NotificationsModule dependencies initialized","timestamp":"2025-07-03T08:22:45.477Z"}
{"context":"InstanceLoader","level":"info","message":"ReviewsModule dependencies initialized","timestamp":"2025-07-03T08:22:45.477Z"}
{"context":"InstanceLoader","level":"info","message":"PaymentsModule dependencies initialized","timestamp":"2025-07-03T08:22:45.477Z"}
{"context":"InstanceLoader","level":"info","message":"TasksModule dependencies initialized","timestamp":"2025-07-03T08:22:45.477Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-07-03T08:22:45.477Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-07-03T08:22:45.548Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/register, POST} route","timestamp":"2025-07-03T08:22:45.552Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/login, POST} route","timestamp":"2025-07-03T08:22:45.552Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-07-03T08:22:45.553Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/forgot-password, POST} route","timestamp":"2025-07-03T08:22:45.553Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/reset-password, POST} route","timestamp":"2025-07-03T08:22:45.554Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-email, POST} route","timestamp":"2025-07-03T08:22:45.554Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/resend-verification, POST} route","timestamp":"2025-07-03T08:22:45.554Z"}
{"context":"RoutesResolver","level":"info","message":"UsersController {/api/v1/users}:","timestamp":"2025-07-03T08:22:45.555Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route","timestamp":"2025-07-03T08:22:45.555Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route","timestamp":"2025-07-03T08:22:45.556Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/me, GET} route","timestamp":"2025-07-03T08:22:45.557Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/stats, GET} route","timestamp":"2025-07-03T08:22:45.557Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/nearby, GET} route","timestamp":"2025-07-03T08:22:45.560Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, GET} route","timestamp":"2025-07-03T08:22:45.560Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/me, PATCH} route","timestamp":"2025-07-03T08:22:45.560Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route","timestamp":"2025-07-03T08:22:45.561Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/me/change-password, POST} route","timestamp":"2025-07-03T08:22:45.562Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id/verify-email, POST} route","timestamp":"2025-07-03T08:22:45.562Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id/verify-phone, POST} route","timestamp":"2025-07-03T08:22:45.562Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/me, DELETE} route","timestamp":"2025-07-03T08:22:45.563Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route","timestamp":"2025-07-03T08:22:45.563Z"}
{"context":"RoutesResolver","level":"info","message":"ProjectsController {/api/v1/projects}:","timestamp":"2025-07-03T08:22:45.564Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects, POST} route","timestamp":"2025-07-03T08:22:45.564Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects, GET} route","timestamp":"2025-07-03T08:22:45.564Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/me, GET} route","timestamp":"2025-07-03T08:22:45.564Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/stats, GET} route","timestamp":"2025-07-03T08:22:45.565Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/me/stats, GET} route","timestamp":"2025-07-03T08:22:45.565Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/nearby, GET} route","timestamp":"2025-07-03T08:22:45.565Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/:id, GET} route","timestamp":"2025-07-03T08:22:45.566Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/:id, PATCH} route","timestamp":"2025-07-03T08:22:45.567Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/:id, DELETE} route","timestamp":"2025-07-03T08:22:45.567Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/:id/publish, POST} route","timestamp":"2025-07-03T08:22:45.567Z"}
{"context":"RoutesResolver","level":"info","message":"ServiceProvidersController {/api/v1/service-providers}:","timestamp":"2025-07-03T08:22:45.568Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers, POST} route","timestamp":"2025-07-03T08:22:45.568Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers, GET} route","timestamp":"2025-07-03T08:22:45.569Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/me, GET} route","timestamp":"2025-07-03T08:22:45.569Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/stats, GET} route","timestamp":"2025-07-03T08:22:45.569Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/:id, GET} route","timestamp":"2025-07-03T08:22:45.570Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/:id, PATCH} route","timestamp":"2025-07-03T08:22:45.570Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/:id, DELETE} route","timestamp":"2025-07-03T08:22:45.570Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/:id/review, POST} route","timestamp":"2025-07-03T08:22:45.571Z"}
{"context":"RoutesResolver","level":"info","message":"QuotesController {/api/v1/quotes}:","timestamp":"2025-07-03T08:22:45.571Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes, POST} route","timestamp":"2025-07-03T08:22:45.573Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes, GET} route","timestamp":"2025-07-03T08:22:45.573Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/project/:projectId, GET} route","timestamp":"2025-07-03T08:22:45.577Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/provider/:providerId, GET} route","timestamp":"2025-07-03T08:22:45.578Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/customer/:customerId, GET} route","timestamp":"2025-07-03T08:22:45.579Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id, GET} route","timestamp":"2025-07-03T08:22:45.579Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id, PATCH} route","timestamp":"2025-07-03T08:22:45.581Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id, DELETE} route","timestamp":"2025-07-03T08:22:45.582Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id/submit, POST} route","timestamp":"2025-07-03T08:22:45.582Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id/accept, POST} route","timestamp":"2025-07-03T08:22:45.583Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id/decline, POST} route","timestamp":"2025-07-03T08:22:45.583Z"}
{"context":"RoutesResolver","level":"info","message":"FilesController {/api/v1/files}:","timestamp":"2025-07-03T08:22:45.583Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files/upload, POST} route","timestamp":"2025-07-03T08:22:45.584Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files, GET} route","timestamp":"2025-07-03T08:22:45.584Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files/stats, GET} route","timestamp":"2025-07-03T08:22:45.585Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files/:id, GET} route","timestamp":"2025-07-03T08:22:45.585Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files/:id/download, GET} route","timestamp":"2025-07-03T08:22:45.586Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files/:id, DELETE} route","timestamp":"2025-07-03T08:22:45.586Z"}
{"context":"RoutesResolver","level":"info","message":"NotificationsController {/api/v1/notifications}:","timestamp":"2025-07-03T08:22:45.586Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications, POST} route","timestamp":"2025-07-03T08:22:45.586Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/bulk, POST} route","timestamp":"2025-07-03T08:22:45.587Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/me, GET} route","timestamp":"2025-07-03T08:22:45.587Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/me/unread-count, GET} route","timestamp":"2025-07-03T08:22:45.588Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/me/stats, GET} route","timestamp":"2025-07-03T08:22:45.588Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/:id, GET} route","timestamp":"2025-07-03T08:22:45.588Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/:id/read, PATCH} route","timestamp":"2025-07-03T08:22:45.588Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/me/read-all, PATCH} route","timestamp":"2025-07-03T08:22:45.589Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/:id, DELETE} route","timestamp":"2025-07-03T08:22:45.590Z"}
{"context":"RoutesResolver","level":"info","message":"ReviewsController {/api/v1/reviews}:","timestamp":"2025-07-03T08:22:45.591Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews, POST} route","timestamp":"2025-07-03T08:22:45.591Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews, GET} route","timestamp":"2025-07-03T08:22:45.592Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/stats/:userId, GET} route","timestamp":"2025-07-03T08:22:45.592Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id, GET} route","timestamp":"2025-07-03T08:22:45.592Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id, PATCH} route","timestamp":"2025-07-03T08:22:45.593Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id, DELETE} route","timestamp":"2025-07-03T08:22:45.593Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id/response, POST} route","timestamp":"2025-07-03T08:22:45.593Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id/like, POST} route","timestamp":"2025-07-03T08:22:45.593Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id/helpful, POST} route","timestamp":"2025-07-03T08:22:45.593Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id/moderate, PATCH} route","timestamp":"2025-07-03T08:22:45.594Z"}
{"context":"RoutesResolver","level":"info","message":"PaymentsController {/api/v1/payments}:","timestamp":"2025-07-03T08:22:45.594Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments, POST} route","timestamp":"2025-07-03T08:22:45.594Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments, GET} route","timestamp":"2025-07-03T08:22:45.594Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/stats/:userId, GET} route","timestamp":"2025-07-03T08:22:45.595Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id, GET} route","timestamp":"2025-07-03T08:22:45.595Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/process, POST} route","timestamp":"2025-07-03T08:22:45.595Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/confirm, POST} route","timestamp":"2025-07-03T08:22:45.595Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/refund, POST} route","timestamp":"2025-07-03T08:22:45.595Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/release-escrow, POST} route","timestamp":"2025-07-03T08:22:45.596Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/cancel, POST} route","timestamp":"2025-07-03T08:22:45.596Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/status, PATCH} route","timestamp":"2025-07-03T08:22:45.597Z"}
{"context":"RoutesResolver","level":"info","message":"TasksController {/api/v1/tasks}:","timestamp":"2025-07-03T08:22:45.597Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks, POST} route","timestamp":"2025-07-03T08:22:45.597Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks, GET} route","timestamp":"2025-07-03T08:22:45.598Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/stats/:userId, GET} route","timestamp":"2025-07-03T08:22:45.598Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/project-stats/:projectId, GET} route","timestamp":"2025-07-03T08:22:45.599Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id, GET} route","timestamp":"2025-07-03T08:22:45.599Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id, PATCH} route","timestamp":"2025-07-03T08:22:45.600Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id, DELETE} route","timestamp":"2025-07-03T08:22:45.600Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id/comments, POST} route","timestamp":"2025-07-03T08:22:45.600Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id/time-logs, POST} route","timestamp":"2025-07-03T08:22:45.601Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id/status, PATCH} route","timestamp":"2025-07-03T08:22:45.601Z"}
{"context":"RoutesResolver","level":"info","message":"I18nController {/api/v1/i18n}:","timestamp":"2025-07-03T08:22:45.601Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/i18n/languages, GET} route","timestamp":"2025-07-03T08:22:45.601Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/i18n/translate, GET} route","timestamp":"2025-07-03T08:22:45.602Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/i18n/demo, GET} route","timestamp":"2025-07-03T08:22:45.602Z"}
{"context":"RoutesResolver","level":"info","message":"HealthController {/api/v1/health}:","timestamp":"2025-07-03T08:22:45.602Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health, GET} route","timestamp":"2025-07-03T08:22:45.603Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health/database, GET} route","timestamp":"2025-07-03T08:22:45.603Z"}
{"context":"I18nService","level":"info","message":"Checking translation changes","timestamp":"2025-07-03T08:22:45.809Z"}
{"context":"I18nService","level":"info","message":"No changes detected","timestamp":"2025-07-03T08:22:45.816Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-07-03T08:22:45.819Z"}
{"level":"info","message":"Application is running on: http://localhost:3000","timestamp":"2025-07-03T08:22:45.821Z"}
{"level":"info","message":"Swagger documentation: http://localhost:3000/api/docs","timestamp":"2025-07-03T08:22:45.823Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:22:57.679Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:22:57.720Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:57.722Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:22:57.722Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:22:57.723Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:22:57.723Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:22:57.724Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:22:57.724Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:22:57.724Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:22:57.727Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:22:57.727Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:22:57.727Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:22:57.728Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:22:57.728Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:22:57.745Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:22:57.746Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:22:57.746Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:22:57.747Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:22:57.757Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:22:57.757Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseCoreModule dependencies initialized","timestamp":"2025-07-03T08:22:57.923Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:57.938Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:57.939Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:57.939Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:57.939Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:57.940Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:57.940Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:57.940Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:57.941Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:22:57.941Z"}
{"context":"InstanceLoader","level":"info","message":"UsersModule dependencies initialized","timestamp":"2025-07-03T08:22:57.946Z"}
{"context":"InstanceLoader","level":"info","message":"ProjectsModule dependencies initialized","timestamp":"2025-07-03T08:22:57.947Z"}
{"context":"InstanceLoader","level":"info","message":"ServiceProvidersModule dependencies initialized","timestamp":"2025-07-03T08:22:57.947Z"}
{"context":"InstanceLoader","level":"info","message":"QuotesModule dependencies initialized","timestamp":"2025-07-03T08:22:57.948Z"}
{"context":"InstanceLoader","level":"info","message":"FilesModule dependencies initialized","timestamp":"2025-07-03T08:22:57.948Z"}
{"context":"InstanceLoader","level":"info","message":"NotificationsModule dependencies initialized","timestamp":"2025-07-03T08:22:57.949Z"}
{"context":"InstanceLoader","level":"info","message":"ReviewsModule dependencies initialized","timestamp":"2025-07-03T08:22:57.949Z"}
{"context":"InstanceLoader","level":"info","message":"PaymentsModule dependencies initialized","timestamp":"2025-07-03T08:22:57.950Z"}
{"context":"InstanceLoader","level":"info","message":"TasksModule dependencies initialized","timestamp":"2025-07-03T08:22:57.950Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-07-03T08:22:57.952Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-07-03T08:22:58.017Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/register, POST} route","timestamp":"2025-07-03T08:22:58.019Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/login, POST} route","timestamp":"2025-07-03T08:22:58.020Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-07-03T08:22:58.020Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/forgot-password, POST} route","timestamp":"2025-07-03T08:22:58.021Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/reset-password, POST} route","timestamp":"2025-07-03T08:22:58.021Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-email, POST} route","timestamp":"2025-07-03T08:22:58.021Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/resend-verification, POST} route","timestamp":"2025-07-03T08:22:58.022Z"}
{"context":"RoutesResolver","level":"info","message":"UsersController {/api/v1/users}:","timestamp":"2025-07-03T08:22:58.022Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route","timestamp":"2025-07-03T08:22:58.022Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route","timestamp":"2025-07-03T08:22:58.023Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/me, GET} route","timestamp":"2025-07-03T08:22:58.023Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/stats, GET} route","timestamp":"2025-07-03T08:22:58.024Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/nearby, GET} route","timestamp":"2025-07-03T08:22:58.024Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, GET} route","timestamp":"2025-07-03T08:22:58.024Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/me, PATCH} route","timestamp":"2025-07-03T08:22:58.025Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route","timestamp":"2025-07-03T08:22:58.025Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/me/change-password, POST} route","timestamp":"2025-07-03T08:22:58.025Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id/verify-email, POST} route","timestamp":"2025-07-03T08:22:58.025Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id/verify-phone, POST} route","timestamp":"2025-07-03T08:22:58.026Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/me, DELETE} route","timestamp":"2025-07-03T08:22:58.026Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route","timestamp":"2025-07-03T08:22:58.026Z"}
{"context":"RoutesResolver","level":"info","message":"ProjectsController {/api/v1/projects}:","timestamp":"2025-07-03T08:22:58.027Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects, POST} route","timestamp":"2025-07-03T08:22:58.027Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects, GET} route","timestamp":"2025-07-03T08:22:58.027Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/me, GET} route","timestamp":"2025-07-03T08:22:58.027Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/stats, GET} route","timestamp":"2025-07-03T08:22:58.029Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/me/stats, GET} route","timestamp":"2025-07-03T08:22:58.029Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/nearby, GET} route","timestamp":"2025-07-03T08:22:58.030Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/:id, GET} route","timestamp":"2025-07-03T08:22:58.030Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/:id, PATCH} route","timestamp":"2025-07-03T08:22:58.030Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/:id, DELETE} route","timestamp":"2025-07-03T08:22:58.030Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/projects/:id/publish, POST} route","timestamp":"2025-07-03T08:22:58.030Z"}
{"context":"RoutesResolver","level":"info","message":"ServiceProvidersController {/api/v1/service-providers}:","timestamp":"2025-07-03T08:22:58.031Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers, POST} route","timestamp":"2025-07-03T08:22:58.031Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers, GET} route","timestamp":"2025-07-03T08:22:58.031Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/me, GET} route","timestamp":"2025-07-03T08:22:58.031Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/stats, GET} route","timestamp":"2025-07-03T08:22:58.032Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/:id, GET} route","timestamp":"2025-07-03T08:22:58.032Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/:id, PATCH} route","timestamp":"2025-07-03T08:22:58.032Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/:id, DELETE} route","timestamp":"2025-07-03T08:22:58.032Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/service-providers/:id/review, POST} route","timestamp":"2025-07-03T08:22:58.032Z"}
{"context":"RoutesResolver","level":"info","message":"QuotesController {/api/v1/quotes}:","timestamp":"2025-07-03T08:22:58.033Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes, POST} route","timestamp":"2025-07-03T08:22:58.033Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes, GET} route","timestamp":"2025-07-03T08:22:58.033Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/project/:projectId, GET} route","timestamp":"2025-07-03T08:22:58.034Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/provider/:providerId, GET} route","timestamp":"2025-07-03T08:22:58.034Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/customer/:customerId, GET} route","timestamp":"2025-07-03T08:22:58.034Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id, GET} route","timestamp":"2025-07-03T08:22:58.035Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id, PATCH} route","timestamp":"2025-07-03T08:22:58.035Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id, DELETE} route","timestamp":"2025-07-03T08:22:58.035Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id/submit, POST} route","timestamp":"2025-07-03T08:22:58.035Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id/accept, POST} route","timestamp":"2025-07-03T08:22:58.035Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/quotes/:id/decline, POST} route","timestamp":"2025-07-03T08:22:58.036Z"}
{"context":"RoutesResolver","level":"info","message":"FilesController {/api/v1/files}:","timestamp":"2025-07-03T08:22:58.036Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files/upload, POST} route","timestamp":"2025-07-03T08:22:58.036Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files, GET} route","timestamp":"2025-07-03T08:22:58.036Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files/stats, GET} route","timestamp":"2025-07-03T08:22:58.037Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files/:id, GET} route","timestamp":"2025-07-03T08:22:58.037Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files/:id/download, GET} route","timestamp":"2025-07-03T08:22:58.037Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/files/:id, DELETE} route","timestamp":"2025-07-03T08:22:58.037Z"}
{"context":"RoutesResolver","level":"info","message":"NotificationsController {/api/v1/notifications}:","timestamp":"2025-07-03T08:22:58.037Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications, POST} route","timestamp":"2025-07-03T08:22:58.038Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/bulk, POST} route","timestamp":"2025-07-03T08:22:58.038Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/me, GET} route","timestamp":"2025-07-03T08:22:58.038Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/me/unread-count, GET} route","timestamp":"2025-07-03T08:22:58.038Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/me/stats, GET} route","timestamp":"2025-07-03T08:22:58.039Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/:id, GET} route","timestamp":"2025-07-03T08:22:58.039Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/:id/read, PATCH} route","timestamp":"2025-07-03T08:22:58.039Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/me/read-all, PATCH} route","timestamp":"2025-07-03T08:22:58.039Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/:id, DELETE} route","timestamp":"2025-07-03T08:22:58.039Z"}
{"context":"RoutesResolver","level":"info","message":"ReviewsController {/api/v1/reviews}:","timestamp":"2025-07-03T08:22:58.040Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews, POST} route","timestamp":"2025-07-03T08:22:58.040Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews, GET} route","timestamp":"2025-07-03T08:22:58.040Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/stats/:userId, GET} route","timestamp":"2025-07-03T08:22:58.040Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id, GET} route","timestamp":"2025-07-03T08:22:58.041Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id, PATCH} route","timestamp":"2025-07-03T08:22:58.041Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id, DELETE} route","timestamp":"2025-07-03T08:22:58.041Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id/response, POST} route","timestamp":"2025-07-03T08:22:58.041Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id/like, POST} route","timestamp":"2025-07-03T08:22:58.041Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id/helpful, POST} route","timestamp":"2025-07-03T08:22:58.042Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/reviews/:id/moderate, PATCH} route","timestamp":"2025-07-03T08:22:58.042Z"}
{"context":"RoutesResolver","level":"info","message":"PaymentsController {/api/v1/payments}:","timestamp":"2025-07-03T08:22:58.042Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments, POST} route","timestamp":"2025-07-03T08:22:58.043Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments, GET} route","timestamp":"2025-07-03T08:22:58.043Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/stats/:userId, GET} route","timestamp":"2025-07-03T08:22:58.043Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id, GET} route","timestamp":"2025-07-03T08:22:58.043Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/process, POST} route","timestamp":"2025-07-03T08:22:58.043Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/confirm, POST} route","timestamp":"2025-07-03T08:22:58.045Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/refund, POST} route","timestamp":"2025-07-03T08:22:58.045Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/release-escrow, POST} route","timestamp":"2025-07-03T08:22:58.046Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/cancel, POST} route","timestamp":"2025-07-03T08:22:58.046Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payments/:id/status, PATCH} route","timestamp":"2025-07-03T08:22:58.046Z"}
{"context":"RoutesResolver","level":"info","message":"TasksController {/api/v1/tasks}:","timestamp":"2025-07-03T08:22:58.046Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks, POST} route","timestamp":"2025-07-03T08:22:58.047Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks, GET} route","timestamp":"2025-07-03T08:22:58.047Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/stats/:userId, GET} route","timestamp":"2025-07-03T08:22:58.047Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/project-stats/:projectId, GET} route","timestamp":"2025-07-03T08:22:58.047Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id, GET} route","timestamp":"2025-07-03T08:22:58.047Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id, PATCH} route","timestamp":"2025-07-03T08:22:58.048Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id, DELETE} route","timestamp":"2025-07-03T08:22:58.048Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id/comments, POST} route","timestamp":"2025-07-03T08:22:58.048Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id/time-logs, POST} route","timestamp":"2025-07-03T08:22:58.048Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/tasks/:id/status, PATCH} route","timestamp":"2025-07-03T08:22:58.049Z"}
{"context":"RoutesResolver","level":"info","message":"I18nController {/api/v1/i18n}:","timestamp":"2025-07-03T08:22:58.049Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/i18n/languages, GET} route","timestamp":"2025-07-03T08:22:58.049Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/i18n/translate, GET} route","timestamp":"2025-07-03T08:22:58.049Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/i18n/demo, GET} route","timestamp":"2025-07-03T08:22:58.050Z"}
{"context":"RoutesResolver","level":"info","message":"HealthController {/api/v1/health}:","timestamp":"2025-07-03T08:22:58.050Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health, GET} route","timestamp":"2025-07-03T08:22:58.050Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health/database, GET} route","timestamp":"2025-07-03T08:22:58.050Z"}
{"context":"I18nService","level":"info","message":"Checking translation changes","timestamp":"2025-07-03T08:22:58.249Z"}
{"context":"I18nService","level":"info","message":"No changes detected","timestamp":"2025-07-03T08:22:58.257Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-07-03T08:22:58.259Z"}
{"level":"info","message":"Application is running on: http://localhost:3000","timestamp":"2025-07-03T08:22:58.261Z"}
{"level":"info","message":"Swagger documentation: http://localhost:3000/api/docs","timestamp":"2025-07-03T08:22:58.262Z"}
