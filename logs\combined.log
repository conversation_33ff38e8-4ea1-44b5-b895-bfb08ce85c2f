{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:47:53.614Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:47:53.652Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:47:53.653Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:47:53.653Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:47:53.654Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:47:53.655Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:47:53.655Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:47:53.655Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:47:53.656Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:47:53.657Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:47:53.658Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:47:53.658Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:47:53.659Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:47:53.659Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:47:53.679Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:47:53.680Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:47:53.680Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:47:53.681Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:47:53.683Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:47:53.684Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:47:53.686Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:47:53.686Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:48:23.685Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:48:56.709Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:49:35.839Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:50:08.852Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:50:33.512Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:50:33.550Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:50:33.551Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:50:33.551Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:50:33.552Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:50:33.552Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:50:33.552Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:50:33.553Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:50:33.553Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:50:33.555Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:50:33.555Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:50:33.556Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:50:33.556Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:50:33.556Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:50:33.574Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:50:33.575Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:50:33.576Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:50:33.576Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:50:33.580Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:50:33.581Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:50:33.582Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:50:33.583Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:51:03.485Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:51:03.533Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:51:03.534Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:51:03.535Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:51:03.535Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:51:03.535Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:51:03.539Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:51:03.539Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:51:03.540Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:51:03.541Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:51:03.542Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:51:03.542Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:51:03.542Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:51:03.542Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:51:03.563Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:51:03.564Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:51:03.564Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:51:03.564Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:51:03.567Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:51:03.568Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:51:03.570Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:51:03.570Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:51:33.573Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:52:06.598Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:52:39.627Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:52:41.305Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:52:41.343Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:52:41.344Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:52:41.348Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:52:41.348Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:52:41.349Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:52:41.349Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:52:41.349Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:52:41.349Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:52:41.351Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:52:41.351Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:52:41.352Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:52:41.353Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:52:41.353Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:52:41.370Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:52:41.371Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:52:41.372Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:52:41.372Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:52:41.374Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:52:41.374Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:52:41.375Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:52:41.378Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:54:01.098Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:54:01.136Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:54:01.137Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:54:01.138Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:54:01.138Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:54:01.138Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:54:01.139Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:54:01.139Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:54:01.139Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:54:01.140Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:54:01.141Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:54:01.141Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:54:01.142Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:54:01.142Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:54:01.142Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:54:01.150Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:01.157Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:54:01.158Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:54:01.158Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:54:01.159Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:54:01.165Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:54:01.166Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:54:01.167Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:54:01.167Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:04.164Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:07.181Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:10.189Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:13.205Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:16.212Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:19.219Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:22.233Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:25.254Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:57:30.901Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:57:30.942Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:57:30.942Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:57:30.943Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:57:30.944Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:57:30.945Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:57:30.945Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:57:30.945Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:57:30.946Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:57:30.946Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:57:30.948Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:57:30.948Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:57:30.949Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:57:30.949Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:57:30.949Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:57:30.955Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:30.965Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:57:30.966Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:57:30.966Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:57:30.966Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:57:30.971Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:57:30.971Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:57:30.972Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:57:30.972Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:33.979Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:36.993Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:39.999Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:43.003Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:46.018Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:49.029Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:52.041Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:55.050Z"}
