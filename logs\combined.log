{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:47:53.614Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:47:53.652Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:47:53.653Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:47:53.653Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:47:53.654Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:47:53.655Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:47:53.655Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:47:53.655Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:47:53.656Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:47:53.657Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:47:53.658Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:47:53.658Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:47:53.659Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:47:53.659Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:47:53.679Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:47:53.680Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:47:53.680Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:47:53.681Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:47:53.683Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:47:53.684Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:47:53.686Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:47:53.686Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:48:23.685Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:48:56.709Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:49:35.839Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:50:08.852Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:50:33.512Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:50:33.550Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:50:33.551Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:50:33.551Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:50:33.552Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:50:33.552Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:50:33.552Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:50:33.553Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:50:33.553Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:50:33.555Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:50:33.555Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:50:33.556Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:50:33.556Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:50:33.556Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:50:33.574Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:50:33.575Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:50:33.576Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:50:33.576Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:50:33.580Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:50:33.581Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:50:33.582Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:50:33.583Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:51:03.485Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:51:03.533Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:51:03.534Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:51:03.535Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:51:03.535Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:51:03.535Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:51:03.539Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:51:03.539Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:51:03.540Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:51:03.541Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:51:03.542Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:51:03.542Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:51:03.542Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:51:03.542Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:51:03.563Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:51:03.564Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:51:03.564Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:51:03.564Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:51:03.567Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:51:03.568Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:51:03.570Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:51:03.570Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:51:33.573Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:52:06.598Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:52:39.627Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:52:41.305Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:52:41.343Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:52:41.344Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:52:41.348Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:52:41.348Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:52:41.349Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:52:41.349Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:52:41.349Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:52:41.349Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:52:41.351Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:52:41.351Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:52:41.352Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:52:41.353Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:52:41.353Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:52:41.370Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:52:41.371Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:52:41.372Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:52:41.372Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:52:41.374Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:52:41.374Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:52:41.375Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:52:41.378Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:54:01.098Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:54:01.136Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:54:01.137Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:54:01.138Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:54:01.138Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:54:01.138Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:54:01.139Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:54:01.139Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:54:01.139Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:54:01.140Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:54:01.141Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:54:01.141Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:54:01.142Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:54:01.142Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:54:01.142Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:54:01.150Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:01.157Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:54:01.158Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:54:01.158Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:54:01.159Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:54:01.165Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:54:01.166Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:54:01.167Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:54:01.167Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:04.164Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:07.181Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:10.189Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:13.205Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:16.212Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:19.219Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:22.233Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:54:25.254Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:57:30.901Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:57:30.942Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:57:30.942Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:57:30.943Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:57:30.944Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:57:30.945Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:57:30.945Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:57:30.945Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:57:30.946Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:57:30.946Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:57:30.948Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:57:30.948Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:57:30.949Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:57:30.949Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:57:30.949Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:57:30.955Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:30.965Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:57:30.966Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:57:30.966Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:57:30.966Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:57:30.971Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:57:30.971Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:57:30.972Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:57:30.972Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:33.979Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:36.993Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:39.999Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:43.003Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:46.018Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:49.029Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:52.041Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:375:11)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)\n    at <anonymous> (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:225:18)"],"timestamp":"2025-07-03T07:57:55.050Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:59:04.959Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:59:04.991Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:59:04.992Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:59:04.992Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:59:04.992Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:59:04.993Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:59:04.993Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:59:04.993Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:59:04.994Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:59:04.994Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:59:04.995Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:59:04.996Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:59:04.996Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:59:04.996Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:59:04.996Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:59:05.013Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:59:05.013Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:59:05.014Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:59:05.014Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:59:05.018Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:59:05.019Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:59:05.020Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:59:05.023Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:59:10.027Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:59:18.048Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:59:26.073Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:59:44.454Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:59:44.494Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:59:44.495Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:59:44.497Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:59:44.498Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:59:44.498Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:59:44.498Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:59:44.499Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:59:44.499Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:59:44.501Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:59:44.501Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:59:44.501Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:59:44.502Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:59:44.502Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:59:44.523Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:59:44.524Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:59:44.524Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:59:44.525Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:59:44.528Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:59:44.528Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:59:44.529Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:59:44.529Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:59:49.526Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T07:59:57.555Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:59:59.700Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:59:59.734Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:59:59.735Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:59:59.735Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:59:59.736Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:59:59.736Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:59:59.736Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:59:59.737Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:59:59.737Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:59:59.738Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:59:59.739Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:59:59.739Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:59:59.739Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:59:59.739Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:59:59.757Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:59:59.757Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:59:59.758Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:59:59.759Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:59:59.761Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:59:59.761Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:59:59.762Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:59:59.762Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:00:04.765Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:00:12.772Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:00:20.787Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:00:28.795Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:00:36.815Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:00:44.836Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:00:52.863Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:01:29.550Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:01:29.589Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:01:29.591Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:01:29.591Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:01:29.591Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:01:29.592Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:01:29.592Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:01:29.593Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:01:29.593Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:01:29.595Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:01:29.595Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:01:29.595Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:01:29.595Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:01:29.595Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:01:29.615Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:01:29.615Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:01:29.616Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:01:29.616Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T08:01:29.620Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T08:01:29.621Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:01:29.622Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:01:29.623Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:01:34.625Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:01:42.644Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:01:50.663Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:01:58.681Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:02:06.701Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:02:14.707Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:02:22.720Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:02:30.742Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:02:38.765Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:08:14.129Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:08:14.166Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:08:14.167Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:08:14.167Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:08:14.168Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:08:14.168Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:08:14.168Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:08:14.169Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:08:14.169Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:08:14.171Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:08:14.171Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:08:14.171Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:08:14.171Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:08:14.172Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:08:14.192Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:08:14.193Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:08:14.195Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:08:14.197Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T08:08:14.199Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T08:08:14.199Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:08:14.200Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:08:14.201Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:08:19.209Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:08:30.045Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:08:38.074Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:08:46.093Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:08:54.103Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:09:02.133Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:09:10.159Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:09:18.178Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:09:27.002Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:09:27.037Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:09:27.038Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:09:27.039Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:09:27.039Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:09:27.039Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:09:27.040Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:09:27.040Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:09:27.041Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:09:27.042Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:09:27.042Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:09:27.043Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:09:27.043Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:09:27.044Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:09:27.063Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:09:27.064Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:09:27.064Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:09:27.065Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T08:09:27.067Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T08:09:27.067Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:09:27.068Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:09:27.068Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:09:32.073Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:09:40.091Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:09:47.754Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:09:47.790Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:09:47.794Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:09:47.795Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:09:47.795Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:09:47.795Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:09:47.796Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:09:47.796Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:09:47.796Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:09:47.798Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:09:47.798Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:09:47.799Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:09:47.799Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:09:47.799Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:09:47.816Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:09:47.816Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:09:47.817Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:09:47.817Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T08:09:47.819Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T08:09:47.820Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:09:47.824Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:09:47.824Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:09:52.830Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:00.849Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:08.859Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:16.867Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:24.883Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:32.898Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:40.915Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:48.940Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:10:56.961Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:13:23.846Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:13:23.882Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:13:23.883Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:13:23.883Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:13:23.884Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:13:23.884Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:13:23.884Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:13:23.885Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:13:23.885Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:13:23.887Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:13:23.889Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:13:23.889Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:13:23.890Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:13:23.890Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:13:23.913Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:13:23.914Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:13:23.915Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:13:23.916Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T08:13:23.919Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T08:13:23.921Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:13:23.922Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:13:23.923Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:13:28.920Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:13:36.948Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:13:44.966Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:13:52.983Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:14:01.013Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:14:09.030Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:14:17.052Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:14:28.952Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:14:28.987Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:14:28.988Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:14:28.988Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:14:28.989Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:14:28.990Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:14:28.990Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:14:28.991Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:14:28.991Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:14:28.994Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:14:28.994Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:14:28.995Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:14:28.995Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:14:28.996Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:14:29.015Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:14:29.016Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:14:29.016Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:14:29.017Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T08:14:29.019Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T08:14:29.019Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:14:29.020Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:14:29.021Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:14:34.020Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:14:42.041Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:14:50.063Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:14:58.081Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:15:06.103Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:15:14.115Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:15:22.132Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:15:30.145Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:15:38.163Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:15:54.939Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:15:54.972Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:15:54.973Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:15:54.973Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:15:54.973Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:15:54.974Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:15:54.974Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:15:54.974Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:15:54.975Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:15:54.976Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:15:54.977Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:15:54.977Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:15:54.980Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:15:54.981Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:15:55.001Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:15:55.001Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:15:55.002Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:15:55.003Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T08:15:55.005Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T08:15:55.006Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:15:55.007Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:15:55.007Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:16:00.009Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:16:08.024Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:16:16.044Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:16:24.065Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:16:32.083Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:16:40.110Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.asPromise (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:1278:11)\n    at async D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:112"],"timestamp":"2025-07-03T08:16:48.137Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:16:54.774Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:16:54.814Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:16:54.814Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:16:54.815Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:16:54.815Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:16:54.815Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:16:54.816Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:16:54.816Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:16:54.816Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:16:54.818Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:16:54.818Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:16:54.818Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:16:54.819Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:16:54.819Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:16:54.823Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:16:54.830Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:16:54.831Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:16:54.831Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:16:54.832Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:16:54.841Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:16:54.844Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:16:57.843Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:17:00.854Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:17:03.870Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (5)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:17:06.884Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (6)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:17:09.889Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (7)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:17:12.901Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (8)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:17:15.910Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (9)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:17:18.917Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:18:29.663Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:18:29.700Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:18:29.700Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:18:29.701Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:18:29.701Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:18:29.702Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:18:29.702Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:18:29.702Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:18:29.703Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:18:29.705Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:18:29.706Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:18:29.706Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:18:29.706Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:18:29.707Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:18:29.711Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:18:29.718Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:18:29.719Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:18:29.722Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:18:29.723Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:18:29.732Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:18:29.732Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (2)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:18:32.725Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (3)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:18:35.728Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (4)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:18:38.740Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T08:18:41.829Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T08:18:41.881Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T08:18:41.882Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T08:18:41.882Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T08:18:41.882Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T08:18:41.883Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T08:18:41.883Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T08:18:41.887Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T08:18:41.887Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T08:18:41.889Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T08:18:41.889Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T08:18:41.890Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:18:41.890Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T08:18:41.890Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T08:18:41.895Z"}
{"context":"MongooseModule","level":"error","message":"Unable to connect to the database. Retrying (1)...","stack":["MongoParseError: Protocol and host list are required in \"mongodb://root:NdaGqBe@@<EMAIL>:3717\"\n    at new ConnectionString (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb-connection-string-url\\src\\index.ts:145:15)\n    at parseOptions (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\connection_string.ts:256:15)\n    at new MongoClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongodb\\src\\mongo_client.ts:368:34)\n    at NativeConnection.createClient (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.createConnection (D:\\Tommi\\Desktop\\test\\node_modules\\mongoose\\lib\\index.js:361:10)\n    at MongooseCoreModule.createMongooseConnection (D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:114:37)\n    at D:\\Tommi\\Desktop\\test\\node_modules\\@nestjs\\mongoose\\dist\\mongoose-core.module.js:65:123\n    at Observable._subscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\observable\\defer.ts:54:15)\n    at Observable.Observable._trySubscribe (D:\\Tommi\\Desktop\\test\\node_modules\\rxjs\\src\\internal\\Observable.ts:235:19)"],"timestamp":"2025-07-03T08:18:41.904Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T08:18:41.906Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T08:18:41.908Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T08:18:41.908Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:18:41.918Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T08:18:41.919Z"}
