{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T07:47:53.614Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T07:47:53.652Z"}
{"context":"InstanceLoader","level":"info","message":"MongooseModule dependencies initialized","timestamp":"2025-07-03T07:47:53.653Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T07:47:53.653Z"}
{"context":"InstanceLoader","level":"info","message":"RedisModule dependencies initialized","timestamp":"2025-07-03T07:47:53.654Z"}
{"context":"InstanceLoader","level":"info","message":"EmailModule dependencies initialized","timestamp":"2025-07-03T07:47:53.655Z"}
{"context":"InstanceLoader","level":"info","message":"SmsModule dependencies initialized","timestamp":"2025-07-03T07:47:53.655Z"}
{"context":"InstanceLoader","level":"info","message":"TranslationModule dependencies initialized","timestamp":"2025-07-03T07:47:53.655Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T07:47:53.656Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T07:47:53.657Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-07-03T07:47:53.658Z"}
{"context":"InstanceLoader","level":"info","message":"HealthModule dependencies initialized","timestamp":"2025-07-03T07:47:53.658Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:47:53.659Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T07:47:53.659Z"}
{"context":"InstanceLoader","level":"info","message":"BullModule dependencies initialized","timestamp":"2025-07-03T07:47:53.679Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T07:47:53.680Z"}
{"context":"InstanceLoader","level":"info","message":"ThrottlerModule dependencies initialized","timestamp":"2025-07-03T07:47:53.680Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-07-03T07:47:53.681Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n","syscall":"stat"}],"timestamp":"2025-07-03T07:47:53.683Z"}
{"context":"I18nService","level":"error","message":"parsing translation error","stack":[{"code":"ENOENT","errno":-4058,"path":"D:\\Tommi\\Desktop\\test\\i18n\\","syscall":"scandir"}],"timestamp":"2025-07-03T07:47:53.684Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:47:53.686Z"}
{"context":"InstanceLoader","level":"info","message":"I18nModule dependencies initialized","timestamp":"2025-07-03T07:47:53.686Z"}
