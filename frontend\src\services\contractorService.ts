import { api } from './api';
import type { ApiResponse, PaginationParams } from './api';
import type { ServiceProvider, ProjectCategory } from '../types';

// 服务商相关的请求类型
export interface ContractorRegistrationRequest {
  businessName: string;
  businessNumber?: string;
  description: string;
  specialties: ProjectCategory[];
  serviceAreas: string[];
  experience: number;
  website?: string;
  portfolio?: {
    title: string;
    description: string;
    images: string[];
    category: ProjectCategory;
    completedDate: Date;
  }[];
  certifications?: {
    name: string;
    issuer: string;
    issueDate: Date;
    expiryDate?: Date;
    certificateUrl?: string;
  }[];
  insurance?: {
    provider: string;
    policyNumber: string;
    coverage: number;
    expiryDate: Date;
    certificateUrl?: string;
  };
}

export interface ContractorSearchParams extends PaginationParams {
  specialty?: ProjectCategory;
  serviceArea?: string;
  minRating?: number;
  maxDistance?: number;
  verified?: boolean;
  keyword?: string;
  location?: {
    latitude: number;
    longitude: number;
  };
}

export interface ContractorProfileUpdate extends Partial<ContractorRegistrationRequest> {
  hourlyRate?: number;
  availability?: 'available' | 'busy' | 'unavailable';
}

// 服务商服务
export const contractorService = {
  // 注册为服务商
  registerAsContractor: async (data: ContractorRegistrationRequest): Promise<ApiResponse<ServiceProvider>> => {
    return api.post<ServiceProvider>('/contractors/register', data);
  },

  // 获取服务商列表
  getContractors: async (params?: ContractorSearchParams): Promise<ApiResponse<ServiceProvider[]>> => {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (key === 'location' && typeof value === 'object') {
            queryParams.append('latitude', value.latitude.toString());
            queryParams.append('longitude', value.longitude.toString());
          } else {
            queryParams.append(key, value.toString());
          }
        }
      });
    }
    
    const url = `/contractors${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return api.get<ServiceProvider[]>(url);
  },

  // 获取单个服务商详情
  getContractor: async (contractorId: string): Promise<ApiResponse<ServiceProvider>> => {
    return api.get<ServiceProvider>(`/contractors/${contractorId}`);
  },

  // 更新服务商资料
  updateContractorProfile: async (
    contractorId: string, 
    data: ContractorProfileUpdate
  ): Promise<ApiResponse<ServiceProvider>> => {
    return api.put<ServiceProvider>(`/contractors/${contractorId}`, data);
  },

  // 获取我的服务商资料
  getMyContractorProfile: async (): Promise<ApiResponse<ServiceProvider>> => {
    return api.get<ServiceProvider>('/contractors/my-profile');
  },

  // 上传服务商头像
  uploadContractorAvatar: async (
    contractorId: string,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<{ avatarUrl: string }>> => {
    return api.upload<{ avatarUrl: string }>(`/contractors/${contractorId}/avatar`, file, onProgress);
  },

  // 上传作品集图片
  uploadPortfolioImage: async (
    contractorId: string,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<{ imageUrl: string }>> => {
    return api.upload<{ imageUrl: string }>(`/contractors/${contractorId}/portfolio`, file, onProgress);
  },

  // 上传认证文件
  uploadCertification: async (
    contractorId: string,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<{ certificateUrl: string }>> => {
    return api.upload<{ certificateUrl: string }>(`/contractors/${contractorId}/certifications`, file, onProgress);
  },

  // 上传保险文件
  uploadInsuranceDocument: async (
    contractorId: string,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<{ documentUrl: string }>> => {
    return api.upload<{ documentUrl: string }>(`/contractors/${contractorId}/insurance`, file, onProgress);
  },

  // 搜索服务商
  searchContractors: async (
    keyword: string,
    filters?: Omit<ContractorSearchParams, 'keyword'>
  ): Promise<ApiResponse<ServiceProvider[]>> => {
    const params = { keyword, ...filters };
    return contractorService.getContractors(params);
  },

  // 获取推荐服务商
  getRecommendedContractors: async (
    projectId?: string,
    limit: number = 10
  ): Promise<ApiResponse<ServiceProvider[]>> => {
    const url = projectId 
      ? `/contractors/recommended?projectId=${projectId}&limit=${limit}`
      : `/contractors/recommended?limit=${limit}`;
    return api.get<ServiceProvider[]>(url);
  },

  // 获取热门服务商
  getPopularContractors: async (limit: number = 10): Promise<ApiResponse<ServiceProvider[]>> => {
    return api.get<ServiceProvider[]>(`/contractors/popular?limit=${limit}`);
  },

  // 获取附近的服务商
  getNearbyContractors: async (
    latitude: number,
    longitude: number,
    radius: number = 50,
    limit: number = 20
  ): Promise<ApiResponse<ServiceProvider[]>> => {
    return api.get<ServiceProvider[]>(
      `/contractors/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}&limit=${limit}`
    );
  },

  // 获取服务商统计信息
  getContractorStats: async (contractorId?: string): Promise<ApiResponse<{
    totalProjects: number;
    completedProjects: number;
    activeProjects: number;
    averageRating: number;
    totalReviews: number;
    responseRate: number;
    onTimeRate: number;
  }>> => {
    const url = contractorId ? `/contractors/${contractorId}/stats` : '/contractors/my-stats';
    return api.get(url);
  },

  // 获取服务商可用性
  getContractorAvailability: async (contractorId: string): Promise<ApiResponse<{
    status: 'available' | 'busy' | 'unavailable';
    nextAvailableDate?: Date;
    workingHours: {
      [key: string]: { start: string; end: string; available: boolean };
    };
  }>> => {
    return api.get(`/contractors/${contractorId}/availability`);
  },

  // 更新服务商可用性
  updateContractorAvailability: async (
    contractorId: string,
    availability: {
      status: 'available' | 'busy' | 'unavailable';
      nextAvailableDate?: Date;
      workingHours?: {
        [key: string]: { start: string; end: string; available: boolean };
      };
    }
  ): Promise<ApiResponse<{ message: string }>> => {
    return api.put<{ message: string }>(`/contractors/${contractorId}/availability`, availability);
  },

  // 验证服务商
  verifyContractor: async (contractorId: string): Promise<ApiResponse<{ message: string }>> => {
    return api.post<{ message: string }>(`/contractors/${contractorId}/verify`);
  },

  // 举报服务商
  reportContractor: async (
    contractorId: string,
    reason: string,
    description: string
  ): Promise<ApiResponse<{ message: string }>> => {
    return api.post<{ message: string }>(`/contractors/${contractorId}/report`, {
      reason,
      description,
    });
  },

  // 收藏服务商
  favoriteContractor: async (contractorId: string): Promise<ApiResponse<{ message: string }>> => {
    return api.post<{ message: string }>(`/contractors/${contractorId}/favorite`);
  },

  // 取消收藏服务商
  unfavoriteContractor: async (contractorId: string): Promise<ApiResponse<{ message: string }>> => {
    return api.delete<{ message: string }>(`/contractors/${contractorId}/favorite`);
  },

  // 获取收藏的服务商
  getFavoriteContractors: async (params?: PaginationParams): Promise<ApiResponse<ServiceProvider[]>> => {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const url = `/contractors/favorites${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return api.get<ServiceProvider[]>(url);
  },
};
