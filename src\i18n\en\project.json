{"title": "Projects", "create": "Create Project", "edit": "Edit Project", "delete": "Delete Project", "view": "View Project", "details": "Project Details", "overview": "Overview", "timeline": "Timeline", "budget": "Budget", "documents": "Documents", "photos": "Photos", "contractors": "Contractors", "quotes": "Quotes", "tasks": "Tasks", "reviews": "Reviews", "fields": {"title": "Project Title", "description": "Description", "category": "Category", "budget": "Budget", "startDate": "Start Date", "endDate": "End Date", "estimatedDuration": "Estimated Duration", "actualDuration": "Actual Duration", "location": "Location", "propertyType": "Property Type", "propertySize": "Property Size", "rooms": "Rooms", "urgency": "Urgency", "requirements": "Requirements", "preferences": "Preferences", "tags": "Tags"}, "categories": {"kitchen": "Kitchen Renovation", "bathroom": "Bathroom Renovation", "basement": "Basement Renovation", "flooring": "Flooring", "painting": "Painting", "roofing": "Roofing", "plumbing": "Plumbing", "electrical": "Electrical", "hvac": "HVAC", "landscaping": "Landscaping", "addition": "Home Addition", "exterior": "Exterior Renovation", "other": "Other"}, "propertyTypes": {"house": "House", "condo": "Condominium", "townhouse": "Townhouse", "apartment": "Apartment", "commercial": "Commercial", "other": "Other"}, "urgency": {"low": "Low", "medium": "Medium", "high": "High", "urgent": "<PERSON><PERSON>"}, "status": {"draft": "Draft", "published": "Published", "in_progress": "In Progress", "on_hold": "On Hold", "completed": "Completed", "cancelled": "Cancelled"}, "actions": {"publish": "Publish Project", "unpublish": "Unpublish Project", "start": "Start Project", "pause": "Pause Project", "resume": "Resume Project", "complete": "Complete Project", "cancel": "Cancel Project", "archive": "Archive Project", "duplicate": "Duplicate Project", "share": "Share Project", "export": "Export Project", "invite": "Invite Contractors", "requestQuote": "Request Quote", "acceptQuote": "Accept Quote", "rejectQuote": "Reject Quote"}, "messages": {"created": "Project created successfully!", "updated": "Project updated successfully!", "deleted": "Project deleted successfully!", "published": "Project published successfully!", "unpublished": "Project unpublished successfully!", "started": "Project started successfully!", "paused": "Project paused successfully!", "resumed": "Project resumed successfully!", "completed": "Project completed successfully!", "cancelled": "Project cancelled successfully!", "archived": "Project archived successfully!", "duplicated": "Project duplicated successfully!", "shared": "Project shared successfully!", "exported": "Project exported successfully!", "invitationSent": "Invitation sent to contractors!", "quoteRequested": "Quote requested successfully!", "quoteAccepted": "Quote accepted successfully!", "quoteRejected": "Quote rejected successfully!"}, "filters": {"all": "All Projects", "myProjects": "My Projects", "active": "Active Projects", "completed": "Completed Projects", "draft": "Draft Projects", "byCategory": "By Category", "byBudget": "By Budget", "byLocation": "By Location", "byDate": "By Date"}, "search": {"placeholder": "Search projects...", "noResults": "No projects found", "results": "{{count}} projects found"}}