import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  Payment,
  PaymentDocument,
  PaymentStatus,
  PaymentType,
} from './schemas/payment.schema';
import {
  CreatePaymentDto,
  PaymentQueryDto,
  ProcessPaymentDto,
  RefundPaymentDto,
  EscrowReleaseDto,
  UpdatePaymentStatusDto,
} from './dto/create-payment.dto';
import { PaginatedResult } from '../../common/interfaces/paginated-result.interface';
import { User } from '../users/schemas/user.schema';

@Injectable()
export class PaymentsService {
  constructor(
    @InjectModel(Payment.name) private paymentModel: Model<PaymentDocument>,
  ) {}

  /**
   * 创建支付
   */
  async create(createPaymentDto: CreatePaymentDto, user: User): Promise<Payment> {
    const paymentData = {
      ...createPaymentDto,
      payerId: user._id,
      payeeId: new Types.ObjectId(createPaymentDto.payeeId),
      projectId: createPaymentDto.projectId ? new Types.ObjectId(createPaymentDto.projectId) : undefined,
      quoteId: createPaymentDto.quoteId ? new Types.ObjectId(createPaymentDto.quoteId) : undefined,
      expiresAt: createPaymentDto.expiresAt ? new Date(createPaymentDto.expiresAt) : undefined,
    };

    const createdPayment = new this.paymentModel(paymentData);
    return createdPayment.save();
  }

  /**
   * 获取支付列表
   */
  async findAll(query: PaymentQueryDto, user?: User): Promise<PaginatedResult<Payment>> {
    const {
      page = 1,
      limit = 20,
      type,
      status,
      method,
      currency,
      payerId,
      payeeId,
      projectId,
      quoteId,
      minAmount,
      maxAmount,
      isEscrow,
      startDate,
      endDate,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = query;

    const filter: any = {
      isDeleted: false,
    };

    // 如果有用户信息，只显示与该用户相关的支付
    if (user) {
      filter.$or = [
        { payerId: user._id },
        { payeeId: user._id },
      ];
    }

    // 类型过滤
    if (type) {
      filter.type = type;
    }

    // 状态过滤
    if (status) {
      filter.status = status;
    }

    // 支付方式过滤
    if (method) {
      filter.method = method;
    }

    // 货币过滤
    if (currency) {
      filter.currency = currency;
    }

    // 付款人过滤
    if (payerId) {
      filter.payerId = payerId;
    }

    // 收款人过滤
    if (payeeId) {
      filter.payeeId = payeeId;
    }

    // 项目过滤
    if (projectId) {
      filter.projectId = projectId;
    }

    // 报价过滤
    if (quoteId) {
      filter.quoteId = quoteId;
    }

    // 金额范围过滤
    if (minAmount !== undefined || maxAmount !== undefined) {
      filter.amount = {};
      if (minAmount !== undefined) {
        filter.amount.$gte = minAmount;
      }
      if (maxAmount !== undefined) {
        filter.amount.$lte = maxAmount;
      }
    }

    // 托管过滤
    if (isEscrow !== undefined) {
      filter.isEscrow = isEscrow;
    }

    // 日期范围过滤
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) {
        filter.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        filter.createdAt.$lte = new Date(endDate);
      }
    }

    // 搜索
    if (search) {
      filter.$or = [
        { description: new RegExp(search, 'i') },
        { notes: new RegExp(search, 'i') },
        { 'details.confirmationNumber': new RegExp(search, 'i') },
      ];
    }

    // 排序
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [items, total] = await Promise.all([
      this.paymentModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('payerId', 'profile.firstName profile.lastName profile.avatar')
        .populate('payeeId', 'profile.firstName profile.lastName profile.avatar')
        .populate('projectId', 'title status')
        .populate('quoteId', 'title status')
        .exec(),
      this.paymentModel.countDocuments(filter),
    ]);

    return {
      items,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * 根据ID获取支付详情
   */
  async findOne(id: string, user?: User): Promise<Payment> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid payment ID');
    }

    const filter: any = {
      _id: id,
      isDeleted: false,
    };

    // 权限检查
    if (user) {
      filter.$or = [
        { payerId: user._id },
        { payeeId: user._id },
      ];
    }

    const payment = await this.paymentModel
      .findOne(filter)
      .populate('payerId', 'profile.firstName profile.lastName profile.avatar')
      .populate('payeeId', 'profile.firstName profile.lastName profile.avatar')
      .populate('projectId', 'title status')
      .populate('quoteId', 'title status')
      .exec();

    if (!payment) {
      throw new NotFoundException('Payment not found');
    }

    return payment;
  }

  /**
   * 处理支付
   */
  async processPayment(id: string, processPaymentDto: ProcessPaymentDto, user: User): Promise<Payment> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid payment ID');
    }

    const payment = await this.paymentModel.findOne({
      _id: id,
      payerId: user._id,
      isDeleted: false,
    });

    if (!payment) {
      throw new NotFoundException('Payment not found');
    }

    if (payment.status !== PaymentStatus.PENDING) {
      throw new BadRequestException('Payment cannot be processed');
    }

    // 这里应该集成实际的支付网关（如Stripe）
    // 现在只是模拟处理
    const updatedPayment = await this.paymentModel
      .findByIdAndUpdate(
        id,
        {
          status: PaymentStatus.PROCESSING,
          paymentIntentId: `pi_${Date.now()}`, // 模拟支付意图ID
          details: {
            gatewayTransactionId: `txn_${Date.now()}`,
            gateway: 'stripe',
            confirmationNumber: `conf_${Date.now()}`,
          },
        },
        { new: true }
      )
      .populate('payerId', 'profile.firstName profile.lastName profile.avatar')
      .populate('payeeId', 'profile.firstName profile.lastName profile.avatar')
      .exec();

    return updatedPayment;
  }

  /**
   * 确认支付完成
   */
  async confirmPayment(id: string): Promise<Payment> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid payment ID');
    }

    const updatedPayment = await this.paymentModel
      .findByIdAndUpdate(
        id,
        {
          status: PaymentStatus.COMPLETED,
          completedAt: new Date(),
        },
        { new: true }
      )
      .populate('payerId', 'profile.firstName profile.lastName profile.avatar')
      .populate('payeeId', 'profile.firstName profile.lastName profile.avatar')
      .exec();

    if (!updatedPayment) {
      throw new NotFoundException('Payment not found');
    }

    return updatedPayment;
  }

  /**
   * 支付失败
   */
  async failPayment(id: string, reason: string): Promise<Payment> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid payment ID');
    }

    const updatedPayment = await this.paymentModel
      .findByIdAndUpdate(
        id,
        {
          status: PaymentStatus.FAILED,
          failedAt: new Date(),
          failureReason: reason,
        },
        { new: true }
      )
      .populate('payerId', 'profile.firstName profile.lastName profile.avatar')
      .populate('payeeId', 'profile.firstName profile.lastName profile.avatar')
      .exec();

    if (!updatedPayment) {
      throw new NotFoundException('Payment not found');
    }

    return updatedPayment;
  }

  /**
   * 退款
   */
  async refundPayment(id: string, refundDto: RefundPaymentDto, user: User): Promise<Payment> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid payment ID');
    }

    const payment = await this.paymentModel.findOne({
      _id: id,
      isDeleted: false,
    });

    if (!payment) {
      throw new NotFoundException('Payment not found');
    }

    // 权限检查：只有付款人或收款人可以发起退款
    if (payment.payerId.toString() !== user._id.toString() &&
        payment.payeeId.toString() !== user._id.toString()) {
      throw new ForbiddenException('You do not have permission to refund this payment');
    }

    if (payment.status !== PaymentStatus.COMPLETED) {
      throw new BadRequestException('Only completed payments can be refunded');
    }

    const totalRefunded = payment.refunds?.reduce((sum, refund) => sum + refund.amount, 0) || 0;
    const remainingAmount = payment.amount - totalRefunded;

    if (refundDto.amount > remainingAmount) {
      throw new BadRequestException('Refund amount exceeds remaining amount');
    }

    const refundInfo = {
      amount: refundDto.amount,
      reason: refundDto.reason,
      initiatedBy: user._id,
      refundedAt: new Date(),
      refundTransactionId: `ref_${Date.now()}`, // 模拟退款交易ID
      status: 'completed',
    };

    const newStatus = refundDto.amount === remainingAmount
      ? PaymentStatus.REFUNDED
      : PaymentStatus.PARTIALLY_REFUNDED;

    const updatedPayment = await this.paymentModel
      .findByIdAndUpdate(
        id,
        {
          $push: { refunds: refundInfo },
          status: newStatus,
        },
        { new: true }
      )
      .populate('payerId', 'profile.firstName profile.lastName profile.avatar')
      .populate('payeeId', 'profile.firstName profile.lastName profile.avatar')
      .exec();

    return updatedPayment;
  }

  /**
   * 释放托管资金
   */
  async releaseEscrow(id: string, releaseDto: EscrowReleaseDto, user: User): Promise<Payment> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid payment ID');
    }

    const payment = await this.paymentModel.findOne({
      _id: id,
      isDeleted: false,
    });

    if (!payment) {
      throw new NotFoundException('Payment not found');
    }

    if (!payment.isEscrow) {
      throw new BadRequestException('This payment is not in escrow');
    }

    if (payment.status !== PaymentStatus.COMPLETED) {
      throw new BadRequestException('Only completed payments can have escrow released');
    }

    if (payment.escrowReleasedAt) {
      throw new BadRequestException('Escrow has already been released');
    }

    // 权限检查：只有付款人可以释放托管资金
    if (payment.payerId.toString() !== user._id.toString()) {
      throw new ForbiddenException('Only the payer can release escrow funds');
    }

    const updatedPayment = await this.paymentModel
      .findByIdAndUpdate(
        id,
        {
          escrowReleasedAt: new Date(),
          escrowReleasedBy: user._id,
          notes: releaseDto.notes || payment.notes,
        },
        { new: true }
      )
      .populate('payerId', 'profile.firstName profile.lastName profile.avatar')
      .populate('payeeId', 'profile.firstName profile.lastName profile.avatar')
      .exec();

    return updatedPayment;
  }

  /**
   * 取消支付
   */
  async cancelPayment(id: string, reason: string, user: User): Promise<Payment> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid payment ID');
    }

    const payment = await this.paymentModel.findOne({
      _id: id,
      payerId: user._id,
      isDeleted: false,
    });

    if (!payment) {
      throw new NotFoundException('Payment not found');
    }

    if (payment.status !== PaymentStatus.PENDING && payment.status !== PaymentStatus.PROCESSING) {
      throw new BadRequestException('Payment cannot be cancelled');
    }

    const updatedPayment = await this.paymentModel
      .findByIdAndUpdate(
        id,
        {
          status: PaymentStatus.CANCELLED,
          cancelledAt: new Date(),
          cancellationReason: reason,
        },
        { new: true }
      )
      .populate('payerId', 'profile.firstName profile.lastName profile.avatar')
      .populate('payeeId', 'profile.firstName profile.lastName profile.avatar')
      .exec();

    return updatedPayment;
  }

  /**
   * 更新支付状态（管理员）
   */
  async updateStatus(id: string, updateStatusDto: UpdatePaymentStatusDto, user: User): Promise<Payment> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid payment ID');
    }

    const updatedPayment = await this.paymentModel
      .findByIdAndUpdate(
        id,
        {
          status: updateStatusDto.status,
          notes: updateStatusDto.notes || undefined,
        },
        { new: true }
      )
      .populate('payerId', 'profile.firstName profile.lastName profile.avatar')
      .populate('payeeId', 'profile.firstName profile.lastName profile.avatar')
      .exec();

    if (!updatedPayment) {
      throw new NotFoundException('Payment not found');
    }

    return updatedPayment;
  }

  /**
   * 获取用户支付统计
   */
  async getUserStats(userId: string): Promise<any> {
    const [paidStats, receivedStats] = await Promise.all([
      // 支付统计
      this.paymentModel.aggregate([
        {
          $match: {
            payerId: new Types.ObjectId(userId),
            isDeleted: false,
          },
        },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            totalAmount: { $sum: '$amount' },
          },
        },
      ]),
      // 收款统计
      this.paymentModel.aggregate([
        {
          $match: {
            payeeId: new Types.ObjectId(userId),
            isDeleted: false,
          },
        },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            totalAmount: { $sum: '$amount' },
          },
        },
      ]),
    ]);

    const formatStats = (stats: any[]) => {
      const result = {
        total: 0,
        totalAmount: 0,
        pending: 0,
        pendingAmount: 0,
        completed: 0,
        completedAmount: 0,
        failed: 0,
        failedAmount: 0,
        cancelled: 0,
        cancelledAmount: 0,
      };

      stats.forEach(stat => {
        result.total += stat.count;
        result.totalAmount += stat.totalAmount;

        switch (stat._id) {
          case PaymentStatus.PENDING:
            result.pending = stat.count;
            result.pendingAmount = stat.totalAmount;
            break;
          case PaymentStatus.COMPLETED:
            result.completed = stat.count;
            result.completedAmount = stat.totalAmount;
            break;
          case PaymentStatus.FAILED:
            result.failed = stat.count;
            result.failedAmount = stat.totalAmount;
            break;
          case PaymentStatus.CANCELLED:
            result.cancelled = stat.count;
            result.cancelledAmount = stat.totalAmount;
            break;
        }
      });

      return result;
    };

    return {
      paid: formatStats(paidStats),
      received: formatStats(receivedStats),
    };
  }
}
