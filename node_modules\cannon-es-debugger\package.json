{"name": "cannon-es-debugger", "version": "1.0.0", "description": "Wireframe debugger for use with cannon-es https://github.com/pmndrs/cannon-es", "keywords": ["debugger", "cannon", "three", "react", "react-three-fiber"], "author": "<PERSON> <<EMAIL>> (http://steffe.se)", "contributors": ["<PERSON> <<EMAIL>> (https://github.com/codynova)", "<PERSON> <<EMAIL>> (https://github.com/marcofugaro)"], "license": "MIT", "main": "./dist/cannon-es-debugger.cjs.js", "module": "./dist/cannon-es-debugger.js", "types": "./dist/cannon-es-debugger.d.ts", "sideEffects": false, "files": ["dist"], "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "npm run typegen && rollup -c", "typecheck": "tsc --noEmit --emitDeclarationOnly false --strict", "typegen": "tsc --outFile dist/cannon-es-debugger.d.ts"}, "peerDependencies": {"cannon-es": "0.x", "three": "0.x", "typescript": ">=3.8"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "devDependencies": {"@babel/core": "^7.16.5", "@babel/preset-env": "^7.16.5", "@babel/preset-typescript": "^7.16.5", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-node-resolve": "^13.1.1", "@types/three": "^0.135.0", "cannon-es": "^0.18.0", "prettier": "^2.5.1", "rimraf": "^3.0.2", "rollup": "^2.62.0", "three": "^0.136.0", "typescript": "^4.5.4"}}