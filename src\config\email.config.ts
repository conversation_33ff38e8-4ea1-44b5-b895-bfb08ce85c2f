import { registerAs } from '@nestjs/config';

export const emailConfig = registerAs('email', () => ({
  smtp: {
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT) || 587,
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  },
  from: {
    name: process.env.EMAIL_FROM_NAME || 'HomeReno',
    address: process.env.EMAIL_FROM_ADDRESS || '<EMAIL>',
  },
  templates: {
    verification: 'email-verification',
    passwordReset: 'password-reset',
    welcome: 'welcome',
    projectUpdate: 'project-update',
    quoteReceived: 'quote-received',
  },
}));
