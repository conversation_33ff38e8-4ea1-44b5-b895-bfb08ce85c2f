import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  Notification,
  NotificationDocument,
  NotificationType,
  NotificationChannel,
} from './schemas/notification.schema';
import {
  CreateNotificationDto,
  NotificationQueryDto,
  BulkNotificationDto,
} from './dto/create-notification.dto';
import { PaginatedResult } from '../../common/interfaces/paginated-result.interface';
import { User } from '../users/schemas/user.schema';

@Injectable()
export class NotificationsService {
  constructor(
    @InjectModel(Notification.name)
    private notificationModel: Model<NotificationDocument>,
  ) {}

  /**
   * 创建单个通知
   */
  async create(createNotificationDto: CreateNotificationDto): Promise<Notification> {
    const notificationData = {
      ...createNotificationDto,
      userId: new Types.ObjectId(createNotificationDto.userId),
      senderId: createNotificationDto.senderId
        ? new Types.ObjectId(createNotificationDto.senderId)
        : undefined,
      scheduledAt: createNotificationDto.scheduledAt
        ? new Date(createNotificationDto.scheduledAt)
        : undefined,
      expiresAt: createNotificationDto.expiresAt
        ? new Date(createNotificationDto.expiresAt)
        : undefined,
    };

    const createdNotification = new this.notificationModel(notificationData);
    return createdNotification.save();
  }

  /**
   * 批量创建通知
   */
  async createBulk(bulkNotificationDto: BulkNotificationDto): Promise<Notification[]> {
    const notifications = bulkNotificationDto.userIds.map(userId => ({
      ...bulkNotificationDto,
      userId: new Types.ObjectId(userId),
      senderId: bulkNotificationDto.senderId
        ? new Types.ObjectId(bulkNotificationDto.senderId)
        : undefined,
      scheduledAt: bulkNotificationDto.scheduledAt
        ? new Date(bulkNotificationDto.scheduledAt)
        : undefined,
      expiresAt: bulkNotificationDto.expiresAt
        ? new Date(bulkNotificationDto.expiresAt)
        : undefined,
    }));

    return this.notificationModel.insertMany(notifications);
  }

  /**
   * 获取用户的通知列表
   */
  async findByUser(
    userId: string,
    query: NotificationQueryDto,
  ): Promise<PaginatedResult<Notification>> {
    const {
      page = 1,
      limit = 20,
      type,
      priority,
      isRead,
      isSent,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = query;

    const filter: any = {
      userId: new Types.ObjectId(userId),
      isDeleted: false,
    };

    // 类型过滤
    if (type) {
      filter.type = type;
    }

    // 优先级过滤
    if (priority) {
      filter.priority = priority;
    }

    // 已读状态过滤
    if (isRead !== undefined) {
      filter.isRead = isRead;
    }

    // 发送状态过滤
    if (isSent !== undefined) {
      filter.isSent = isSent;
    }

    // 搜索
    if (search) {
      filter.$or = [
        { title: new RegExp(search, 'i') },
        { message: new RegExp(search, 'i') },
      ];
    }

    // 排序
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [items, total] = await Promise.all([
      this.notificationModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('senderId', 'profile.firstName profile.lastName')
        .exec(),
      this.notificationModel.countDocuments(filter),
    ]);

    return {
      items,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * 根据ID获取通知详情
   */
  async findOne(id: string, user: User): Promise<Notification> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid notification ID');
    }

    const notification = await this.notificationModel
      .findOne({
        _id: id,
        isDeleted: false,
      })
      .populate('senderId', 'profile.firstName profile.lastName')
      .exec();

    if (!notification) {
      throw new NotFoundException('Notification not found');
    }

    // 权限检查
    if (notification.userId.toString() !== user._id.toString()) {
      throw new ForbiddenException('You can only access your own notifications');
    }

    return notification;
  }

  /**
   * 标记通知为已读
   */
  async markAsRead(id: string, user: User): Promise<Notification> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid notification ID');
    }

    const notification = await this.notificationModel.findOne({
      _id: id,
      userId: user._id,
      isDeleted: false,
    });

    if (!notification) {
      throw new NotFoundException('Notification not found');
    }

    if (notification.isRead) {
      return notification;
    }

    const updatedNotification = await this.notificationModel
      .findByIdAndUpdate(
        id,
        {
          isRead: true,
          readAt: new Date(),
        },
        { new: true }
      )
      .populate('senderId', 'profile.firstName profile.lastName')
      .exec();

    return updatedNotification;
  }

  /**
   * 批量标记通知为已读
   */
  async markAllAsRead(userId: string): Promise<{ modifiedCount: number }> {
    const result = await this.notificationModel.updateMany(
      {
        userId: new Types.ObjectId(userId),
        isRead: false,
        isDeleted: false,
      },
      {
        isRead: true,
        readAt: new Date(),
      }
    );

    return { modifiedCount: result.modifiedCount };
  }

  /**
   * 删除通知
   */
  async remove(id: string, user: User): Promise<void> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid notification ID');
    }

    const notification = await this.notificationModel.findOne({
      _id: id,
      userId: user._id,
      isDeleted: false,
    });

    if (!notification) {
      throw new NotFoundException('Notification not found');
    }

    await this.notificationModel.findByIdAndUpdate(id, {
      isDeleted: true,
      deletedAt: new Date(),
    });
  }

  /**
   * 获取用户未读通知数量
   */
  async getUnreadCount(userId: string): Promise<number> {
    return this.notificationModel.countDocuments({
      userId: new Types.ObjectId(userId),
      isRead: false,
      isDeleted: false,
    });
  }

  /**
   * 获取用户通知统计
   */
  async getUserStats(userId: string): Promise<any> {
    const stats = await this.notificationModel.aggregate([
      {
        $match: {
          userId: new Types.ObjectId(userId),
          isDeleted: false,
        },
      },
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          unread: { $sum: { $cond: [{ $eq: ['$isRead', false] }, 1, 0] } },
          read: { $sum: { $cond: [{ $eq: ['$isRead', true] }, 1, 0] } },
          highPriority: { $sum: { $cond: [{ $eq: ['$priority', 'high'] }, 1, 0] } },
          urgentPriority: { $sum: { $cond: [{ $eq: ['$priority', 'urgent'] }, 1, 0] } },
        },
      },
    ]);

    return stats[0] || {
      total: 0,
      unread: 0,
      read: 0,
      highPriority: 0,
      urgentPriority: 0,
    };
  }
}
