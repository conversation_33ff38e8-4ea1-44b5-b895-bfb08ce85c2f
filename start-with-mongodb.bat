@echo off
echo ========================================
echo HomeReno 开发环境启动脚本 (MongoDB模式)
echo ========================================

echo.
echo 1. 设置环境变量...
set USE_MOCK_DATABASE=false
set NODE_ENV=development

echo   USE_MOCK_DATABASE=%USE_MOCK_DATABASE%
echo   NODE_ENV=%NODE_ENV%

echo.
echo 2. 检查配置文件...
if not exist ".env.development" (
    echo ❌ 未找到 .env.development 配置文件
    pause
    exit /b 1
)
echo ✅ 找到配置文件

echo.
echo 3. 创建必要的目录...
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads
if not exist "i18n" mkdir i18n

echo.
echo 4. 启动应用...
npm run start:dev

pause
