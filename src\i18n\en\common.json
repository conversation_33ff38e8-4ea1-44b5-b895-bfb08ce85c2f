{"success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "update": "Update", "search": "Search", "filter": "Filter", "sort": "Sort", "page": "Page", "of": "of", "total": "Total", "items": "items", "noData": "No data available", "confirm": "Confirm", "yes": "Yes", "no": "No", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "clear": "Clear", "close": "Close", "open": "Open", "view": "View", "download": "Download", "upload": "Upload", "required": "Required", "optional": "Optional", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "city": "City", "province": "Province", "postalCode": "Postal Code", "country": "Country", "date": "Date", "time": "Time", "status": "Status", "type": "Type", "category": "Category", "description": "Description", "title": "Title", "content": "Content", "price": "Price", "amount": "Amount", "quantity": "Quantity", "subtotal": "Subtotal", "tax": "Tax", "discount": "Discount", "currency": {"cad": "CAD", "usd": "USD"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "phone": "Please enter a valid phone number", "minLength": "Minimum length is {{min}} characters", "maxLength": "Maximum length is {{max}} characters", "min": "Minimum value is {{min}}", "max": "Maximum value is {{max}}", "pattern": "Invalid format", "unique": "This value already exists", "passwordMismatch": "Passwords do not match", "invalidCredentials": "Invalid email or password", "accountLocked": "Account is locked", "accountNotVerified": "Account is not verified"}, "errors": {"general": "An error occurred. Please try again.", "network": "Network error. Please check your connection.", "unauthorized": "You are not authorized to perform this action.", "forbidden": "Access denied.", "notFound": "Resource not found.", "conflict": "Conflict occurred.", "validation": "Validation failed.", "server": "Server error. Please try again later."}}