import { Injectable } from '@nestjs/common';
import { I18nService as NestI18nService } from 'nestjs-i18n';

export interface I18nTranslations {
  common: {
    success: string;
    error: string;
    warning: string;
    info: string;
    loading: string;
    save: string;
    cancel: string;
    delete: string;
    edit: string;
    create: string;
    update: string;
    search: string;
    filter: string;
    sort: string;
    page: string;
    of: string;
    total: string;
    items: string;
    noData: string;
    confirm: string;
    yes: string;
    no: string;
    back: string;
    next: string;
    previous: string;
    submit: string;
    reset: string;
    clear: string;
    close: string;
    open: string;
    view: string;
    download: string;
    upload: string;
    required: string;
    optional: string;
    name: string;
    email: string;
    phone: string;
    address: string;
    city: string;
    province: string;
    postalCode: string;
    country: string;
    date: string;
    time: string;
    status: string;
    type: string;
    category: string;
    description: string;
    title: string;
    content: string;
    price: string;
    amount: string;
    quantity: string;
    subtotal: string;
    tax: string;
    discount: string;
    currency: {
      cad: string;
      usd: string;
    };
    validation: {
      required: string;
      email: string;
      phone: string;
      minLength: string;
      maxLength: string;
      min: string;
      max: string;
      pattern: string;
      unique: string;
      passwordMismatch: string;
      invalidCredentials: string;
      accountLocked: string;
      accountNotVerified: string;
    };
    errors: {
      general: string;
      network: string;
      unauthorized: string;
      forbidden: string;
      notFound: string;
      conflict: string;
      validation: string;
      server: string;
    };
  };
  user: {
    profile: {
      title: string;
      firstName: string;
      lastName: string;
      avatar: string;
      bio: string;
      dateOfBirth: string;
      gender: string;
      language: string;
      timezone: string;
      updateProfile: string;
      profileUpdated: string;
    };
    account: {
      title: string;
      email: string;
      phone: string;
      password: string;
      currentPassword: string;
      newPassword: string;
      confirmPassword: string;
      changePassword: string;
      passwordChanged: string;
      emailVerification: string;
      phoneVerification: string;
      twoFactorAuth: string;
      enable: string;
      disable: string;
      verified: string;
      unverified: string;
    };
    address: {
      title: string;
      street: string;
      unit: string;
      city: string;
      province: string;
      postalCode: string;
      country: string;
      isPrimary: string;
      addAddress: string;
      editAddress: string;
      deleteAddress: string;
      addressAdded: string;
      addressUpdated: string;
      addressDeleted: string;
    };
    preferences: {
      title: string;
      notifications: string;
      emailNotifications: string;
      smsNotifications: string;
      pushNotifications: string;
      marketing: string;
      language: string;
      currency: string;
      timezone: string;
      theme: string;
      light: string;
      dark: string;
      auto: string;
    };
    types: {
      homeowner: string;
      serviceProvider: string;
      admin: string;
    };
    status: {
      active: string;
      inactive: string;
      suspended: string;
      pending: string;
    };
    actions: {
      register: string;
      login: string;
      logout: string;
      forgotPassword: string;
      resetPassword: string;
      verifyEmail: string;
      verifyPhone: string;
      resendVerification: string;
      deactivateAccount: string;
      deleteAccount: string;
    };
    messages: {
      registrationSuccess: string;
      loginSuccess: string;
      logoutSuccess: string;
      passwordResetSent: string;
      passwordResetSuccess: string;
      emailVerificationSent: string;
      emailVerified: string;
      phoneVerified: string;
      accountDeactivated: string;
      accountDeleted: string;
    };
  };
  project: any; // 项目相关翻译接口
}

@Injectable()
export class I18nService {
  constructor(private readonly i18n: NestI18nService<I18nTranslations>) {}

  translate(key: string, options?: any): string {
    return this.i18n.translate(key as any, options);
  }

  translateWithLang(key: string, lang: string, options?: any): string {
    return this.i18n.translate(key as any, { lang, ...options });
  }

  getSupportedLanguages(): string[] {
    return ['en', 'zh'];
  }

  getDefaultLanguage(): string {
    return 'en';
  }
}
