import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { DesignProject, DesignProjectDocument } from './schemas/design-project.schema';
import { DesignTemplate, DesignTemplateDocument } from './schemas/design-template.schema';
import { DesignAsset, DesignAssetDocument } from './schemas/design-asset.schema';
import { CreateDesignProjectDto } from './dto/create-design-project.dto';
import { UpdateDesignProjectDto } from './dto/update-design-project.dto';
import { QueryDesignProjectsDto } from './dto/query-design-projects.dto';
import { User } from '../users/schemas/user.schema';
import * as crypto from 'crypto';

@Injectable()
export class DesignToolsService {
  constructor(
    @InjectModel(DesignProject.name)
    private designProjectModel: Model<DesignProjectDocument>,
    @InjectModel(DesignTemplate.name)
    private designTemplateModel: Model<DesignTemplateDocument>,
    @InjectModel(DesignAsset.name)
    private designAssetModel: Model<DesignAssetDocument>,
  ) {}

  // 设计项目相关方法
  async createProject(createProjectDto: CreateDesignProjectDto, user: User): Promise<DesignProject> {
    const projectData = {
      ...createProjectDto,
      userId: user._id,
    };

    const project = new this.designProjectModel(projectData);
    return project.save();
  }

  async findAllProjects(query: QueryDesignProjectsDto, user?: User): Promise<{
    projects: DesignProject[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc', ...filters } = query;
    
    // 构建查询条件
    const matchConditions: any = {};
    
    if (filters.houseType) {
      matchConditions.houseType = filters.houseType;
    }
    
    if (filters.style) {
      matchConditions.style = filters.style;
    }
    
    if (filters.status) {
      matchConditions.status = filters.status;
    }
    
    if (filters.search) {
      matchConditions.$or = [
        { name: { $regex: filters.search, $options: 'i' } },
        { description: { $regex: filters.search, $options: 'i' } }
      ];
    }
    
    // 权限控制
    if (filters.publicOnly || !user) {
      matchConditions.isPublic = true;
    } else {
      matchConditions.$or = [
        { userId: user._id },
        { isPublic: true }
      ];
    }

    // 排序
    const sortOptions: any = {};
    sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [projects, total] = await Promise.all([
      this.designProjectModel
        .find(matchConditions)
        .sort(sortOptions)
        .skip(skip)
        .limit(limit)
        .populate('userId', 'profile.firstName profile.lastName')
        .populate('assets', 'name thumbnailUrl type category')
        .exec(),
      this.designProjectModel.countDocuments(matchConditions)
    ]);

    return {
      projects,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  async findProjectById(id: string, user?: User): Promise<DesignProject> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid project ID');
    }

    const project = await this.designProjectModel
      .findById(id)
      .populate('userId', 'profile.firstName profile.lastName')
      .populate('assets')
      .exec();

    if (!project) {
      throw new NotFoundException('Design project not found');
    }

    // 权限检查
    if (!project.isPublic && (!user || project.userId.toString() !== user._id.toString())) {
      throw new ForbiddenException('Access denied to this project');
    }

    return project;
  }

  async updateProject(id: string, updateProjectDto: UpdateDesignProjectDto, user: User): Promise<DesignProject> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid project ID');
    }

    const project = await this.designProjectModel.findById(id);
    
    if (!project) {
      throw new NotFoundException('Design project not found');
    }

    if (project.userId.toString() !== user._id.toString()) {
      throw new ForbiddenException('You can only update your own projects');
    }

    const updatedProject = await this.designProjectModel
      .findByIdAndUpdate(id, updateProjectDto, { new: true })
      .populate('userId', 'profile.firstName profile.lastName')
      .populate('assets')
      .exec();

    return updatedProject;
  }

  async deleteProject(id: string, user: User): Promise<void> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid project ID');
    }

    const project = await this.designProjectModel.findById(id);
    
    if (!project) {
      throw new NotFoundException('Design project not found');
    }

    if (project.userId.toString() !== user._id.toString()) {
      throw new ForbiddenException('You can only delete your own projects');
    }

    await this.designProjectModel.findByIdAndDelete(id);
  }

  async duplicateProject(id: string, user: User): Promise<DesignProject> {
    const originalProject = await this.findProjectById(id, user);

    const duplicateData = {
      name: `${originalProject.name} (Copy)`,
      description: originalProject.description,
      projectId: originalProject.projectId,
      houseType: originalProject.houseType,
      area: originalProject.area,
      rooms: originalProject.rooms,
      style: originalProject.style,
      budget: originalProject.budget,
      sceneData: originalProject.sceneData,
      assets: originalProject.assets,
      userId: user._id,
      status: 'draft',
      isPublic: false
    };

    const duplicatedProject = new this.designProjectModel(duplicateData);
    return duplicatedProject.save();
  }

  // 分享功能
  async shareProject(id: string, user: User, expiresInDays: number = 30): Promise<{ shareToken: string; shareUrl: string }> {
    const project = await this.designProjectModel.findById(id);
    
    if (!project) {
      throw new NotFoundException('Design project not found');
    }

    if (project.userId.toString() !== user._id.toString()) {
      throw new ForbiddenException('You can only share your own projects');
    }

    const shareToken = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + expiresInDays);

    await this.designProjectModel.findByIdAndUpdate(id, {
      shareSettings: {
        shareToken,
        expiresAt,
        allowComments: false
      }
    });

    return {
      shareToken,
      shareUrl: `/design/shared/${shareToken}`
    };
  }

  async getSharedProject(shareToken: string): Promise<DesignProject> {
    const project = await this.designProjectModel
      .findOne({ 'shareSettings.shareToken': shareToken })
      .populate('userId', 'profile.firstName profile.lastName')
      .populate('assets')
      .exec();

    if (!project) {
      throw new NotFoundException('Shared project not found');
    }

    if (project.shareSettings?.expiresAt && project.shareSettings.expiresAt < new Date()) {
      throw new ForbiddenException('Share link has expired');
    }

    return project;
  }

  // 模板相关方法
  async findAllTemplates(filters: any = {}): Promise<DesignTemplate[]> {
    const matchConditions: any = { status: 'published' };
    
    if (filters.category) {
      matchConditions.category = filters.category;
    }
    
    if (filters.style) {
      matchConditions.style = filters.style;
    }
    
    if (filters.houseTypes) {
      matchConditions.houseTypes = { $in: filters.houseTypes };
    }
    
    if (filters.roomTypes) {
      matchConditions.roomTypes = { $in: filters.roomTypes };
    }

    return this.designTemplateModel
      .find(matchConditions)
      .sort({ isOfficial: -1, 'rating.average': -1, usageCount: -1 })
      .exec();
  }

  async findTemplateById(id: string): Promise<DesignTemplate> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid template ID');
    }

    const template = await this.designTemplateModel.findById(id);
    
    if (!template || template.status !== 'published') {
      throw new NotFoundException('Template not found');
    }

    // 增加使用次数
    await this.designTemplateModel.findByIdAndUpdate(id, { $inc: { usageCount: 1 } });

    return template;
  }

  // 资产相关方法
  async findAllAssets(filters: any = {}): Promise<DesignAsset[]> {
    const matchConditions: any = { status: 'approved' };
    
    if (filters.type) {
      matchConditions.type = filters.type;
    }
    
    if (filters.category) {
      matchConditions.category = filters.category;
    }
    
    if (filters.suitableRooms) {
      matchConditions.suitableRooms = { $in: filters.suitableRooms };
    }
    
    if (filters.styles) {
      matchConditions.styles = { $in: filters.styles };
    }
    
    if (filters.isFree !== undefined) {
      matchConditions.isFree = filters.isFree;
    }

    return this.designAssetModel
      .find(matchConditions)
      .sort({ isOfficial: -1, 'rating.average': -1, usageCount: -1 })
      .exec();
  }

  async findAssetById(id: string): Promise<DesignAsset> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid asset ID');
    }

    const asset = await this.designAssetModel.findById(id);
    
    if (!asset || asset.status !== 'approved') {
      throw new NotFoundException('Asset not found');
    }

    // 增加使用次数
    await this.designAssetModel.findByIdAndUpdate(id, { $inc: { usageCount: 1 } });

    return asset;
  }
}
