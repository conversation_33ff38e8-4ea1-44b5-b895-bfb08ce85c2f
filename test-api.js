const http = require('http');

function testAPI() {
  console.log('🧪 测试3D设计工具API端点...');
  
  // 测试获取设计模板
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/v1/design-tools/templates',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  const req = http.request(options, (res) => {
    let data = '';
    
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      if (res.statusCode === 200) {
        try {
          const result = JSON.parse(data);
          console.log('✅ 设计模板API测试成功!');
          console.log('📋 响应数据结构:', JSON.stringify(result, null, 2));

          if (result.data && Array.isArray(result.data)) {
            console.log(`📐 找到 ${result.data.length} 个设计模板:`);
            result.data.forEach(template => {
              console.log(`   - ${template.name} (${template.style}风格)`);
            });
          } else if (Array.isArray(result)) {
            console.log(`📐 找到 ${result.length} 个设计模板:`);
            result.forEach(template => {
              console.log(`   - ${template.name} (${template.style}风格)`);
            });
          } else {
            console.log('📋 响应格式:', typeof result);
          }

          // 测试设计资产API
          testAssetsAPI();
        } catch (error) {
          console.error('❌ 解析响应失败:', error.message);
          console.log('📋 原始响应:', data);
        }
      } else {
        console.error(`❌ API请求失败: ${res.statusCode}`);
        console.error('响应:', data);
      }
    });
  });
  
  req.on('error', (error) => {
    console.error('❌ 连接失败:', error.message);
    console.log('💡 请确保应用程序正在运行在端口3000');
  });
  
  req.end();
}

function testAssetsAPI() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/v1/design-tools/assets',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  const req = http.request(options, (res) => {
    let data = '';
    
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      if (res.statusCode === 200) {
        try {
          const result = JSON.parse(data);
          console.log('\n✅ 设计资产API测试成功!');
          console.log(`🎨 找到 ${result.data.length} 个设计资产:`);
          result.data.forEach(asset => {
            console.log(`   - ${asset.name} (${asset.type}类型)`);
          });
          
          console.log('\n🎉 3D设计工具API测试完成!');
          console.log('\n📊 测试结果总结:');
          console.log('   ✅ 设计模板API: 正常工作');
          console.log('   ✅ 设计资产API: 正常工作');
          console.log('   ✅ 数据库连接: 正常');
          console.log('   ✅ 种子数据: 已加载');
          
        } catch (error) {
          console.error('❌ 解析响应失败:', error.message);
        }
      } else {
        console.error(`❌ API请求失败: ${res.statusCode}`);
        console.error('响应:', data);
      }
    });
  });
  
  req.on('error', (error) => {
    console.error('❌ 连接失败:', error.message);
  });
  
  req.end();
}

// 等待2秒后开始测试，给应用程序启动时间
setTimeout(testAPI, 2000);
