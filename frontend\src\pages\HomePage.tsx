import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { 
  Typography, 
  Button, 
  Row, 
  Col, 
  Card, 
  Space,
  Statistic,
  Divider 
} from 'antd';
import {
  ProjectOutlined,
  TeamOutlined,
  SafetyOutlined,
  StarOutlined,
  ArrowRightOutlined,
} from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const HomePage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();

  const features = [
    {
      icon: <TeamOutlined style={{ fontSize: 48, color: '#1890ff' }} />,
      title: t('contractors.verified'),
      description: i18n.language === 'zh'
        ? '所有装修师傅都经过严格认证，确保专业可靠'
        : 'All contractors are thoroughly verified for professionalism and reliability',
    },
    {
      icon: <ProjectOutlined style={{ fontSize: 48, color: '#52c41a' }} />,
      title: '3D ' + (i18n.language === 'zh' ? '设计工具' : 'Design Tools'),
      description: i18n.language === 'zh'
        ? '使用先进的3D工具可视化您的装修项目'
        : 'Visualize your renovation projects with advanced 3D design tools',
    },
    {
      icon: <SafetyOutlined style={{ fontSize: 48, color: '#faad14' }} />,
      title: i18n.language === 'zh' ? '安全保障' : 'Secure Protection',
      description: i18n.language === 'zh'
        ? '提供全面的保险和支付保护，让您安心装修'
        : 'Comprehensive insurance and payment protection for peace of mind',
    },
    {
      icon: <StarOutlined style={{ fontSize: 48, color: '#eb2f96' }} />,
      title: i18n.language === 'zh' ? '质量保证' : 'Quality Guarantee',
      description: i18n.language === 'zh'
        ? '严格的质量标准和客户评价系统确保服务质量'
        : 'Strict quality standards and customer review system ensure service excellence',
    },
  ];

  const stats = [
    {
      title: i18n.language === 'zh' ? '认证师傅' : 'Verified Contractors',
      value: 1200,
      suffix: '+',
    },
    {
      title: i18n.language === 'zh' ? '完成项目' : 'Completed Projects',
      value: 5600,
      suffix: '+',
    },
    {
      title: i18n.language === 'zh' ? '满意客户' : 'Happy Customers',
      value: 4800,
      suffix: '+',
    },
    {
      title: i18n.language === 'zh' ? '平均评分' : 'Average Rating',
      value: 4.8,
      precision: 1,
      suffix: '/5',
    },
  ];

  return (
    <div>
      {/* Hero Section */}
      <div style={{ 
        textAlign: 'center', 
        padding: '80px 0',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        borderRadius: '12px',
        marginBottom: '60px'
      }}>
        <Title level={1} style={{ color: 'white', fontSize: '3.5rem', marginBottom: 24 }}>
          {i18n.language === 'zh' ? '让装修变得简单' : 'Make Renovation Simple'}
        </Title>
        <Paragraph style={{ 
          fontSize: '1.3rem', 
          color: 'rgba(255,255,255,0.9)',
          maxWidth: 600,
          margin: '0 auto 40px'
        }}>
          {i18n.language === 'zh' 
            ? '连接房主与专业装修师傅，提供从设计到施工的一站式服务'
            : 'Connect homeowners with professional contractors for complete renovation services from design to construction'
          }
        </Paragraph>
        <Space size="large">
          <Button 
            type="primary" 
            size="large"
            onClick={() => navigate('/projects')}
            style={{ height: 50, fontSize: '1.1rem', padding: '0 30px' }}
          >
            {i18n.language === 'zh' ? '浏览项目' : 'Browse Projects'}
            <ArrowRightOutlined />
          </Button>
          <Button 
            size="large"
            onClick={() => navigate('/contractors')}
            style={{ 
              height: 50, 
              fontSize: '1.1rem', 
              padding: '0 30px',
              background: 'rgba(255,255,255,0.2)',
              borderColor: 'rgba(255,255,255,0.4)',
              color: 'white'
            }}
          >
            {t('contractors.findContractors')}
          </Button>
        </Space>
      </div>

      {/* Stats Section */}
      <Row gutter={[32, 32]} style={{ marginBottom: '80px' }}>
        {stats.map((stat, index) => (
          <Col xs={12} sm={6} key={index}>
            <Card style={{ textAlign: 'center', height: '100%' }}>
              <Statistic
                title={stat.title}
                value={stat.value}
                precision={stat.precision}
                suffix={stat.suffix}
                valueStyle={{ color: '#1890ff', fontSize: '2rem' }}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* Features Section */}
      <div style={{ marginBottom: '80px' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: 60 }}>
          {i18n.language === 'zh' ? '为什么选择 HomeReno？' : 'Why Choose HomeReno?'}
        </Title>
        <Row gutter={[32, 32]}>
          {features.map((feature, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <Card 
                style={{ 
                  height: '100%', 
                  textAlign: 'center',
                  transition: 'transform 0.3s ease',
                }}
                hoverable
              >
                <div style={{ marginBottom: 24 }}>
                  {feature.icon}
                </div>
                <Title level={4} style={{ marginBottom: 16 }}>
                  {feature.title}
                </Title>
                <Paragraph style={{ color: '#666' }}>
                  {feature.description}
                </Paragraph>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* CTA Section */}
      <Card style={{ 
        textAlign: 'center', 
        background: '#f8f9fa',
        border: 'none',
        padding: '40px 20px'
      }}>
        <Title level={3} style={{ marginBottom: 16 }}>
          {i18n.language === 'zh' ? '准备开始您的装修项目？' : 'Ready to Start Your Renovation Project?'}
        </Title>
        <Paragraph style={{ fontSize: '1.1rem', color: '#666', marginBottom: 32 }}>
          {i18n.language === 'zh'
            ? '立即注册，发布您的项目需求，获取专业报价'
            : 'Register now, post your project requirements, and get professional quotes'
          }
        </Paragraph>
        <Space size="large">
          <Button 
            type="primary" 
            size="large"
            onClick={() => navigate('/auth/register')}
          >
            {t('navigation.register')}
          </Button>
          <Button 
            size="large"
            onClick={() => navigate('/auth/login')}
          >
            {t('navigation.login')}
          </Button>
        </Space>
      </Card>
    </div>
  );
};

export default HomePage;
