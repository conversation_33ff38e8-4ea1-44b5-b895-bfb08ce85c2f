import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Typography, 
  Button, 
  Row, 
  Col, 
  Card, 
  Space,
  Statistic
} from 'antd';
import {
  ProjectOutlined,
  TeamOutlined,
  SafetyOutlined,
  StarOutlined,
  HomeOutlined,
  ToolOutlined,
  BgColorsOutlined,
  BuildOutlined,
} from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const HomePage: React.FC = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: <TeamOutlined style={{ fontSize: 48, color: '#1890ff' }} />,
      title: 'Certified Professionals',
      description: 'Connect with licensed contractors, architects, and designers across Canada',
    },
    {
      icon: <ProjectOutlined style={{ fontSize: 48, color: '#52c41a' }} />,
      title: '3D Visualization',
      description: 'See your dream home come to life with advanced 3D design and modeling tools',
    },
    {
      icon: <SafetyOutlined style={{ fontSize: 48, color: '#faad14' }} />,
      title: 'Secure Payments',
      description: 'Protected milestone payments with escrow service for your peace of mind',
    },
    {
      icon: <StarOutlined style={{ fontSize: 48, color: '#eb2f96' }} />,
      title: 'Quality Assurance',
      description: 'Every project backed by our quality guarantee and customer review system',
    },
  ];

  const stats = [
    {
      title: 'Certified Contractors',
      value: 2800,
      suffix: '+',
    },
    {
      title: 'Projects Completed',
      value: 12500,
      suffix: '+',
    },
    {
      title: 'Canadian Cities',
      value: 150,
      suffix: '+',
    },
    {
      title: 'Customer Satisfaction',
      value: 98,
      precision: 0,
      suffix: '%',
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* Hero Section */}
      <div style={{
        textAlign: 'center',
        padding: '60px 24px',
        background: '#1890ff',
        color: 'white',
        borderRadius: '8px',
        marginBottom: '40px'
      }}>
        <Title level={1} style={{ color: 'white', fontSize: '2.5rem', marginBottom: 16 }}>
          Transform Your Home with Canada's Top Renovation Platform
        </Title>
        <Paragraph style={{
          fontSize: '1.1rem',
          color: 'white',
          maxWidth: 600,
          margin: '0 auto 32px'
        }}>
          From kitchen makeovers to full home renovations, connect with certified professionals across Canada.
        </Paragraph>
        <Space size="large">
          <Button
            type="primary"
            size="large"
            onClick={() => navigate('/projects/create')}
            style={{ backgroundColor: 'white', color: '#1890ff', border: 'none' }}
          >
            Start Your Project
          </Button>
          <Button
            size="large"
            onClick={() => navigate('/contractors')}
            style={{
              backgroundColor: 'transparent',
              borderColor: 'white',
              color: 'white'
            }}
          >
            Find Professionals
          </Button>
        </Space>
      </div>

      {/* Statistics Section */}
      <Row gutter={[16, 16]} style={{ marginBottom: '40px' }}>
        {stats.map((stat, index) => (
          <Col xs={12} sm={6} key={index}>
            <Card style={{ textAlign: 'center' }}>
              <Statistic
                title={stat.title}
                value={stat.value}
                precision={stat.precision}
                suffix={stat.suffix}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* Features Section */}
      <div style={{ marginBottom: '40px' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: '32px' }}>
          Why Choose HomeReno?
        </Title>
        <Row gutter={[16, 16]}>
          {features.map((feature, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <Card style={{ textAlign: 'center', height: '100%' }}>
                <div style={{ marginBottom: '16px' }}>
                  {feature.icon}
                </div>
                <Title level={4} style={{ marginBottom: '8px' }}>
                  {feature.title}
                </Title>
                <Paragraph style={{ color: '#666' }}>
                  {feature.description}
                </Paragraph>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* Project Types Section */}
      <div style={{ marginBottom: '40px' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: '32px' }}>
          Popular Renovation Projects
        </Title>
        <Row gutter={[16, 16]}>
          <Col xs={12} sm={6}>
            <Card style={{ textAlign: 'center' }}>
              <HomeOutlined style={{ fontSize: 32, color: '#1890ff', marginBottom: 8 }} />
              <Title level={4}>Kitchen</Title>
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card style={{ textAlign: 'center' }}>
              <ToolOutlined style={{ fontSize: 32, color: '#52c41a', marginBottom: 8 }} />
              <Title level={4}>Bathroom</Title>
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card style={{ textAlign: 'center' }}>
              <BgColorsOutlined style={{ fontSize: 32, color: '#faad14', marginBottom: 8 }} />
              <Title level={4}>Interior</Title>
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card style={{ textAlign: 'center' }}>
              <BuildOutlined style={{ fontSize: 32, color: '#eb2f96', marginBottom: 8 }} />
              <Title level={4}>Addition</Title>
            </Card>
          </Col>
        </Row>
      </div>

      {/* Call to Action Section */}
      <div style={{
        textAlign: 'center',
        padding: '40px 24px',
        background: '#f5f5f5',
        borderRadius: '8px'
      }}>
        <Title level={2} style={{ marginBottom: '16px' }}>
          Ready to Transform Your Home?
        </Title>
        <Paragraph style={{ color: '#666', marginBottom: '24px' }}>
          Join thousands of Canadian homeowners who trust HomeReno
        </Paragraph>
        <Space>
          <Button
            type="primary"
            size="large"
            onClick={() => navigate('/register')}
          >
            Start Free Project
          </Button>
          <Button
            size="large"
            onClick={() => navigate('/contractors')}
          >
            Browse Professionals
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default HomePage;
