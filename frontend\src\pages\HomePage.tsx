import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Typography, 
  Button, 
  Row, 
  Col, 
  Card, 
  Space,
  Statistic
} from 'antd';
import {
  ProjectOutlined,
  TeamOutlined,
  SafetyOutlined,
  StarOutlined,
  ArrowRightOutlined,
  HomeOutlined,
  ToolOutlined,
  BgColorsOutlined,
  BuildOutlined,
} from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const HomePage: React.FC = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: <TeamOutlined style={{ fontSize: 48, color: '#1890ff' }} />,
      title: 'Certified Professionals',
      description: 'Connect with licensed contractors, architects, and designers across Canada',
    },
    {
      icon: <ProjectOutlined style={{ fontSize: 48, color: '#52c41a' }} />,
      title: '3D Visualization',
      description: 'See your dream home come to life with advanced 3D design and modeling tools',
    },
    {
      icon: <SafetyOutlined style={{ fontSize: 48, color: '#faad14' }} />,
      title: 'Secure Payments',
      description: 'Protected milestone payments with escrow service for your peace of mind',
    },
    {
      icon: <StarOutlined style={{ fontSize: 48, color: '#eb2f96' }} />,
      title: 'Quality Assurance',
      description: 'Every project backed by our quality guarantee and customer review system',
    },
  ];

  const stats = [
    {
      title: 'Certified Contractors',
      value: 2800,
      suffix: '+',
    },
    {
      title: 'Projects Completed',
      value: 12500,
      suffix: '+',
    },
    {
      title: 'Canadian Cities',
      value: 150,
      suffix: '+',
    },
    {
      title: 'Customer Satisfaction',
      value: 98,
      precision: 0,
      suffix: '%',
    },
  ];

  return (
    <div>
      {/* Hero Section */}
      <div style={{ 
        textAlign: 'center', 
        padding: '80px 0',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        borderRadius: '12px',
        marginBottom: '60px'
      }}>
        <Title level={1} style={{ color: 'white', fontSize: '3.5rem', marginBottom: 24 }}>
          Transform Your Home with Canada's Top Renovation Platform
        </Title>
        <Paragraph style={{
          fontSize: '1.3rem',
          color: 'rgba(255,255,255,0.9)',
          maxWidth: 700,
          margin: '0 auto 40px'
        }}>
          From kitchen makeovers to full home renovations, connect with certified professionals across Canada. Get quotes, visualize your project in 3D, and bring your dream home to life.
        </Paragraph>
        <Space size="large">
          <Button
            type="primary"
            size="large"
            onClick={() => navigate('/projects/create')}
            style={{ height: 50, fontSize: '1.1rem', padding: '0 30px' }}
          >
            Start Your Project
            <ArrowRightOutlined />
          </Button>
          <Button
            size="large"
            onClick={() => navigate('/contractors')}
            style={{
              height: 50,
              fontSize: '1.1rem',
              padding: '0 30px',
              background: 'rgba(255,255,255,0.2)',
              borderColor: 'rgba(255,255,255,0.4)',
              color: 'white'
            }}
          >
            Find Professionals
          </Button>
        </Space>
      </div>

      {/* Statistics Section */}
      <Row gutter={[32, 32]} style={{ marginBottom: '80px' }}>
        {stats.map((stat, index) => (
          <Col xs={12} sm={6} key={index}>
            <Card style={{ textAlign: 'center', height: '100%' }}>
              <Statistic
                title={stat.title}
                value={stat.value}
                precision={stat.precision}
                suffix={stat.suffix}
                valueStyle={{ color: '#1890ff', fontSize: '2rem' }}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* Features Section */}
      <div style={{ marginBottom: '80px' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: '60px' }}>
          Why HomeReno is Canada's #1 Renovation Platform
        </Title>
        <Row gutter={[32, 32]}>
          {features.map((feature, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <Card
                style={{
                  textAlign: 'center',
                  height: '100%',
                  borderRadius: '12px',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                }}
                styles={{ body: { padding: '40px 24px' } }}
              >
                <div style={{ marginBottom: '24px' }}>
                  {feature.icon}
                </div>
                <Title level={4} style={{ marginBottom: '16px' }}>
                  {feature.title}
                </Title>
                <Paragraph style={{ color: '#666', lineHeight: 1.6 }}>
                  {feature.description}
                </Paragraph>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* Project Types Section */}
      <div style={{ marginBottom: '80px' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: '60px' }}>
          Popular Renovation Projects
        </Title>
        <Row gutter={[24, 24]}>
          <Col xs={24} sm={12} lg={6}>
            <Card
              hoverable
              style={{ textAlign: 'center', height: '100%' }}
              cover={
                <div style={{
                  height: 120,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <HomeOutlined style={{ fontSize: 48, color: 'white' }} />
                </div>
              }
            >
              <Title level={4}>Kitchen Renovation</Title>
              <Paragraph style={{ color: '#666' }}>
                Transform your kitchen with modern designs and professional installation
              </Paragraph>
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card
              hoverable
              style={{ textAlign: 'center', height: '100%' }}
              cover={
                <div style={{
                  height: 120,
                  background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <ToolOutlined style={{ fontSize: 48, color: 'white' }} />
                </div>
              }
            >
              <Title level={4}>Bathroom Remodel</Title>
              <Paragraph style={{ color: '#666' }}>
                Create your perfect bathroom oasis with expert plumbing and design
              </Paragraph>
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card
              hoverable
              style={{ textAlign: 'center', height: '100%' }}
              cover={
                <div style={{
                  height: 120,
                  background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <BgColorsOutlined style={{ fontSize: 48, color: 'white' }} />
                </div>
              }
            >
              <Title level={4}>Interior Design</Title>
              <Paragraph style={{ color: '#666' }}>
                Complete interior makeovers with professional designers and decorators
              </Paragraph>
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card
              hoverable
              style={{ textAlign: 'center', height: '100%' }}
              cover={
                <div style={{
                  height: 120,
                  background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <BuildOutlined style={{ fontSize: 48, color: 'white' }} />
                </div>
              }
            >
              <Title level={4}>Home Addition</Title>
              <Paragraph style={{ color: '#666' }}>
                Expand your living space with professional construction services
              </Paragraph>
            </Card>
          </Col>
        </Row>
      </div>

      {/* Call to Action Section */}
      <div style={{
        textAlign: 'center',
        padding: '60px 0',
        background: '#f8f9fa',
        borderRadius: '12px'
      }}>
        <Title level={2} style={{ marginBottom: '24px' }}>
          Ready to Transform Your Home?
        </Title>
        <Paragraph style={{ fontSize: '1.1rem', color: '#666', marginBottom: '40px' }}>
          Join over 12,500 Canadian homeowners who have successfully completed their renovation projects with HomeReno
        </Paragraph>
        <Space size="large">
          <Button
            type="primary"
            size="large"
            onClick={() => navigate('/register')}
            style={{ height: 50, fontSize: '1.1rem', padding: '0 40px' }}
          >
            Start Free Project
          </Button>
          <Button
            size="large"
            onClick={() => navigate('/contractors')}
            style={{ height: 50, fontSize: '1.1rem', padding: '0 40px' }}
          >
            Browse Professionals
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default HomePage;
