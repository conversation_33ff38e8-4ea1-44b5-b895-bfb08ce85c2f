import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Typography, 
  Button, 
  Row, 
  Col, 
  Card, 
  Space,
  Statistic
} from 'antd';
import {
  ProjectOutlined,
  TeamOutlined,
  SafetyOutlined,
  StarOutlined,
  ArrowRightOutlined,
} from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const HomePage: React.FC = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: <TeamOutlined style={{ fontSize: 48, color: '#1890ff' }} />,
      title: 'Verified Contractors',
      description: 'All contractors are thoroughly verified for professionalism and reliability',
    },
    {
      icon: <ProjectOutlined style={{ fontSize: 48, color: '#52c41a' }} />,
      title: '3D Design Tools',
      description: 'Visualize your renovation projects with advanced 3D design tools',
    },
    {
      icon: <SafetyOutlined style={{ fontSize: 48, color: '#faad14' }} />,
      title: 'Secure Protection',
      description: 'Comprehensive insurance and payment protection for peace of mind',
    },
    {
      icon: <StarOutlined style={{ fontSize: 48, color: '#eb2f96' }} />,
      title: 'Quality Guarantee',
      description: 'Strict quality standards and customer review system ensure service excellence',
    },
  ];

  const stats = [
    {
      title: 'Verified Contractors',
      value: 1200,
      suffix: '+',
    },
    {
      title: 'Completed Projects',
      value: 5600,
      suffix: '+',
    },
    {
      title: 'Happy Customers',
      value: 4800,
      suffix: '+',
    },
    {
      title: 'Average Rating',
      value: 4.8,
      precision: 1,
      suffix: '/5',
    },
  ];

  return (
    <div>
      {/* Hero Section */}
      <div style={{ 
        textAlign: 'center', 
        padding: '80px 0',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        borderRadius: '12px',
        marginBottom: '60px'
      }}>
        <Title level={1} style={{ color: 'white', fontSize: '3.5rem', marginBottom: 24 }}>
          Make Renovation Simple
        </Title>
        <Paragraph style={{ 
          fontSize: '1.3rem', 
          color: 'rgba(255,255,255,0.9)',
          maxWidth: 600,
          margin: '0 auto 40px'
        }}>
          Connect homeowners with professional contractors for complete renovation services from design to construction
        </Paragraph>
        <Space size="large">
          <Button 
            type="primary" 
            size="large"
            onClick={() => navigate('/projects')}
            style={{ height: 50, fontSize: '1.1rem', padding: '0 30px' }}
          >
            Browse Projects
            <ArrowRightOutlined />
          </Button>
          <Button 
            size="large"
            onClick={() => navigate('/contractors')}
            style={{ 
              height: 50, 
              fontSize: '1.1rem', 
              padding: '0 30px',
              background: 'rgba(255,255,255,0.2)',
              borderColor: 'rgba(255,255,255,0.4)',
              color: 'white'
            }}
          >
            Find Contractors
          </Button>
        </Space>
      </div>

      {/* Statistics Section */}
      <Row gutter={[32, 32]} style={{ marginBottom: '80px' }}>
        {stats.map((stat, index) => (
          <Col xs={12} sm={6} key={index}>
            <Card style={{ textAlign: 'center', height: '100%' }}>
              <Statistic
                title={stat.title}
                value={stat.value}
                precision={stat.precision}
                suffix={stat.suffix}
                valueStyle={{ color: '#1890ff', fontSize: '2rem' }}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* Features Section */}
      <div style={{ marginBottom: '80px' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: '60px' }}>
          Why Choose HomeReno?
        </Title>
        <Row gutter={[32, 32]}>
          {features.map((feature, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <Card 
                style={{ 
                  textAlign: 'center', 
                  height: '100%',
                  borderRadius: '12px',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                }}
                bodyStyle={{ padding: '40px 24px' }}
              >
                <div style={{ marginBottom: '24px' }}>
                  {feature.icon}
                </div>
                <Title level={4} style={{ marginBottom: '16px' }}>
                  {feature.title}
                </Title>
                <Paragraph style={{ color: '#666', lineHeight: 1.6 }}>
                  {feature.description}
                </Paragraph>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* Call to Action Section */}
      <div style={{
        textAlign: 'center',
        padding: '60px 0',
        background: '#f8f9fa',
        borderRadius: '12px'
      }}>
        <Title level={2} style={{ marginBottom: '24px' }}>
          Ready to Start Your Renovation?
        </Title>
        <Paragraph style={{ fontSize: '1.1rem', color: '#666', marginBottom: '40px' }}>
          Join thousands of satisfied homeowners who found their perfect contractors through HomeReno
        </Paragraph>
        <Space size="large">
          <Button 
            type="primary" 
            size="large"
            onClick={() => navigate('/register')}
            style={{ height: 50, fontSize: '1.1rem', padding: '0 40px' }}
          >
            Get Started Today
          </Button>
          <Button 
            size="large"
            onClick={() => navigate('/contractors')}
            style={{ height: 50, fontSize: '1.1rem', padding: '0 40px' }}
          >
            Browse Contractors
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default HomePage;
