import { NestFactory } from '@nestjs/core';
import { AppModule } from '../../../app.module';
import { DesignToolsService } from '../design-tools.service';

async function testDatabaseConnection() {
  console.log('🔍 测试数据库连接...');
  
  try {
    const app = await NestFactory.createApplicationContext(AppModule);
    const designToolsService = app.get(DesignToolsService);
    
    // 尝试获取现有模板
    console.log('📊 获取现有模板...');
    const templates = await designToolsService.findAllTemplates({});
    console.log(`✅ 成功获取 ${templates.length} 个模板`);

    // 尝试创建一个简单的项目来测试写入权限
    console.log('📝 尝试创建测试项目...');
    const testUser = { _id: '507f1f77bcf86cd799439011' } as any; // 模拟用户ID
    const testProject = {
      name: 'Test Project',
      description: 'A test project for database connection',
      houseType: 'apartment',
      area: 1000,
      budget: {
        min: 10000,
        max: 50000,
        currency: 'CAD'
      },
      sceneData: {
        camera: {
          position: [0, 5, 10],
          target: [0, 0, 0],
          fov: 75
        },
        lighting: {
          ambient: 0.4,
          directional: {
            intensity: 1,
            position: [10, 10, 5]
          }
        }
      },
      assets: []
    };

    const createdProject = await designToolsService.createProject(testProject, testUser);
    console.log('✅ 成功创建测试项目:', createdProject._id);

    // 删除测试项目
    await designToolsService.deleteProject(createdProject._id.toString(), testUser);
    console.log('✅ 成功删除测试项目');
    
    await app.close();
    console.log('🎉 数据库连接测试成功！');
    
  } catch (error) {
    console.error('❌ 数据库连接测试失败:', error.message);
    process.exit(1);
  }
}

testDatabaseConnection();
