import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  Review,
  ReviewDocument,
  ReviewStatus,
  ReviewType,
} from './schemas/review.schema';
import {
  CreateReviewDto,
  UpdateReviewDto,
  ReviewQueryDto,
  CreateReviewResponseDto,
  ReviewModerationDto,
} from './dto/create-review.dto';
import { PaginatedResult } from '../../common/interfaces/paginated-result.interface';
import { User } from '../users/schemas/user.schema';

@Injectable()
export class ReviewsService {
  constructor(
    @InjectModel(Review.name) private reviewModel: Model<ReviewDocument>,
  ) {}

  /**
   * 创建评价
   */
  async create(createReviewDto: CreateReviewDto, user: User): Promise<Review> {
    // 检查是否已经评价过
    if (createReviewDto.projectId) {
      const existingReview = await this.reviewModel.findOne({
        reviewerId: user._id,
        revieweeId: createReviewDto.revieweeId,
        projectId: createReviewDto.projectId,
        isDeleted: false,
      });

      if (existingReview) {
        throw new ConflictException('You have already reviewed this project');
      }
    }

    const reviewData = {
      ...createReviewDto,
      reviewerId: user._id,
      revieweeId: new Types.ObjectId(createReviewDto.revieweeId),
      projectId: createReviewDto.projectId ? new Types.ObjectId(createReviewDto.projectId) : undefined,
      serviceProviderId: createReviewDto.serviceProviderId ? new Types.ObjectId(createReviewDto.serviceProviderId) : undefined,
    };

    const createdReview = new this.reviewModel(reviewData);
    return createdReview.save();
  }

  /**
   * 获取评价列表
   */
  async findAll(query: ReviewQueryDto): Promise<PaginatedResult<Review>> {
    const {
      page = 1,
      limit = 20,
      type,
      status = ReviewStatus.APPROVED,
      revieweeId,
      projectId,
      serviceProviderId,
      minRating,
      maxRating,
      isRecommended,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = query;

    const filter: any = {
      isDeleted: false,
      status,
    };

    // 类型过滤
    if (type) {
      filter.type = type;
    }

    // 被评价者过滤
    if (revieweeId) {
      filter.revieweeId = revieweeId;
    }

    // 项目过滤
    if (projectId) {
      filter.projectId = projectId;
    }

    // 服务商过滤
    if (serviceProviderId) {
      filter.serviceProviderId = serviceProviderId;
    }

    // 评分范围过滤
    if (minRating !== undefined || maxRating !== undefined) {
      filter.overallRating = {};
      if (minRating !== undefined) {
        filter.overallRating.$gte = minRating;
      }
      if (maxRating !== undefined) {
        filter.overallRating.$lte = maxRating;
      }
    }

    // 推荐过滤
    if (isRecommended !== undefined) {
      filter.isRecommended = isRecommended;
    }

    // 搜索
    if (search) {
      filter.$or = [
        { title: new RegExp(search, 'i') },
        { content: new RegExp(search, 'i') },
        { tags: new RegExp(search, 'i') },
      ];
    }

    // 排序
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [items, total] = await Promise.all([
      this.reviewModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('reviewerId', 'profile.firstName profile.lastName profile.avatar')
        .populate('revieweeId', 'profile.firstName profile.lastName profile.avatar')
        .populate('responses.responderId', 'profile.firstName profile.lastName')
        .exec(),
      this.reviewModel.countDocuments(filter),
    ]);

    return {
      items,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * 根据ID获取评价详情
   */
  async findOne(id: string): Promise<Review> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid review ID');
    }

    const review = await this.reviewModel
      .findOne({
        _id: id,
        isDeleted: false,
      })
      .populate('reviewerId', 'profile.firstName profile.lastName profile.avatar')
      .populate('revieweeId', 'profile.firstName profile.lastName profile.avatar')
      .populate('responses.responderId', 'profile.firstName profile.lastName')
      .exec();

    if (!review) {
      throw new NotFoundException('Review not found');
    }

    return review;
  }

  /**
   * 更新评价
   */
  async update(id: string, updateReviewDto: UpdateReviewDto, user: User): Promise<Review> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid review ID');
    }

    const review = await this.reviewModel.findOne({
      _id: id,
      isDeleted: false,
    });

    if (!review) {
      throw new NotFoundException('Review not found');
    }

    // 权限检查
    if (review.reviewerId.toString() !== user._id.toString()) {
      throw new ForbiddenException('You can only update your own reviews');
    }

    // 只有待审核或已批准的评价可以修改
    if (review.status !== ReviewStatus.PENDING && review.status !== ReviewStatus.APPROVED) {
      throw new ForbiddenException('Cannot update this review');
    }

    const updatedReview = await this.reviewModel
      .findByIdAndUpdate(
        id,
        {
          ...updateReviewDto,
          status: ReviewStatus.PENDING, // 修改后重新审核
        },
        { new: true }
      )
      .populate('reviewerId', 'profile.firstName profile.lastName profile.avatar')
      .populate('revieweeId', 'profile.firstName profile.lastName profile.avatar')
      .exec();

    return updatedReview;
  }

  /**
   * 删除评价
   */
  async remove(id: string, user: User): Promise<void> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid review ID');
    }

    const review = await this.reviewModel.findOne({
      _id: id,
      isDeleted: false,
    });

    if (!review) {
      throw new NotFoundException('Review not found');
    }

    // 权限检查
    if (review.reviewerId.toString() !== user._id.toString()) {
      throw new ForbiddenException('You can only delete your own reviews');
    }

    await this.reviewModel.findByIdAndUpdate(id, {
      isDeleted: true,
      deletedAt: new Date(),
      deletedBy: user._id,
    });
  }

  /**
   * 添加回复
   */
  async addResponse(id: string, createResponseDto: CreateReviewResponseDto, user: User): Promise<Review> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid review ID');
    }

    const review = await this.reviewModel.findOne({
      _id: id,
      isDeleted: false,
      status: ReviewStatus.APPROVED,
    });

    if (!review) {
      throw new NotFoundException('Review not found or not approved');
    }

    // 只有被评价者可以回复
    if (review.revieweeId.toString() !== user._id.toString()) {
      throw new ForbiddenException('Only the reviewee can respond to this review');
    }

    const response = {
      content: createResponseDto.content,
      responderId: user._id,
      createdAt: new Date(),
    };

    const updatedReview = await this.reviewModel
      .findByIdAndUpdate(
        id,
        {
          $push: { responses: response },
        },
        { new: true }
      )
      .populate('reviewerId', 'profile.firstName profile.lastName profile.avatar')
      .populate('revieweeId', 'profile.firstName profile.lastName profile.avatar')
      .populate('responses.responderId', 'profile.firstName profile.lastName')
      .exec();

    return updatedReview;
  }

  /**
   * 点赞评价
   */
  async likeReview(id: string, user: User): Promise<Review> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid review ID');
    }

    const review = await this.reviewModel.findByIdAndUpdate(
      id,
      {
        $inc: { likeCount: 1 },
      },
      { new: true }
    );

    if (!review) {
      throw new NotFoundException('Review not found');
    }

    return review;
  }

  /**
   * 标记评价为有用
   */
  async markHelpful(id: string, user: User): Promise<Review> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid review ID');
    }

    const review = await this.reviewModel.findByIdAndUpdate(
      id,
      {
        $inc: { helpfulCount: 1 },
      },
      { new: true }
    );

    if (!review) {
      throw new NotFoundException('Review not found');
    }

    return review;
  }

  /**
   * 审核评价（管理员）
   */
  async moderate(id: string, moderationDto: ReviewModerationDto, user: User): Promise<Review> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid review ID');
    }

    const updatedReview = await this.reviewModel
      .findByIdAndUpdate(
        id,
        {
          status: moderationDto.status,
          moderationNote: moderationDto.moderationNote,
          moderatedBy: user._id,
          moderatedAt: new Date(),
        },
        { new: true }
      )
      .populate('reviewerId', 'profile.firstName profile.lastName profile.avatar')
      .populate('revieweeId', 'profile.firstName profile.lastName profile.avatar')
      .exec();

    if (!updatedReview) {
      throw new NotFoundException('Review not found');
    }

    return updatedReview;
  }

  /**
   * 获取用户的评价统计
   */
  async getUserStats(userId: string): Promise<any> {
    const [givenStats, receivedStats] = await Promise.all([
      // 给出的评价统计
      this.reviewModel.aggregate([
        {
          $match: {
            reviewerId: new Types.ObjectId(userId),
            isDeleted: false,
          },
        },
        {
          $group: {
            _id: null,
            totalGiven: { $sum: 1 },
            averageGivenRating: { $avg: '$overallRating' },
          },
        },
      ]),
      // 收到的评价统计
      this.reviewModel.aggregate([
        {
          $match: {
            revieweeId: new Types.ObjectId(userId),
            isDeleted: false,
            status: ReviewStatus.APPROVED,
          },
        },
        {
          $group: {
            _id: null,
            totalReceived: { $sum: 1 },
            averageReceivedRating: { $avg: '$overallRating' },
            recommendationCount: { $sum: { $cond: ['$isRecommended', 1, 0] } },
            ratingDistribution: {
              $push: '$overallRating',
            },
          },
        },
      ]),
    ]);

    const given = givenStats[0] || { totalGiven: 0, averageGivenRating: 0 };
    const received = receivedStats[0] || {
      totalReceived: 0,
      averageReceivedRating: 0,
      recommendationCount: 0,
      ratingDistribution: [],
    };

    // 计算评分分布
    const ratingDistribution = [1, 2, 3, 4, 5].map(rating => ({
      rating,
      count: received.ratingDistribution.filter((r: number) => r === rating).length,
    }));

    return {
      given: {
        total: given.totalGiven,
        averageRating: Math.round(given.averageGivenRating * 10) / 10,
      },
      received: {
        total: received.totalReceived,
        averageRating: Math.round(received.averageReceivedRating * 10) / 10,
        recommendationRate: received.totalReceived > 0
          ? Math.round((received.recommendationCount / received.totalReceived) * 100)
          : 0,
        ratingDistribution,
      },
    };
  }
}
