import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { QuotesService } from './quotes.service';
import { CreateQuoteDto } from './dto/create-quote.dto';
import { UpdateQuoteDto } from './dto/update-quote.dto';
import { QuoteQueryDto } from './dto/quote-query.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Quote } from './schemas/quote.schema';
import { PaginatedResult } from '../../common/interfaces/paginated-result.interface';

@ApiTags('Quotes')
@Controller('quotes')
@UseGuards(ThrottlerGuard)
export class QuotesController {
  constructor(private readonly quotesService: QuotesService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建报价' })
  @ApiResponse({
    status: 201,
    description: '报价创建成功',
    type: Quote,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  async create(
    @Body() createQuoteDto: CreateQuoteDto,
    @Request() req: any,
  ): Promise<Quote> {
    return this.quotesService.create(createQuoteDto, req.user);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取报价列表' })
  @ApiResponse({
    status: 200,
    description: '获取报价列表成功',
    type: [Quote],
  })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiQuery({ name: 'status', required: false, description: '报价状态' })
  @ApiQuery({ name: 'projectId', required: false, description: '项目ID' })
  @ApiQuery({ name: 'providerId', required: false, description: '服务商ID' })
  @ApiQuery({ name: 'customerId', required: false, description: '客户ID' })
  @ApiQuery({ name: 'minAmount', required: false, description: '最低金额' })
  @ApiQuery({ name: 'maxAmount', required: false, description: '最高金额' })
  @ApiQuery({ name: 'search', required: false, description: '搜索关键词' })
  async findAll(@Query() query: QuoteQueryDto): Promise<PaginatedResult<Quote>> {
    return this.quotesService.findAll(query);
  }

  @Get('project/:projectId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取项目的所有报价' })
  @ApiResponse({
    status: 200,
    description: '获取项目报价成功',
    type: [Quote],
  })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  async findByProject(
    @Param('projectId') projectId: string,
    @Query() query: QuoteQueryDto,
  ): Promise<PaginatedResult<Quote>> {
    return this.quotesService.findByProject(projectId, query);
  }

  @Get('provider/:providerId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取服务商的所有报价' })
  @ApiResponse({
    status: 200,
    description: '获取服务商报价成功',
    type: [Quote],
  })
  @ApiParam({ name: 'providerId', description: '服务商ID' })
  async findByProvider(
    @Param('providerId') providerId: string,
    @Query() query: QuoteQueryDto,
  ): Promise<PaginatedResult<Quote>> {
    return this.quotesService.findByProvider(providerId, query);
  }

  @Get('customer/:customerId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取客户的所有报价' })
  @ApiResponse({
    status: 200,
    description: '获取客户报价成功',
    type: [Quote],
  })
  @ApiParam({ name: 'customerId', description: '客户ID' })
  async findByCustomer(
    @Param('customerId') customerId: string,
    @Query() query: QuoteQueryDto,
  ): Promise<PaginatedResult<Quote>> {
    return this.quotesService.findByCustomer(customerId, query);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据ID获取报价详情' })
  @ApiResponse({
    status: 200,
    description: '获取报价详情成功',
    type: Quote,
  })
  @ApiResponse({
    status: 404,
    description: '报价不存在',
  })
  @ApiParam({ name: 'id', description: '报价ID' })
  async findOne(@Param('id') id: string): Promise<Quote> {
    return this.quotesService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新报价' })
  @ApiResponse({
    status: 200,
    description: '报价更新成功',
    type: Quote,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '报价不存在',
  })
  @ApiParam({ name: 'id', description: '报价ID' })
  async update(
    @Param('id') id: string,
    @Body() updateQuoteDto: UpdateQuoteDto,
    @Request() req: any,
  ): Promise<Quote> {
    return this.quotesService.update(id, updateQuoteDto, req.user);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除报价' })
  @ApiResponse({
    status: 204,
    description: '报价删除成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '报价不存在',
  })
  @ApiParam({ name: 'id', description: '报价ID' })
  async remove(@Param('id') id: string, @Request() req: any): Promise<void> {
    return this.quotesService.remove(id, req.user);
  }

  @Post(':id/submit')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '提交报价' })
  @ApiResponse({
    status: 200,
    description: '报价提交成功',
    type: Quote,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '报价不存在',
  })
  @ApiParam({ name: 'id', description: '报价ID' })
  async submit(@Param('id') id: string, @Request() req: any): Promise<Quote> {
    return this.quotesService.submit(id, req.user);
  }

  @Post(':id/accept')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '接受报价' })
  @ApiResponse({
    status: 200,
    description: '报价接受成功',
    type: Quote,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '报价不存在',
  })
  @ApiParam({ name: 'id', description: '报价ID' })
  async accept(@Param('id') id: string, @Request() req: any): Promise<Quote> {
    return this.quotesService.accept(id, req.user);
  }

  @Post(':id/decline')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '拒绝报价' })
  @ApiResponse({
    status: 200,
    description: '报价拒绝成功',
    type: Quote,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '报价不存在',
  })
  @ApiParam({ name: 'id', description: '报价ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        reason: {
          type: 'string',
          description: '拒绝原因',
        },
      },
      required: ['reason'],
    },
  })
  async decline(
    @Param('id') id: string,
    @Body() body: { reason: string },
    @Request() req: any,
  ): Promise<Quote> {
    return this.quotesService.decline(id, body.reason, req.user);
  }
}
