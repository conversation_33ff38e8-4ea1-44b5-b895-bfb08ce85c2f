'use strict';(function(h,c){"object"===typeof exports&&"undefined"!==typeof module?c(exports,require("three")):"function"===typeof define&&define.amd?define(["exports","three"],c):(h="undefined"!==typeof globalThis?globalThis:h||self,c(h.troika_three_utils={},h.THREE))})(this,function(h,c){function q(a){return a.replace(/^[ \t]*#include +<([\w\d./]+)>/gm,function(a,k){return(k=c.ShaderChunk[k])?q(k):a})}function m(a,b){let k=D(b),e=n.get(a);e||n.set(a,e=Object.create(null));if(e[k])return new e[k];
let g=`_onBeforeCompile${k}`,h=function(d,e){a.onBeforeCompile.call(this,d,e);e=this.customProgramCacheKey()+"|"+d.vertexShader+"|"+d.fragmentShader;var c=v[e];c||(c=E(this,d,b,k),c=v[e]=c);d.vertexShader=c.vertexShader;d.fragmentShader=c.fragmentShader;l(d.uniforms,this.uniforms);b.timeUniform&&(d.uniforms[b.timeUniform]={get value(){return Date.now()-F}});if(this[g])this[g](d)},t=function(){return w(b.chained?a:a.clone())},w=function(d){const c=Object.create(d,G);Object.defineProperty(c,"baseMaterial",
{value:a});Object.defineProperty(c,"id",{value:H++});{var e=4294967295*Math.random()|0;let a=4294967295*Math.random()|0,b=4294967295*Math.random()|0,d=4294967295*Math.random()|0;e=(f[e&255]+f[e>>8&255]+f[e>>16&255]+f[e>>24&255]+"-"+f[a&255]+f[a>>8&255]+"-"+f[a>>16&15|64]+f[a>>24&255]+"-"+f[b&63|128]+f[b>>8&255]+"-"+f[b>>16&255]+f[b>>24&255]+f[d&255]+f[d>>8&255]+f[d>>16&255]+f[d>>24&255]).toUpperCase()}c.uuid=e;c.uniforms=l({},d.uniforms,b.uniforms);c.defines=l({},d.defines,b.defines);c.defines[`TROIKA_DERIVED_MATERIAL_${k}`]=
"";c.extensions=l({},d.extensions,b.extensions);c._listeners=void 0;return c},G={constructor:{value:t},isDerivedMaterial:{value:!0},type:{get:()=>a.type,set:b=>{a.type=b}},isDerivedFrom:{writable:!0,configurable:!0,value:function(a){const b=this.baseMaterial;return a===b||b.isDerivedMaterial&&b.isDerivedFrom(a)||!1}},customProgramCacheKey:{writable:!0,configurable:!0,value:function(){return a.customProgramCacheKey()+"|"+k}},onBeforeCompile:{get(){return h},set(a){this[g]=a}},copy:{writable:!0,configurable:!0,
value:function(b){a.copy.call(this,b);a.isShaderMaterial||a.isDerivedMaterial||(l(this.extensions,b.extensions),l(this.defines,b.defines),l(this.uniforms,c.UniformsUtils.clone(b.uniforms)));return this}},clone:{writable:!0,configurable:!0,value:function(){const b=new a.constructor;return w(b).copy(this)}},getDepthMaterial:{writable:!0,configurable:!0,value:function(){let d=this._depthMaterial;d||(d=this._depthMaterial=m(a.isDerivedMaterial?a.getDepthMaterial():new c.MeshDepthMaterial({depthPacking:c.RGBADepthPacking}),
b),d.defines.IS_DEPTH_MATERIAL="",d.uniforms=this.uniforms);return d}},getDistanceMaterial:{writable:!0,configurable:!0,value:function(){let d=this._distanceMaterial;d||(d=this._distanceMaterial=m(a.isDerivedMaterial?a.getDistanceMaterial():new c.MeshDistanceMaterial,b),d.defines.IS_DISTANCE_MATERIAL="",d.uniforms=this.uniforms);return d}},dispose:{writable:!0,configurable:!0,value(){const {_depthMaterial:b,_distanceMaterial:c}=this;b&&b.dispose();c&&c.dispose();a.dispose.call(this)}}};e[k]=t;return new t}
function E(a,{vertexShader:b,fragmentShader:c},e,g){let {vertexDefs:k,vertexMainIntro:f,vertexMainOutro:h,vertexTransform:l,fragmentDefs:d,fragmentMainIntro:m,fragmentMainOutro:p,fragmentColorTransform:u,customRewriter:r,timeUniform:n}=e;k=k||"";f=f||"";h=h||"";d=d||"";m=m||"";p=p||"";if(l||r)b=q(b);if(u||r)c=c.replace(/^[ \t]*#include <((?:tonemapping|encodings|colorspace|fog|premultiplied_alpha|dithering)_fragment)>/gm,"\n//!BEGIN_POST_CHUNK $1\n$&\n//!END_POST_CHUNK\n"),c=q(c);r&&(c=r({vertexShader:b,
fragmentShader:c}),b=c.vertexShader,c=c.fragmentShader);if(u){let a=[];c=c.replace(/^\/\/!BEGIN_POST_CHUNK[^]+?^\/\/!END_POST_CHUNK/gm,b=>{a.push(b);return""});p=`${u}\n${a.join("\n")}\n${p}`}n&&(e=`\nuniform float ${n};\n`,k=e+k,d=e+d);l&&(k=`${k}
void troikaVertexTransform${g}(inout vec3 position, inout vec3 normal, inout vec2 uv) {
  ${l}
}
`,f=`
troika_position_${g} = vec3(position);
troika_normal_${g} = vec3(normal);
troika_uv_${g} = vec2(uv);
troikaVertexTransform${g}(troika_position_${g}, troika_normal_${g}, troika_uv_${g});
${f}
`,b=`vec3 troika_position_${g};
vec3 troika_normal_${g};
vec2 troika_uv_${g};
${b}
`.replace(/\b(position|normal|uv)\b/g,(a,b,c,e)=>/\battribute\s+vec[23]\s+$/.test(e.substr(0,c))?b:`troika_${b}_${g}`),a.map&&0<a.map.channel||(b=b.replace(/\bMAP_UV\b/g,`troika_uv_${g}`)));b=x(b,g,k,f,h);c=x(c,g,d,m,p);return{vertexShader:b,fragmentShader:c}}function x(a,b,c,e,g){if(e||g||c)a=a.replace(y,`
${c}
void troikaOrigMain${b}() {`),a+=`
void main() {
  ${e}
  troikaOrigMain${b}();
  ${g}
}`;return a}function I(a,b){return"uniforms"===a?void 0:"function"===typeof b?b.toString():b}function D(a){a=JSON.stringify(a,I);let b=z.get(a);null==b&&z.set(a,b=++J);return b}let y=/\bvoid\s+main\s*\(\s*\)\s*{/g,f=[];for(let a=0;256>a;a++)f[a]=(16>a?"0":"")+a.toString(16);let l=Object.assign||function(){let a=arguments[0];for(let b=1,c=arguments.length;b<c;b++){let c=arguments[b];if(c)for(let b in c)Object.prototype.hasOwnProperty.call(c,b)&&(a[b]=c[b])}return a},F=Date.now(),n=new WeakMap,v=new Map,
H=1E10,J=0,z=new Map,K={MeshDepthMaterial:"depth",MeshDistanceMaterial:"distanceRGBA",MeshNormalMaterial:"normal",MeshBasicMaterial:"basic",MeshLambertMaterial:"lambert",MeshPhongMaterial:"phong",MeshToonMaterial:"toon",MeshStandardMaterial:"physical",MeshPhysicalMaterial:"physical",MeshMatcapMaterial:"matcap",LineBasicMaterial:"basic",LineDashedMaterial:"dashed",PointsMaterial:"points",ShadowMaterial:"shadow",SpriteMaterial:"sprite"},A=null,B=new c.MeshStandardMaterial({color:16777215,side:c.DoubleSide});
class C extends c.Mesh{static getGeometry(){return A||(A=(new c.CylinderGeometry(1,1,1,6,64)).translate(0,.5,0))}constructor(){super(C.getGeometry(),B);this.pointA=new c.Vector3;this.controlA=new c.Vector3;this.controlB=new c.Vector3;this.pointB=new c.Vector3;this.radius=.01;this.dashArray=new c.Vector2;this.dashOffset=0;this.frustumCulled=!1}get material(){let a=this._derivedMaterial,b=this._baseMaterial||this._defaultMaterial||(this._defaultMaterial=B.clone());a&&a.baseMaterial===b||(a=this._derivedMaterial=
m(b,{chained:!0,uniforms:{pointA:{value:new c.Vector3},controlA:{value:new c.Vector3},controlB:{value:new c.Vector3},pointB:{value:new c.Vector3},radius:{value:.01},dashing:{value:new c.Vector3}},vertexDefs:"\nuniform vec3 pointA;\nuniform vec3 controlA;\nuniform vec3 controlB;\nuniform vec3 pointB;\nuniform float radius;\nvarying float bezierT;\n\nvec3 cubicBezier(vec3 p1, vec3 c1, vec3 c2, vec3 p2, float t) {\n  float t2 = 1.0 - t;\n  float b0 = t2 * t2 * t2;\n  float b1 = 3.0 * t * t2 * t2;\n  float b2 = 3.0 * t * t * t2;\n  float b3 = t * t * t;\n  return b0 * p1 + b1 * c1 + b2 * c2 + b3 * p2;\n}\n\nvec3 cubicBezierDerivative(vec3 p1, vec3 c1, vec3 c2, vec3 p2, float t) {\n  float t2 = 1.0 - t;\n  return -3.0 * p1 * t2 * t2 +\n    c1 * (3.0 * t2 * t2 - 6.0 * t2 * t) +\n    c2 * (6.0 * t2 * t - 3.0 * t * t) +\n    3.0 * p2 * t * t;\n}\n",
vertexTransform:'\nfloat t = position.y;\nbezierT = t;\nvec3 bezierCenterPos = cubicBezier(pointA, controlA, controlB, pointB, t);\nvec3 bezierDir = normalize(cubicBezierDerivative(pointA, controlA, controlB, pointB, t));\n\n// Make "sideways" always perpendicular to the camera ray; this ensures that any twists\n// in the cylinder occur where you won\'t see them: \nvec3 viewDirection = normalMatrix * vec3(0.0, 0.0, 1.0);\nif (bezierDir == viewDirection) {\n  bezierDir = normalize(cubicBezierDerivative(pointA, controlA, controlB, pointB, t == 1.0 ? t - 0.0001 : t + 0.0001));\n}\nvec3 sideways = normalize(cross(bezierDir, viewDirection));\nvec3 upish = normalize(cross(sideways, bezierDir));\n\n// Build a matrix for transforming this disc in the cylinder:\nmat4 discTx;\ndiscTx[0].xyz = sideways * radius;\ndiscTx[1].xyz = bezierDir * radius;\ndiscTx[2].xyz = upish * radius;\ndiscTx[3].xyz = bezierCenterPos;\ndiscTx[3][3] = 1.0;\n\n// Apply transform, ignoring original y\nposition = (discTx * vec4(position.x, 0.0, position.z, 1.0)).xyz;\nnormal = normalize(mat3(discTx) * normal);\n',
fragmentDefs:"\nuniform vec3 dashing;\nvarying float bezierT;\n",fragmentMainIntro:"\nif (dashing.x + dashing.y > 0.0) {\n  float dashFrac = mod(bezierT - dashing.z, dashing.x + dashing.y);\n  if (dashFrac > dashing.x) {\n    discard;\n  }\n}\n"}),b.addEventListener("dispose",function e(){b.removeEventListener("dispose",e);a.dispose()}));return a}set material(a){this._baseMaterial=a}get customDepthMaterial(){return this.material.getDepthMaterial()}set customDepthMaterial(a){}get customDistanceMaterial(){return this.material.getDistanceMaterial()}set customDistanceMaterial(a){}onBeforeRender(){let {uniforms:a}=
this.material,{pointA:b,controlA:c,controlB:e,pointB:g,radius:f,dashArray:h,dashOffset:l}=this;a.pointA.value.copy(b);a.controlA.value.copy(c);a.controlB.value.copy(e);a.pointB.value.copy(g);a.radius.value=f;a.dashing.value.set(h.x,h.y,l||0)}raycast(){}}h.BezierMesh=C;h.createDerivedMaterial=m;h.expandShaderIncludes=q;h.getShaderUniformTypes=function(a){let b=/\buniform\s+(int|float|vec[234]|mat[34])\s+([A-Za-z_][\w]*)/g,c=Object.create(null),e;for(;null!==(e=b.exec(a));)c[e[2]]=e[1];return c};h.getShadersForMaterial=
function(a){let b=K[a.type];return b?c.ShaderLib[b]:a};h.invertMatrix4=function(a,b=new c.Matrix4){"function"===typeof b.invert?b.copy(a).invert():b.getInverse(a);return b};h.voidMainRegExp=y;Object.defineProperty(h,"__esModule",{value:!0})})
