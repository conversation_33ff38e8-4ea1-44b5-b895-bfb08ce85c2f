import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { ServiceProvidersService } from './service-providers.service';
import { CreateServiceProviderDto } from './dto/create-service-provider.dto';
import { UpdateServiceProviderDto } from './dto/update-service-provider.dto';
import { ServiceProviderQueryDto } from './dto/service-provider-query.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { ServiceProvider, ServiceProviderStatus } from './schemas/service-provider.schema';
import { PaginatedResult } from '../../common/interfaces/paginated-result.interface';
import { UserType } from '../users/schemas/user.schema';

@ApiTags('Service Providers')
@Controller('service-providers')
@UseGuards(ThrottlerGuard)
export class ServiceProvidersController {
  constructor(private readonly serviceProvidersService: ServiceProvidersService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '申请成为服务商' })
  @ApiResponse({
    status: 201,
    description: '服务商申请提交成功',
    type: ServiceProvider,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 409,
    description: '用户已经是服务商',
  })
  async create(
    @Body() createServiceProviderDto: CreateServiceProviderDto,
    @Request() req: any,
  ): Promise<ServiceProvider> {
    return this.serviceProvidersService.create(createServiceProviderDto, req.user);
  }

  @Get()
  @ApiOperation({ summary: '获取服务商列表' })
  @ApiResponse({
    status: 200,
    description: '获取服务商列表成功',
    type: [ServiceProvider],
  })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiQuery({ name: 'status', required: false, description: '服务商状态' })
  @ApiQuery({ name: 'category', required: false, description: '服务类别' })
  @ApiQuery({ name: 'province', required: false, description: '省份' })
  @ApiQuery({ name: 'city', required: false, description: '城市' })
  @ApiQuery({ name: 'minRating', required: false, description: '最低评分' })
  @ApiQuery({ name: 'search', required: false, description: '搜索关键词' })
  async findAll(@Query() query: ServiceProviderQueryDto): Promise<PaginatedResult<ServiceProvider>> {
    return this.serviceProvidersService.findAll(query);
  }

  @Get('me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取我的服务商信息' })
  @ApiResponse({
    status: 200,
    description: '获取服务商信息成功',
    type: ServiceProvider,
  })
  @ApiResponse({
    status: 404,
    description: '服务商信息不存在',
  })
  async findMyProfile(@Request() req: any): Promise<ServiceProvider> {
    return this.serviceProvidersService.findByUserId(req.user._id.toString());
  }

  @Get('stats')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserType.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取服务商统计信息（管理员）' })
  @ApiResponse({
    status: 200,
    description: '获取统计信息成功',
  })
  async getStats(): Promise<any> {
    return this.serviceProvidersService.getStats();
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID获取服务商详情' })
  @ApiResponse({
    status: 200,
    description: '获取服务商详情成功',
    type: ServiceProvider,
  })
  @ApiResponse({
    status: 404,
    description: '服务商不存在',
  })
  @ApiParam({ name: 'id', description: '服务商ID' })
  async findOne(@Param('id') id: string): Promise<ServiceProvider> {
    return this.serviceProvidersService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新服务商信息' })
  @ApiResponse({
    status: 200,
    description: '服务商信息更新成功',
    type: ServiceProvider,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '服务商不存在',
  })
  @ApiParam({ name: 'id', description: '服务商ID' })
  async update(
    @Param('id') id: string,
    @Body() updateServiceProviderDto: UpdateServiceProviderDto,
    @Request() req: any,
  ): Promise<ServiceProvider> {
    return this.serviceProvidersService.update(id, updateServiceProviderDto, req.user);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除服务商申请' })
  @ApiResponse({
    status: 204,
    description: '服务商申请删除成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '服务商不存在',
  })
  @ApiParam({ name: 'id', description: '服务商ID' })
  async remove(@Param('id') id: string, @Request() req: any): Promise<void> {
    return this.serviceProvidersService.remove(id, req.user);
  }

  @Post(':id/review')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserType.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: '审核服务商申请（管理员）' })
  @ApiResponse({
    status: 200,
    description: '审核完成',
    type: ServiceProvider,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '服务商不存在',
  })
  @ApiParam({ name: 'id', description: '服务商ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        status: {
          type: 'string',
          enum: Object.values(ServiceProviderStatus),
          description: '审核状态',
        },
        reviewNotes: {
          type: 'string',
          description: '审核备注',
        },
      },
      required: ['status', 'reviewNotes'],
    },
  })
  async review(
    @Param('id') id: string,
    @Body() body: { status: ServiceProviderStatus; reviewNotes: string },
    @Request() req: any,
  ): Promise<ServiceProvider> {
    return this.serviceProvidersService.review(id, body.status, body.reviewNotes, req.user);
  }
}
