import { createParamDecorator, ExecutionContext } from '@nestjs/common';

export const Lang = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest();
    
    // 优先级：查询参数 > 请求头 > Accept-Language > 默认语言
    const langFromQuery = request.query?.lang;
    const langFromHeader = request.headers['x-lang'];
    const langFromAcceptLanguage = request.headers['accept-language'];
    
    if (langFromQuery && ['en', 'zh'].includes(langFromQuery)) {
      return langFromQuery;
    }
    
    if (langFromHeader && ['en', 'zh'].includes(langFromHeader)) {
      return langFromHeader;
    }
    
    if (langFromAcceptLanguage) {
      // 解析 Accept-Language 头
      const languages = langFromAcceptLanguage
        .split(',')
        .map(lang => lang.split(';')[0].trim().toLowerCase());
      
      for (const lang of languages) {
        if (lang.startsWith('zh')) {
          return 'zh';
        }
        if (lang.startsWith('en')) {
          return 'en';
        }
      }
    }
    
    // 默认返回英语
    return 'en';
  },
);
