"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.annotateSourceCode = exports.createTypesFile = exports.convertObjectToTypeDefinition = void 0;
const ts = require("typescript");
const convertObjectToTypeDefinition = async (object) => {
    switch (typeof object) {
        case 'object':
            return Promise.all(Object.keys(object).map(async (key) => {
                if (typeof object[key] === 'string') {
                    return ts.factory.createPropertySignature(undefined, ts.factory.createStringLiteral(key), undefined, ts.factory.createKeywordTypeNode(ts.SyntaxKind.StringKeyword));
                }
                if (Array.isArray(object[key])) {
                    return ts.factory.createPropertySignature(undefined, ts.factory.createStringLiteral(key), undefined, ts.factory.createTupleTypeNode(Array(object[key].length).fill(ts.factory.createKeywordTypeNode(ts.SyntaxKind.StringKeyword))));
                }
                return ts.factory.createPropertySignature(undefined, ts.factory.createStringLiteral(key), undefined, ts.factory.createTypeLiteralNode(await (0, exports.convertObjectToTypeDefinition)(object[key])));
            }));
    }
    return [];
};
exports.convertObjectToTypeDefinition = convertObjectToTypeDefinition;
const printer = ts.createPrinter({ newLine: ts.NewLineKind.LineFeed });
const createTypesFile = async (object) => {
    const sourceFile = ts.createSourceFile('placeholder.ts', '', ts.ScriptTarget.ESNext, true, ts.ScriptKind.TS);
    const i18nTranslationsType = ts.factory.createTypeAliasDeclaration([ts.factory.createModifier(ts.SyntaxKind.ExportKeyword)], ts.factory.createIdentifier('I18nTranslations'), undefined, ts.factory.createTypeLiteralNode(await (0, exports.convertObjectToTypeDefinition)(object)));
    const nodes = ts.factory.createNodeArray([
        ts.factory.createImportDeclaration(undefined, ts.factory.createImportClause(false, undefined, ts.factory.createNamedImports([
            ts.factory.createImportSpecifier(false, undefined, ts.factory.createIdentifier('Path')),
        ])), ts.factory.createStringLiteral('nestjs-i18n'), undefined),
        i18nTranslationsType,
        ts.factory.createTypeAliasDeclaration([ts.factory.createModifier(ts.SyntaxKind.ExportKeyword)], ts.factory.createIdentifier('I18nPath'), undefined, ts.factory.createTypeReferenceNode(ts.factory.createIdentifier('Path'), [
            ts.factory.createTypeReferenceNode(ts.factory.createIdentifier('I18nTranslations'), undefined),
        ])),
    ]);
    nodes.forEach((node) => {
        ts.addSyntheticLeadingComment(node, ts.SyntaxKind.MultiLineCommentTrivia, ' prettier-ignore ', true);
    });
    return printer.printList(ts.ListFormat.MultiLine, nodes, sourceFile);
};
exports.createTypesFile = createTypesFile;
const annotateSourceCode = (code) => {
    return `/* DO NOT EDIT, file generated by nestjs-i18n */

/* eslint-disable */
${code}`;
};
exports.annotateSourceCode = annotateSourceCode;
//# sourceMappingURL=typescript.js.map