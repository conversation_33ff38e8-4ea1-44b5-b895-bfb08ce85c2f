import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  Task,
  TaskDocument,
  TaskStatus,
  TaskPriority,
} from './schemas/task.schema';
import {
  CreateTaskDto,
  UpdateTaskDto,
  TaskQueryDto,
  CreateTaskCommentDto,
  CreateTimeLogDto,
} from './dto/create-task.dto';
import { PaginatedResult } from '../../common/interfaces/paginated-result.interface';
import { User } from '../users/schemas/user.schema';

@Injectable()
export class TasksService {
  constructor(
    @InjectModel(Task.name) private taskModel: Model<TaskDocument>,
  ) {}

  /**
   * 创建任务
   */
  async create(createTaskDto: CreateTaskDto, user: User): Promise<Task> {
    const taskData = {
      ...createTaskDto,
      createdBy: user._id,
      projectId: createTaskDto.projectId ? new Types.ObjectId(createTaskDto.projectId) : undefined,
      quoteId: createTaskDto.quoteId ? new Types.ObjectId(createTaskDto.quoteId) : undefined,
      parentTaskId: createTaskDto.parentTaskId ? new Types.ObjectId(createTaskDto.parentTaskId) : undefined,
      estimatedStartDate: createTaskDto.estimatedStartDate ? new Date(createTaskDto.estimatedStartDate) : undefined,
      estimatedEndDate: createTaskDto.estimatedEndDate ? new Date(createTaskDto.estimatedEndDate) : undefined,
      dueDate: createTaskDto.dueDate ? new Date(createTaskDto.dueDate) : undefined,
      assignees: createTaskDto.assignees?.map(assignee => ({
        userId: new Types.ObjectId(assignee.userId),
        assignedBy: user._id,
        assignedAt: new Date(),
        note: assignee.note,
      })),
    };

    const createdTask = new this.taskModel(taskData);
    return createdTask.save();
  }

  /**
   * 获取任务列表
   */
  async findAll(query: TaskQueryDto, user?: User): Promise<PaginatedResult<Task>> {
    const {
      page = 1,
      limit = 20,
      type,
      status,
      priority,
      createdBy,
      assigneeId,
      projectId,
      parentTaskId,
      isImportant,
      isUrgent,
      isOverdue,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = query;

    const filter: any = {
      isDeleted: false,
    };

    // 如果有用户信息，只显示与该用户相关的任务
    if (user) {
      filter.$or = [
        { createdBy: user._id },
        { 'assignees.userId': user._id },
      ];
    }

    // 类型过滤
    if (type) {
      filter.type = type;
    }

    // 状态过滤
    if (status) {
      filter.status = status;
    }

    // 优先级过滤
    if (priority) {
      filter.priority = priority;
    }

    // 创建者过滤
    if (createdBy) {
      filter.createdBy = createdBy;
    }

    // 分配者过滤
    if (assigneeId) {
      filter['assignees.userId'] = assigneeId;
    }

    // 项目过滤
    if (projectId) {
      filter.projectId = projectId;
    }

    // 父任务过滤
    if (parentTaskId) {
      filter.parentTaskId = parentTaskId;
    }

    // 重要性过滤
    if (isImportant !== undefined) {
      filter.isImportant = isImportant;
    }

    // 紧急性过滤
    if (isUrgent !== undefined) {
      filter.isUrgent = isUrgent;
    }

    // 逾期过滤
    if (isOverdue !== undefined) {
      if (isOverdue) {
        filter.dueDate = { $lt: new Date() };
        filter.status = { $ne: TaskStatus.COMPLETED };
      }
    }

    // 搜索
    if (search) {
      filter.$or = [
        { title: new RegExp(search, 'i') },
        { description: new RegExp(search, 'i') },
        { tags: new RegExp(search, 'i') },
        { notes: new RegExp(search, 'i') },
      ];
    }

    // 排序
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [items, total] = await Promise.all([
      this.taskModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('createdBy', 'profile.firstName profile.lastName profile.avatar')
        .populate('assignees.userId', 'profile.firstName profile.lastName profile.avatar')
        .populate('projectId', 'title status')
        .populate('parentTaskId', 'title status')
        .exec(),
      this.taskModel.countDocuments(filter),
    ]);

    return {
      items,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * 根据ID获取任务详情
   */
  async findOne(id: string, user?: User): Promise<Task> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid task ID');
    }

    const filter: any = {
      _id: id,
      isDeleted: false,
    };

    // 权限检查
    if (user) {
      filter.$or = [
        { createdBy: user._id },
        { 'assignees.userId': user._id },
      ];
    }

    const task = await this.taskModel
      .findOne(filter)
      .populate('createdBy', 'profile.firstName profile.lastName profile.avatar')
      .populate('assignees.userId', 'profile.firstName profile.lastName profile.avatar')
      .populate('assignees.assignedBy', 'profile.firstName profile.lastName')
      .populate('projectId', 'title status')
      .populate('parentTaskId', 'title status')
      .populate('comments.authorId', 'profile.firstName profile.lastName profile.avatar')
      .populate('timeLogs.userId', 'profile.firstName profile.lastName')
      .exec();

    if (!task) {
      throw new NotFoundException('Task not found');
    }

    return task;
  }

  /**
   * 更新任务
   */
  async update(id: string, updateTaskDto: UpdateTaskDto, user: User): Promise<Task> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid task ID');
    }

    const task = await this.taskModel.findOne({
      _id: id,
      isDeleted: false,
    });

    if (!task) {
      throw new NotFoundException('Task not found');
    }

    // 权限检查：只有创建者或分配者可以更新任务
    const isCreator = task.createdBy.toString() === user._id.toString();
    const isAssignee = task.assignees?.some(assignee => 
      assignee.userId.toString() === user._id.toString()
    );

    if (!isCreator && !isAssignee) {
      throw new ForbiddenException('You do not have permission to update this task');
    }

    const updateData: any = { ...updateTaskDto };

    // 处理日期字段
    if (updateTaskDto.estimatedStartDate) {
      updateData.estimatedStartDate = new Date(updateTaskDto.estimatedStartDate);
    }
    if (updateTaskDto.estimatedEndDate) {
      updateData.estimatedEndDate = new Date(updateTaskDto.estimatedEndDate);
    }
    if (updateTaskDto.actualStartDate) {
      updateData.actualStartDate = new Date(updateTaskDto.actualStartDate);
    }
    if (updateTaskDto.actualEndDate) {
      updateData.actualEndDate = new Date(updateTaskDto.actualEndDate);
    }
    if (updateTaskDto.dueDate) {
      updateData.dueDate = new Date(updateTaskDto.dueDate);
    }

    // 处理分配者
    if (updateTaskDto.assignees) {
      updateData.assignees = updateTaskDto.assignees.map(assignee => ({
        userId: new Types.ObjectId(assignee.userId),
        assignedBy: user._id,
        assignedAt: new Date(),
        note: assignee.note,
      }));
    }

    const updatedTask = await this.taskModel
      .findByIdAndUpdate(id, updateData, { new: true })
      .populate('createdBy', 'profile.firstName profile.lastName profile.avatar')
      .populate('assignees.userId', 'profile.firstName profile.lastName profile.avatar')
      .populate('projectId', 'title status')
      .populate('parentTaskId', 'title status')
      .exec();

    return updatedTask;
  }

  /**
   * 删除任务
   */
  async remove(id: string, user: User): Promise<void> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid task ID');
    }

    const task = await this.taskModel.findOne({
      _id: id,
      isDeleted: false,
    });

    if (!task) {
      throw new NotFoundException('Task not found');
    }

    // 权限检查：只有创建者可以删除任务
    if (task.createdBy.toString() !== user._id.toString()) {
      throw new ForbiddenException('You can only delete tasks you created');
    }

    await this.taskModel.findByIdAndUpdate(id, {
      isDeleted: true,
      deletedAt: new Date(),
      deletedBy: user._id,
    });
  }

  /**
   * 添加任务评论
   */
  async addComment(id: string, commentDto: CreateTaskCommentDto, user: User): Promise<Task> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid task ID');
    }

    const task = await this.taskModel.findOne({
      _id: id,
      isDeleted: false,
    });

    if (!task) {
      throw new NotFoundException('Task not found');
    }

    // 权限检查：只有创建者或分配者可以添加评论
    const isCreator = task.createdBy.toString() === user._id.toString();
    const isAssignee = task.assignees?.some(assignee =>
      assignee.userId.toString() === user._id.toString()
    );

    if (!isCreator && !isAssignee) {
      throw new ForbiddenException('You do not have permission to comment on this task');
    }

    const comment = {
      authorId: user._id,
      content: commentDto.content,
      attachments: commentDto.attachments || [],
      createdAt: new Date(),
      isEdited: false,
    };

    const updatedTask = await this.taskModel
      .findByIdAndUpdate(
        id,
        { $push: { comments: comment } },
        { new: true }
      )
      .populate('createdBy', 'profile.firstName profile.lastName profile.avatar')
      .populate('assignees.userId', 'profile.firstName profile.lastName profile.avatar')
      .populate('comments.authorId', 'profile.firstName profile.lastName profile.avatar')
      .exec();

    return updatedTask;
  }

  /**
   * 添加时间记录
   */
  async addTimeLog(id: string, timeLogDto: CreateTimeLogDto, user: User): Promise<Task> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid task ID');
    }

    const task = await this.taskModel.findOne({
      _id: id,
      isDeleted: false,
    });

    if (!task) {
      throw new NotFoundException('Task not found');
    }

    // 权限检查：只有分配者可以添加时间记录
    const isAssignee = task.assignees?.some(assignee =>
      assignee.userId.toString() === user._id.toString()
    );

    if (!isAssignee) {
      throw new ForbiddenException('You can only log time for tasks assigned to you');
    }

    let duration = timeLogDto.duration || 0;

    // 如果提供了开始和结束时间，计算时长
    if (timeLogDto.startTime && timeLogDto.endTime) {
      const start = new Date(timeLogDto.startTime);
      const end = new Date(timeLogDto.endTime);
      duration = Math.round((end.getTime() - start.getTime()) / (1000 * 60)); // 转换为分钟
    }

    const timeLog = {
      userId: user._id,
      startTime: new Date(timeLogDto.startTime),
      endTime: timeLogDto.endTime ? new Date(timeLogDto.endTime) : undefined,
      description: timeLogDto.description,
      duration,
    };

    const updatedTask = await this.taskModel
      .findByIdAndUpdate(
        id,
        { $push: { timeLogs: timeLog } },
        { new: true }
      )
      .populate('createdBy', 'profile.firstName profile.lastName profile.avatar')
      .populate('assignees.userId', 'profile.firstName profile.lastName profile.avatar')
      .populate('timeLogs.userId', 'profile.firstName profile.lastName')
      .exec();

    return updatedTask;
  }

  /**
   * 更新任务状态
   */
  async updateStatus(id: string, status: TaskStatus, user: User): Promise<Task> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid task ID');
    }

    const task = await this.taskModel.findOne({
      _id: id,
      isDeleted: false,
    });

    if (!task) {
      throw new NotFoundException('Task not found');
    }

    // 权限检查：只有创建者或分配者可以更新状态
    const isCreator = task.createdBy.toString() === user._id.toString();
    const isAssignee = task.assignees?.some(assignee =>
      assignee.userId.toString() === user._id.toString()
    );

    if (!isCreator && !isAssignee) {
      throw new ForbiddenException('You do not have permission to update this task status');
    }

    const updateData: any = { status };

    // 如果状态变为进行中，设置实际开始时间
    if (status === TaskStatus.IN_PROGRESS && !task.actualStartDate) {
      updateData.actualStartDate = new Date();
    }

    // 如果状态变为完成，设置实际结束时间和进度为100%
    if (status === TaskStatus.COMPLETED) {
      updateData.actualEndDate = new Date();
      updateData.progress = 100;
    }

    const updatedTask = await this.taskModel
      .findByIdAndUpdate(id, updateData, { new: true })
      .populate('createdBy', 'profile.firstName profile.lastName profile.avatar')
      .populate('assignees.userId', 'profile.firstName profile.lastName profile.avatar')
      .exec();

    return updatedTask;
  }

  /**
   * 获取用户任务统计
   */
  async getUserStats(userId: string): Promise<any> {
    const stats = await this.taskModel.aggregate([
      {
        $match: {
          $or: [
            { createdBy: new Types.ObjectId(userId) },
            { 'assignees.userId': new Types.ObjectId(userId) },
          ],
          isDeleted: false,
        },
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalEstimatedHours: { $sum: '$estimatedHours' },
          totalActualHours: { $sum: '$actualHours' },
        },
      },
    ]);

    const result = {
      total: 0,
      totalEstimatedHours: 0,
      totalActualHours: 0,
      todo: 0,
      inProgress: 0,
      review: 0,
      completed: 0,
      cancelled: 0,
      onHold: 0,
    };

    stats.forEach(stat => {
      result.total += stat.count;
      result.totalEstimatedHours += stat.totalEstimatedHours;
      result.totalActualHours += stat.totalActualHours;

      switch (stat._id) {
        case TaskStatus.TODO:
          result.todo = stat.count;
          break;
        case TaskStatus.IN_PROGRESS:
          result.inProgress = stat.count;
          break;
        case TaskStatus.REVIEW:
          result.review = stat.count;
          break;
        case TaskStatus.COMPLETED:
          result.completed = stat.count;
          break;
        case TaskStatus.CANCELLED:
          result.cancelled = stat.count;
          break;
        case TaskStatus.ON_HOLD:
          result.onHold = stat.count;
          break;
      }
    });

    return result;
  }

  /**
   * 获取项目任务统计
   */
  async getProjectStats(projectId: string): Promise<any> {
    const stats = await this.taskModel.aggregate([
      {
        $match: {
          projectId: new Types.ObjectId(projectId),
          isDeleted: false,
        },
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalEstimatedHours: { $sum: '$estimatedHours' },
          totalActualHours: { $sum: '$actualHours' },
          avgProgress: { $avg: '$progress' },
        },
      },
    ]);

    const result = {
      total: 0,
      totalEstimatedHours: 0,
      totalActualHours: 0,
      avgProgress: 0,
      todo: 0,
      inProgress: 0,
      review: 0,
      completed: 0,
      cancelled: 0,
      onHold: 0,
    };

    let totalProgress = 0;
    let taskCount = 0;

    stats.forEach(stat => {
      result.total += stat.count;
      result.totalEstimatedHours += stat.totalEstimatedHours;
      result.totalActualHours += stat.totalActualHours;
      totalProgress += stat.avgProgress * stat.count;
      taskCount += stat.count;

      switch (stat._id) {
        case TaskStatus.TODO:
          result.todo = stat.count;
          break;
        case TaskStatus.IN_PROGRESS:
          result.inProgress = stat.count;
          break;
        case TaskStatus.REVIEW:
          result.review = stat.count;
          break;
        case TaskStatus.COMPLETED:
          result.completed = stat.count;
          break;
        case TaskStatus.CANCELLED:
          result.cancelled = stat.count;
          break;
        case TaskStatus.ON_HOLD:
          result.onHold = stat.count;
          break;
      }
    });

    result.avgProgress = taskCount > 0 ? Math.round(totalProgress / taskCount) : 0;

    return result;
  }
}
