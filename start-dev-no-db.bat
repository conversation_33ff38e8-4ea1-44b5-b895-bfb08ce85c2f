@echo off
echo ========================================
echo HomeReno 开发环境启动脚本 (无数据库模式)
echo ========================================

echo.
echo 1. 设置环境变量...
set USE_MOCK_DATABASE=true
set NODE_ENV=development

echo.
echo 2. 安装依赖包...
call npm install

echo.
echo 3. 创建必要的目录...
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads

echo.
echo 4. 启动应用开发服务器 (内存数据库模式)...
echo.
echo ✅ 应用将在 http://localhost:3000 启动
echo ✅ API文档将在 http://localhost:3000/api 可用
echo ✅ 使用内存数据库，无需MongoDB/Redis
echo ✅ 示例用户: <EMAIL>, <EMAIL>
echo ✅ 密码: password123
echo.

call npm run start:dev

echo.
echo 应用已停止
pause
