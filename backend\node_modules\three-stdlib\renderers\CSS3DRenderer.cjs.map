{"version": 3, "file": "CSS3DRenderer.cjs", "sources": ["../../src/renderers/CSS3DRenderer.js"], "sourcesContent": ["import { Matrix4, Object3D, Quaternion, Vector3 } from 'three'\n\n/**\n * Based on http://www.emagix.net/academic/mscs-project/item/camera-sync-with-css3-and-webgl-threejs\n */\n\nconst _position = /* @__PURE__ */ new Vector3()\nconst _quaternion = /* @__PURE__ */ new Quaternion()\nconst _scale = /* @__PURE__ */ new Vector3()\n\nclass CSS3DObject extends Object3D {\n  constructor(element = document.createElement('div')) {\n    super()\n\n    this.isCSS3DObject = true\n\n    this.element = element\n    this.element.style.position = 'absolute'\n    this.element.style.pointerEvents = 'auto'\n    this.element.style.userSelect = 'none'\n\n    this.element.setAttribute('draggable', false)\n\n    this.addEventListener('removed', function () {\n      this.traverse(function (object) {\n        if (object.element instanceof Element && object.element.parentNode !== null) {\n          object.element.parentNode.removeChild(object.element)\n        }\n      })\n    })\n  }\n\n  copy(source, recursive) {\n    super.copy(source, recursive)\n\n    this.element = source.element.cloneNode(true)\n\n    return this\n  }\n}\n\nclass CSS3DSprite extends CSS3DObject {\n  constructor(element) {\n    super(element)\n\n    this.isCSS3DSprite = true\n\n    this.rotation2D = 0\n  }\n\n  copy(source, recursive) {\n    super.copy(source, recursive)\n\n    this.rotation2D = source.rotation2D\n\n    return this\n  }\n}\n\n//\n\nconst _matrix = /* @__PURE__ */ new Matrix4()\nconst _matrix2 = /* @__PURE__ */ new Matrix4()\n\nclass CSS3DRenderer {\n  constructor(parameters = {}) {\n    const _this = this\n\n    let _width, _height\n    let _widthHalf, _heightHalf\n\n    const cache = {\n      camera: { style: '' },\n      objects: new WeakMap(),\n    }\n\n    const domElement = parameters.element !== undefined ? parameters.element : document.createElement('div')\n\n    domElement.style.overflow = 'hidden'\n\n    this.domElement = domElement\n\n    const viewElement = document.createElement('div')\n    viewElement.style.transformOrigin = '0 0'\n    viewElement.style.pointerEvents = 'none'\n    domElement.appendChild(viewElement)\n\n    const cameraElement = document.createElement('div')\n\n    cameraElement.style.transformStyle = 'preserve-3d'\n\n    viewElement.appendChild(cameraElement)\n\n    this.getSize = function () {\n      return {\n        width: _width,\n        height: _height,\n      }\n    }\n\n    this.render = function (scene, camera) {\n      const fov = camera.projectionMatrix.elements[5] * _heightHalf\n\n      if (camera.view && camera.view.enabled) {\n        // view offset\n        viewElement.style.transform = `translate( ${-camera.view.offsetX * (_width / camera.view.width)}px, ${\n          -camera.view.offsetY * (_height / camera.view.height)\n        }px )`\n\n        // view fullWidth and fullHeight, view width and height\n        viewElement.style.transform += `scale( ${camera.view.fullWidth / camera.view.width}, ${\n          camera.view.fullHeight / camera.view.height\n        } )`\n      } else {\n        viewElement.style.transform = ''\n      }\n\n      if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld()\n      if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld()\n\n      let tx, ty\n\n      if (camera.isOrthographicCamera) {\n        tx = -(camera.right + camera.left) / 2\n        ty = (camera.top + camera.bottom) / 2\n      }\n\n      const scaleByViewOffset = camera.view && camera.view.enabled ? camera.view.height / camera.view.fullHeight : 1\n      const cameraCSSMatrix = camera.isOrthographicCamera\n        ? `scale( ${scaleByViewOffset} )` +\n          'scale(' +\n          fov +\n          ')' +\n          'translate(' +\n          epsilon(tx) +\n          'px,' +\n          epsilon(ty) +\n          'px)' +\n          getCameraCSSMatrix(camera.matrixWorldInverse)\n        : `scale( ${scaleByViewOffset} )` + 'translateZ(' + fov + 'px)' + getCameraCSSMatrix(camera.matrixWorldInverse)\n      const perspective = camera.isPerspectiveCamera ? 'perspective(' + fov + 'px) ' : ''\n\n      const style = perspective + cameraCSSMatrix + 'translate(' + _widthHalf + 'px,' + _heightHalf + 'px)'\n\n      if (cache.camera.style !== style) {\n        cameraElement.style.transform = style\n\n        cache.camera.style = style\n      }\n\n      renderObject(scene, scene, camera, cameraCSSMatrix)\n    }\n\n    this.setSize = function (width, height) {\n      _width = width\n      _height = height\n      _widthHalf = _width / 2\n      _heightHalf = _height / 2\n\n      domElement.style.width = width + 'px'\n      domElement.style.height = height + 'px'\n\n      viewElement.style.width = width + 'px'\n      viewElement.style.height = height + 'px'\n\n      cameraElement.style.width = width + 'px'\n      cameraElement.style.height = height + 'px'\n    }\n\n    function epsilon(value) {\n      return Math.abs(value) < 1e-10 ? 0 : value\n    }\n\n    function getCameraCSSMatrix(matrix) {\n      const elements = matrix.elements\n\n      return (\n        'matrix3d(' +\n        epsilon(elements[0]) +\n        ',' +\n        epsilon(-elements[1]) +\n        ',' +\n        epsilon(elements[2]) +\n        ',' +\n        epsilon(elements[3]) +\n        ',' +\n        epsilon(elements[4]) +\n        ',' +\n        epsilon(-elements[5]) +\n        ',' +\n        epsilon(elements[6]) +\n        ',' +\n        epsilon(elements[7]) +\n        ',' +\n        epsilon(elements[8]) +\n        ',' +\n        epsilon(-elements[9]) +\n        ',' +\n        epsilon(elements[10]) +\n        ',' +\n        epsilon(elements[11]) +\n        ',' +\n        epsilon(elements[12]) +\n        ',' +\n        epsilon(-elements[13]) +\n        ',' +\n        epsilon(elements[14]) +\n        ',' +\n        epsilon(elements[15]) +\n        ')'\n      )\n    }\n\n    function getObjectCSSMatrix(matrix) {\n      const elements = matrix.elements\n      const matrix3d =\n        'matrix3d(' +\n        epsilon(elements[0]) +\n        ',' +\n        epsilon(elements[1]) +\n        ',' +\n        epsilon(elements[2]) +\n        ',' +\n        epsilon(elements[3]) +\n        ',' +\n        epsilon(-elements[4]) +\n        ',' +\n        epsilon(-elements[5]) +\n        ',' +\n        epsilon(-elements[6]) +\n        ',' +\n        epsilon(-elements[7]) +\n        ',' +\n        epsilon(elements[8]) +\n        ',' +\n        epsilon(elements[9]) +\n        ',' +\n        epsilon(elements[10]) +\n        ',' +\n        epsilon(elements[11]) +\n        ',' +\n        epsilon(elements[12]) +\n        ',' +\n        epsilon(elements[13]) +\n        ',' +\n        epsilon(elements[14]) +\n        ',' +\n        epsilon(elements[15]) +\n        ')'\n\n      return 'translate(-50%,-50%)' + matrix3d\n    }\n\n    function renderObject(object, scene, camera, cameraCSSMatrix) {\n      if (object.isCSS3DObject) {\n        const visible = object.visible === true && object.layers.test(camera.layers) === true\n        object.element.style.display = visible === true ? '' : 'none'\n\n        if (visible === true) {\n          object.onBeforeRender(_this, scene, camera)\n\n          let style\n\n          if (object.isCSS3DSprite) {\n            // http://swiftcoder.wordpress.com/2008/11/25/constructing-a-billboard-matrix/\n\n            _matrix.copy(camera.matrixWorldInverse)\n            _matrix.transpose()\n\n            if (object.rotation2D !== 0) _matrix.multiply(_matrix2.makeRotationZ(object.rotation2D))\n\n            object.matrixWorld.decompose(_position, _quaternion, _scale)\n            _matrix.setPosition(_position)\n            _matrix.scale(_scale)\n\n            _matrix.elements[3] = 0\n            _matrix.elements[7] = 0\n            _matrix.elements[11] = 0\n            _matrix.elements[15] = 1\n\n            style = getObjectCSSMatrix(_matrix)\n          } else {\n            style = getObjectCSSMatrix(object.matrixWorld)\n          }\n\n          const element = object.element\n          const cachedObject = cache.objects.get(object)\n\n          if (cachedObject === undefined || cachedObject.style !== style) {\n            element.style.transform = style\n\n            const objectData = { style: style }\n            cache.objects.set(object, objectData)\n          }\n\n          if (element.parentNode !== cameraElement) {\n            cameraElement.appendChild(element)\n          }\n\n          object.onAfterRender(_this, scene, camera)\n        }\n      }\n\n      for (let i = 0, l = object.children.length; i < l; i++) {\n        renderObject(object.children[i], scene, camera, cameraCSSMatrix)\n      }\n    }\n  }\n}\n\nexport { CSS3DObject, CSS3DSprite, CSS3DRenderer }\n"], "names": ["Vector3", "Quaternion", "Object3D", "Matrix4"], "mappings": ";;;AAMA,MAAM,YAA4B,oBAAIA,MAAAA,QAAS;AAC/C,MAAM,cAA8B,oBAAIC,MAAAA,WAAY;AACpD,MAAM,SAAyB,oBAAID,MAAAA,QAAS;AAE5C,MAAM,oBAAoBE,MAAAA,SAAS;AAAA,EACjC,YAAY,UAAU,SAAS,cAAc,KAAK,GAAG;AACnD,UAAO;AAEP,SAAK,gBAAgB;AAErB,SAAK,UAAU;AACf,SAAK,QAAQ,MAAM,WAAW;AAC9B,SAAK,QAAQ,MAAM,gBAAgB;AACnC,SAAK,QAAQ,MAAM,aAAa;AAEhC,SAAK,QAAQ,aAAa,aAAa,KAAK;AAE5C,SAAK,iBAAiB,WAAW,WAAY;AAC3C,WAAK,SAAS,SAAU,QAAQ;AAC9B,YAAI,OAAO,mBAAmB,WAAW,OAAO,QAAQ,eAAe,MAAM;AAC3E,iBAAO,QAAQ,WAAW,YAAY,OAAO,OAAO;AAAA,QACrD;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA,EAED,KAAK,QAAQ,WAAW;AACtB,UAAM,KAAK,QAAQ,SAAS;AAE5B,SAAK,UAAU,OAAO,QAAQ,UAAU,IAAI;AAE5C,WAAO;AAAA,EACR;AACH;AAEA,MAAM,oBAAoB,YAAY;AAAA,EACpC,YAAY,SAAS;AACnB,UAAM,OAAO;AAEb,SAAK,gBAAgB;AAErB,SAAK,aAAa;AAAA,EACnB;AAAA,EAED,KAAK,QAAQ,WAAW;AACtB,UAAM,KAAK,QAAQ,SAAS;AAE5B,SAAK,aAAa,OAAO;AAEzB,WAAO;AAAA,EACR;AACH;AAIA,MAAM,UAA0B,oBAAIC,MAAAA,QAAS;AAC7C,MAAM,WAA2B,oBAAIA,MAAAA,QAAS;AAE9C,MAAM,cAAc;AAAA,EAClB,YAAY,aAAa,IAAI;AAC3B,UAAM,QAAQ;AAEd,QAAI,QAAQ;AACZ,QAAI,YAAY;AAEhB,UAAM,QAAQ;AAAA,MACZ,QAAQ,EAAE,OAAO,GAAI;AAAA,MACrB,SAAS,oBAAI,QAAS;AAAA,IACvB;AAED,UAAM,aAAa,WAAW,YAAY,SAAY,WAAW,UAAU,SAAS,cAAc,KAAK;AAEvG,eAAW,MAAM,WAAW;AAE5B,SAAK,aAAa;AAElB,UAAM,cAAc,SAAS,cAAc,KAAK;AAChD,gBAAY,MAAM,kBAAkB;AACpC,gBAAY,MAAM,gBAAgB;AAClC,eAAW,YAAY,WAAW;AAElC,UAAM,gBAAgB,SAAS,cAAc,KAAK;AAElD,kBAAc,MAAM,iBAAiB;AAErC,gBAAY,YAAY,aAAa;AAErC,SAAK,UAAU,WAAY;AACzB,aAAO;AAAA,QACL,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,IACF;AAED,SAAK,SAAS,SAAU,OAAO,QAAQ;AACrC,YAAM,MAAM,OAAO,iBAAiB,SAAS,CAAC,IAAI;AAElD,UAAI,OAAO,QAAQ,OAAO,KAAK,SAAS;AAEtC,oBAAY,MAAM,YAAY,cAAc,CAAC,OAAO,KAAK,WAAW,SAAS,OAAO,KAAK,aACvF,CAAC,OAAO,KAAK,WAAW,UAAU,OAAO,KAAK;AAIhD,oBAAY,MAAM,aAAa,UAAU,OAAO,KAAK,YAAY,OAAO,KAAK,UAC3E,OAAO,KAAK,aAAa,OAAO,KAAK;AAAA,MAE/C,OAAa;AACL,oBAAY,MAAM,YAAY;AAAA,MAC/B;AAED,UAAI,MAAM,0BAA0B;AAAM,cAAM,kBAAmB;AACnE,UAAI,OAAO,WAAW,QAAQ,OAAO,0BAA0B;AAAM,eAAO,kBAAmB;AAE/F,UAAI,IAAI;AAER,UAAI,OAAO,sBAAsB;AAC/B,aAAK,EAAE,OAAO,QAAQ,OAAO,QAAQ;AACrC,cAAM,OAAO,MAAM,OAAO,UAAU;AAAA,MACrC;AAED,YAAM,oBAAoB,OAAO,QAAQ,OAAO,KAAK,UAAU,OAAO,KAAK,SAAS,OAAO,KAAK,aAAa;AAC7G,YAAM,kBAAkB,OAAO,uBAC3B,UAAU,8BAEV,MACA,gBAEA,QAAQ,EAAE,IACV,QACA,QAAQ,EAAE,IACV,QACA,mBAAmB,OAAO,kBAAkB,IAC5C,UAAU,mCAAwC,MAAM,QAAQ,mBAAmB,OAAO,kBAAkB;AAChH,YAAM,cAAc,OAAO,sBAAsB,iBAAiB,MAAM,SAAS;AAEjF,YAAM,QAAQ,cAAc,kBAAkB,eAAe,aAAa,QAAQ,cAAc;AAEhG,UAAI,MAAM,OAAO,UAAU,OAAO;AAChC,sBAAc,MAAM,YAAY;AAEhC,cAAM,OAAO,QAAQ;AAAA,MACtB;AAED,mBAAa,OAAO,OAAO,MAAuB;AAAA,IACnD;AAED,SAAK,UAAU,SAAU,OAAO,QAAQ;AACtC,eAAS;AACT,gBAAU;AACV,mBAAa,SAAS;AACtB,oBAAc,UAAU;AAExB,iBAAW,MAAM,QAAQ,QAAQ;AACjC,iBAAW,MAAM,SAAS,SAAS;AAEnC,kBAAY,MAAM,QAAQ,QAAQ;AAClC,kBAAY,MAAM,SAAS,SAAS;AAEpC,oBAAc,MAAM,QAAQ,QAAQ;AACpC,oBAAc,MAAM,SAAS,SAAS;AAAA,IACvC;AAED,aAAS,QAAQ,OAAO;AACtB,aAAO,KAAK,IAAI,KAAK,IAAI,QAAQ,IAAI;AAAA,IACtC;AAED,aAAS,mBAAmB,QAAQ;AAClC,YAAM,WAAW,OAAO;AAExB,aACE,cACA,QAAQ,SAAS,CAAC,CAAC,IACnB,MACA,QAAQ,CAAC,SAAS,CAAC,CAAC,IACpB,MACA,QAAQ,SAAS,CAAC,CAAC,IACnB,MACA,QAAQ,SAAS,CAAC,CAAC,IACnB,MACA,QAAQ,SAAS,CAAC,CAAC,IACnB,MACA,QAAQ,CAAC,SAAS,CAAC,CAAC,IACpB,MACA,QAAQ,SAAS,CAAC,CAAC,IACnB,MACA,QAAQ,SAAS,CAAC,CAAC,IACnB,MACA,QAAQ,SAAS,CAAC,CAAC,IACnB,MACA,QAAQ,CAAC,SAAS,CAAC,CAAC,IACpB,MACA,QAAQ,SAAS,EAAE,CAAC,IACpB,MACA,QAAQ,SAAS,EAAE,CAAC,IACpB,MACA,QAAQ,SAAS,EAAE,CAAC,IACpB,MACA,QAAQ,CAAC,SAAS,EAAE,CAAC,IACrB,MACA,QAAQ,SAAS,EAAE,CAAC,IACpB,MACA,QAAQ,SAAS,EAAE,CAAC,IACpB;AAAA,IAEH;AAED,aAAS,mBAAmB,QAAQ;AAClC,YAAM,WAAW,OAAO;AACxB,YAAM,WACJ,cACA,QAAQ,SAAS,CAAC,CAAC,IACnB,MACA,QAAQ,SAAS,CAAC,CAAC,IACnB,MACA,QAAQ,SAAS,CAAC,CAAC,IACnB,MACA,QAAQ,SAAS,CAAC,CAAC,IACnB,MACA,QAAQ,CAAC,SAAS,CAAC,CAAC,IACpB,MACA,QAAQ,CAAC,SAAS,CAAC,CAAC,IACpB,MACA,QAAQ,CAAC,SAAS,CAAC,CAAC,IACpB,MACA,QAAQ,CAAC,SAAS,CAAC,CAAC,IACpB,MACA,QAAQ,SAAS,CAAC,CAAC,IACnB,MACA,QAAQ,SAAS,CAAC,CAAC,IACnB,MACA,QAAQ,SAAS,EAAE,CAAC,IACpB,MACA,QAAQ,SAAS,EAAE,CAAC,IACpB,MACA,QAAQ,SAAS,EAAE,CAAC,IACpB,MACA,QAAQ,SAAS,EAAE,CAAC,IACpB,MACA,QAAQ,SAAS,EAAE,CAAC,IACpB,MACA,QAAQ,SAAS,EAAE,CAAC,IACpB;AAEF,aAAO,yBAAyB;AAAA,IACjC;AAED,aAAS,aAAa,QAAQ,OAAO,QAAQ,iBAAiB;AAC5D,UAAI,OAAO,eAAe;AACxB,cAAM,UAAU,OAAO,YAAY,QAAQ,OAAO,OAAO,KAAK,OAAO,MAAM,MAAM;AACjF,eAAO,QAAQ,MAAM,UAAU,YAAY,OAAO,KAAK;AAEvD,YAAI,YAAY,MAAM;AACpB,iBAAO,eAAe,OAAO,OAAO,MAAM;AAE1C,cAAI;AAEJ,cAAI,OAAO,eAAe;AAGxB,oBAAQ,KAAK,OAAO,kBAAkB;AACtC,oBAAQ,UAAW;AAEnB,gBAAI,OAAO,eAAe;AAAG,sBAAQ,SAAS,SAAS,cAAc,OAAO,UAAU,CAAC;AAEvF,mBAAO,YAAY,UAAU,WAAW,aAAa,MAAM;AAC3D,oBAAQ,YAAY,SAAS;AAC7B,oBAAQ,MAAM,MAAM;AAEpB,oBAAQ,SAAS,CAAC,IAAI;AACtB,oBAAQ,SAAS,CAAC,IAAI;AACtB,oBAAQ,SAAS,EAAE,IAAI;AACvB,oBAAQ,SAAS,EAAE,IAAI;AAEvB,oBAAQ,mBAAmB,OAAO;AAAA,UAC9C,OAAiB;AACL,oBAAQ,mBAAmB,OAAO,WAAW;AAAA,UAC9C;AAED,gBAAM,UAAU,OAAO;AACvB,gBAAM,eAAe,MAAM,QAAQ,IAAI,MAAM;AAE7C,cAAI,iBAAiB,UAAa,aAAa,UAAU,OAAO;AAC9D,oBAAQ,MAAM,YAAY;AAE1B,kBAAM,aAAa,EAAE,MAAc;AACnC,kBAAM,QAAQ,IAAI,QAAQ,UAAU;AAAA,UACrC;AAED,cAAI,QAAQ,eAAe,eAAe;AACxC,0BAAc,YAAY,OAAO;AAAA,UAClC;AAED,iBAAO,cAAc,OAAO,OAAO,MAAM;AAAA,QAC1C;AAAA,MACF;AAED,eAAS,IAAI,GAAG,IAAI,OAAO,SAAS,QAAQ,IAAI,GAAG,KAAK;AACtD,qBAAa,OAAO,SAAS,CAAC,GAAG,OAAO,MAAuB;AAAA,MAChE;AAAA,IACF;AAAA,EACF;AACH;;;;"}