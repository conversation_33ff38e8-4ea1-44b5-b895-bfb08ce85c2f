import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type TaskDocument = Task & Document;

export enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  REVIEW = 'review',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  ON_HOLD = 'on_hold',
}

export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

export enum TaskType {
  PROJECT_TASK = 'project_task',
  MILESTONE = 'milestone',
  INSPECTION = 'inspection',
  APPROVAL = 'approval',
  PAYMENT = 'payment',
  COMMUNICATION = 'communication',
  DOCUMENTATION = 'documentation',
  OTHER = 'other',
}

@Schema({ _id: false })
export class TaskAssignee {
  @Prop({ type: Types.ObjectId, required: true, ref: 'User' })
  @ApiProperty({ description: '被分配者ID' })
  userId: Types.ObjectId;

  @Prop({ default: Date.now })
  @ApiProperty({ description: '分配时间' })
  assignedAt: Date;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  @ApiProperty({ description: '分配者ID' })
  assignedBy?: Types.ObjectId;

  @Prop()
  @ApiProperty({ description: '分配备注' })
  note?: string;
}

@Schema({ _id: false })
export class TaskComment {
  @Prop({ type: Types.ObjectId, required: true, ref: 'User' })
  @ApiProperty({ description: '评论者ID' })
  authorId: Types.ObjectId;

  @Prop({ required: true })
  @ApiProperty({ description: '评论内容' })
  content: string;

  @Prop([String])
  @ApiProperty({ description: '附件', type: [String] })
  attachments?: string[];

  @Prop({ default: Date.now })
  @ApiProperty({ description: '评论时间' })
  createdAt: Date;

  @Prop({ default: false })
  @ApiProperty({ description: '是否已编辑' })
  isEdited: boolean;

  @Prop()
  @ApiProperty({ description: '编辑时间' })
  editedAt?: Date;
}

@Schema({ _id: false })
export class TaskTimeLog {
  @Prop({ type: Types.ObjectId, required: true, ref: 'User' })
  @ApiProperty({ description: '用户ID' })
  userId: Types.ObjectId;

  @Prop({ required: true })
  @ApiProperty({ description: '开始时间' })
  startTime: Date;

  @Prop()
  @ApiProperty({ description: '结束时间' })
  endTime?: Date;

  @Prop()
  @ApiProperty({ description: '工作描述' })
  description?: string;

  @Prop({ default: 0 })
  @ApiProperty({ description: '工作时长（分钟）' })
  duration: number;
}

@Schema({ timestamps: true })
export class Task {
  @Prop({ required: true })
  @ApiProperty({ description: '任务标题' })
  title: string;

  @Prop()
  @ApiProperty({ description: '任务描述' })
  description?: string;

  @Prop({ enum: TaskType, default: TaskType.PROJECT_TASK })
  @ApiProperty({ description: '任务类型', enum: TaskType })
  type: TaskType;

  @Prop({ enum: TaskStatus, default: TaskStatus.TODO })
  @ApiProperty({ description: '任务状态', enum: TaskStatus })
  status: TaskStatus;

  @Prop({ enum: TaskPriority, default: TaskPriority.MEDIUM })
  @ApiProperty({ description: '任务优先级', enum: TaskPriority })
  priority: TaskPriority;

  @Prop({ type: Types.ObjectId, required: true, ref: 'User' })
  @ApiProperty({ description: '创建者ID' })
  createdBy: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Project' })
  @ApiProperty({ description: '关联项目ID' })
  projectId?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Quote' })
  @ApiProperty({ description: '关联报价ID' })
  quoteId?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Task' })
  @ApiProperty({ description: '父任务ID' })
  parentTaskId?: Types.ObjectId;

  @Prop([TaskAssignee])
  @ApiProperty({ description: '任务分配', type: [TaskAssignee] })
  assignees?: TaskAssignee[];

  @Prop()
  @ApiProperty({ description: '预计开始时间' })
  estimatedStartDate?: Date;

  @Prop()
  @ApiProperty({ description: '预计结束时间' })
  estimatedEndDate?: Date;

  @Prop()
  @ApiProperty({ description: '实际开始时间' })
  actualStartDate?: Date;

  @Prop()
  @ApiProperty({ description: '实际结束时间' })
  actualEndDate?: Date;

  @Prop({ default: 0 })
  @ApiProperty({ description: '预计工时（小时）' })
  estimatedHours: number;

  @Prop({ default: 0 })
  @ApiProperty({ description: '实际工时（小时）' })
  actualHours: number;

  @Prop({ default: 0, min: 0, max: 100 })
  @ApiProperty({ description: '完成进度（百分比）', minimum: 0, maximum: 100 })
  progress: number;

  @Prop([String])
  @ApiProperty({ description: '任务标签', type: [String] })
  tags?: string[];

  @Prop([String])
  @ApiProperty({ description: '附件', type: [String] })
  attachments?: string[];

  @Prop([TaskComment])
  @ApiProperty({ description: '任务评论', type: [TaskComment] })
  comments?: TaskComment[];

  @Prop([TaskTimeLog])
  @ApiProperty({ description: '时间记录', type: [TaskTimeLog] })
  timeLogs?: TaskTimeLog[];

  @Prop([String])
  @ApiProperty({ description: '依赖任务ID', type: [String] })
  dependencies?: string[];

  @Prop([String])
  @ApiProperty({ description: '阻塞任务ID', type: [String] })
  blockers?: string[];

  @Prop()
  @ApiProperty({ description: '截止时间' })
  dueDate?: Date;

  @Prop({ default: false })
  @ApiProperty({ description: '是否重要' })
  isImportant: boolean;

  @Prop({ default: false })
  @ApiProperty({ description: '是否紧急' })
  isUrgent: boolean;

  @Prop({ default: false })
  @ApiProperty({ description: '是否可重复' })
  isRecurring: boolean;

  @Prop()
  @ApiProperty({ description: '重复规则' })
  recurringRule?: string;

  @Prop()
  @ApiProperty({ description: '备注' })
  notes?: string;

  @Prop({ type: Object, default: {} })
  @ApiProperty({ description: '元数据' })
  metadata?: Record<string, any>;

  @Prop({ default: false })
  @ApiProperty({ description: '是否已删除' })
  isDeleted: boolean;

  @Prop()
  @ApiProperty({ description: '删除时间' })
  deletedAt?: Date;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  @ApiProperty({ description: '删除者ID' })
  deletedBy?: Types.ObjectId;
}

export const TaskSchema = SchemaFactory.createForClass(Task);

// 创建索引
TaskSchema.index({ createdBy: 1, createdAt: -1 });
TaskSchema.index({ projectId: 1, status: 1 });
TaskSchema.index({ 'assignees.userId': 1, status: 1 });
TaskSchema.index({ status: 1, priority: 1 });
TaskSchema.index({ type: 1, status: 1 });
TaskSchema.index({ dueDate: 1, status: 1 });
TaskSchema.index({ parentTaskId: 1 });
TaskSchema.index({ isDeleted: 1, createdAt: -1 });

// 复合索引
TaskSchema.index({ projectId: 1, status: 1, priority: 1 });
TaskSchema.index({ 'assignees.userId': 1, dueDate: 1, status: 1 });

// 虚拟字段
TaskSchema.virtual('isOverdue').get(function() {
  return this.dueDate && new Date() > this.dueDate && this.status !== TaskStatus.COMPLETED;
});

TaskSchema.virtual('isCompleted').get(function() {
  return this.status === TaskStatus.COMPLETED;
});

TaskSchema.virtual('isInProgress').get(function() {
  return this.status === TaskStatus.IN_PROGRESS;
});

TaskSchema.virtual('totalTimeLogged').get(function() {
  if (!this.timeLogs || this.timeLogs.length === 0) {
    return 0;
  }
  return this.timeLogs.reduce((total, log) => total + log.duration, 0);
});

TaskSchema.virtual('assigneeCount').get(function() {
  return this.assignees ? this.assignees.length : 0;
});

TaskSchema.virtual('commentCount').get(function() {
  return this.comments ? this.comments.length : 0;
});

TaskSchema.virtual('attachmentCount').get(function() {
  return this.attachments ? this.attachments.length : 0;
});

TaskSchema.virtual('daysUntilDue').get(function() {
  if (!this.dueDate) return null;
  const now = new Date();
  const due = new Date(this.dueDate);
  const diffTime = due.getTime() - now.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// 设置虚拟字段在JSON序列化时包含
TaskSchema.set('toJSON', { virtuals: true });
TaskSchema.set('toObject', { virtuals: true });
