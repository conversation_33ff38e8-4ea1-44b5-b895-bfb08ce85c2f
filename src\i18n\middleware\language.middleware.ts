import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class LanguageMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // 从不同来源获取语言设置
    const langFromQuery = req.query?.lang as string;
    const langFromHeader = req.headers['x-lang'] as string;
    const langFromAcceptLanguage = req.headers['accept-language'] as string;
    
    let detectedLang = 'en'; // 默认语言
    
    // 优先级：查询参数 > 自定义头 > Accept-Language
    if (langFromQuery && ['en', 'zh'].includes(langFromQuery)) {
      detectedLang = langFromQuery;
    } else if (langFromHeader && ['en', 'zh'].includes(langFromHeader)) {
      detectedLang = langFromHeader;
    } else if (langFromAcceptLanguage) {
      // 解析 Accept-Language 头
      const languages = langFromAcceptLanguage
        .split(',')
        .map(lang => lang.split(';')[0].trim().toLowerCase());
      
      for (const lang of languages) {
        if (lang.startsWith('zh')) {
          detectedLang = 'zh';
          break;
        }
        if (lang.startsWith('en')) {
          detectedLang = 'en';
          break;
        }
      }
    }
    
    // 将检测到的语言添加到请求对象中
    (req as any).language = detectedLang;
    
    // 设置响应头，告知客户端当前使用的语言
    res.setHeader('Content-Language', detectedLang);
    
    next();
  }
}
