{"version": 3, "file": "i18n.module.js", "sourceRoot": "", "sources": ["../src/i18n.module.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CASwB;AACxB,qDAQ0B;AAC1B,0DAAsD;AAatD,wFAAmF;AACnF,uCAAgE;AAChE,6CAA2D;AAC3D,mCAKiB;AAEjB,uDAAmD;AACnD,+BAAuE;AACvE,wCAAwC;AACxC,uCAA2C;AAC3C,mEAA+D;AAC/D,yBAAyB;AACzB,6BAA6B;AAChB,QAAA,MAAM,GAAG,IAAI,eAAM,CAAC,aAAa,CAAC,CAAC;AAEhD,MAAM,cAAc,GAAyB;IAC3C,SAAS,EAAE,EAAE;IACb,SAAS,EAAE,MAAM;IACjB,OAAO,EAAE,IAAI;IACb,iBAAiB,EAAE,KAAK;IACxB,MAAM,EAAE,wBAAc;CACvB,CAAC;AAIK,IAAM,UAAU,kBAAhB,MAAM,UAAU;IAGrB,YACmB,IAAiB,EAE1B,YAAyC,EACV,WAAwB,EACvD,OAAwB;QAJf,SAAI,GAAJ,IAAI,CAAa;QAE1B,iBAAY,GAAZ,YAAY,CAA6B;QACV,gBAAW,GAAX,WAAW,CAAa;QACvD,YAAO,GAAP,OAAO,CAAiB;QAP1B,gBAAW,GAAG,IAAI,cAAO,EAAQ,CAAC;IAQvC,CAAC;IAEJ,KAAK,CAAC,YAAY;QAEhB,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QAG1B,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,IAAI,KAAK,EAAE;YACxC,IAAI;gBACF,MAAM,GAAG,GAAG,2CAAa,KAAK,EAAC,CAAC;gBAChC,GAAG,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC7C,cAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;aAC5C;YAAC,OAAO,CAAC,EAAE;gBACV,cAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,CAAC,CAAC,CAAC;aAC9C;SACF;QAED,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE;YACxD,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;YACnD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAW,EAAE,IAAS,EAAE,IAAS,EAAE,EAAE;gBACtD,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;YAC1C,CAAC,CAAC;SACH;QAED,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE;YACtC,IAAI;gBACF,MAAM,EAAE,GAAG,2CAAa,oBAAoB,EAAC,CAAC;gBAE9C,IAAI,CAAC,YAAY;qBACd,IAAI,CAAC,IAAA,gBAAS,EAAC,IAAI,CAAC,WAAW,CAAC,CAAC;qBACjC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;oBACrB,cAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;oBAC3C,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAClC,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,IAAA,iBAAS,EAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAC1C,EAAE,CACH,CAAC;oBAEF,MAAM,UAAU,GAAG,MAAM,EAAE,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;oBAEpD,IAAI,CAAC,UAAU,EAAE;wBACf,OAAO;qBACR;oBAED,MAAM,UAAU,GAAG,EAAE,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;oBAErD,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,EAAE;wBAC3D,SAAS,EAAE,IAAI;qBAChB,CAAC,CAAC;oBACH,IAAI,kBAAkB,GAAG,IAAI,CAAC;oBAC9B,IAAI;wBACF,kBAAkB,GAAG,EAAE,CAAC,YAAY,CAClC,IAAI,CAAC,WAAW,CAAC,eAAe,EAChC,MAAM,CACP,CAAC;qBACH;oBAAC,OAAO,GAAG,EAAE;wBACZ,cAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;qBACnB;oBACD,IAAI,kBAAkB,IAAI,UAAU,EAAE;wBACpC,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;wBAC/D,cAAM,CAAC,GAAG,CACR,uBAAuB,IAAI,CAAC,WAAW,CAAC,eAAe;;iBAEtD,CACF,CAAC;qBACH;yBAAM;wBACL,cAAM,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;qBACnC;gBACH,CAAC,CAAC,CAAC;aACN;YAAC,OAAO,CAAC,EAAE;aAEX;SACF;IACH,CAAC;IAED,eAAe;QACb,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;IAC9B,CAAC;IAED,SAAS,CAAC,QAA4B;QACpC,IAAI,IAAI,CAAC,WAAW,CAAC,iBAAiB;YAAE,OAAO;QAE/C,QAAQ;aACL,KAAK,CAAC,gCAAc,CAAC;aACrB,SAAS,CACR,IAAA,wBAAgB,EAAC,QAAQ,CAAC,IAAI,IAAA,oBAAY,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CACpE,CAAC;IACN,CAAC;IAED,MAAM,CAAC,OAAO,CAAC,OAAoB;QACjC,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAE5C,MAAM,oBAAoB,GAAG,IAAI,sBAAe,CAAW,EAAE,CAAC,CAAC;QAC/D,MAAM,sBAAsB,GAAG,IAAI,sBAAe,CAAkB,EAAE,CAAC,CAAC;QAExE,MAAM,WAAW,GAAkB;YACjC,OAAO,EAAE,6BAAY;YACrB,QAAQ,EAAE,OAAO;SAClB,CAAC;QAEF,MAAM,kBAAkB,GAAkB;YACxC,OAAO,EAAE,wBAAU;YACnB,QAAQ,EAAE,OAAO,CAAC,MAAM;SACzB,CAAC;QAEF,MAAM,yBAAyB,GAAkB;YAC/C,OAAO,EAAE,oCAAmB;YAC5B,QAAQ,EAAE,OAAO,CAAC,aAAa;SAChC,CAAC;QAEF,MAAM,4BAA4B,GAAkB;YAClD,OAAO,EAAE,uCAAsB;YAC/B,QAAQ,EAAE,oBAAoB;SAC/B,CAAC;QAEF,MAAM,8BAA8B,GAAkB;YACpD,OAAO,EAAE,0CAAyB;YAClC,QAAQ,EAAE,sBAAsB;SACjC,CAAC;QAEF,MAAM,oBAAoB,GAAG;YAC3B,OAAO,EAAE,kCAAiB;YAC1B,UAAU,EAAE,KAAK,EACf,MAAkB,EACoB,EAAE;gBACxC,IAAI;oBACF,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;oBACxC,IAAI,WAAW,YAAY,iBAAU,EAAE;wBACrC,WAAW,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;qBAC/C;yBAAM;wBACL,sBAAsB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;qBAC1C;iBACF;gBAAC,OAAO,CAAC,EAAE;oBACV,cAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,CAAC,CAAC,CAAC;iBAC9C;gBACD,OAAO,sBAAsB,CAAC,YAAY,EAAE,CAAC;YAC/C,CAAC;YACD,MAAM,EAAE,CAAC,wBAAU,CAAC;SACrB,CAAC;QAEF,MAAM,iBAAiB,GAAG;YACxB,OAAO,EAAE,+BAAc;YACvB,UAAU,EAAE,KAAK,EAAE,MAAkB,EAAiC,EAAE;gBACtE,IAAI;oBACF,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,SAAS,EAAE,CAAC;oBAC3C,IAAI,SAAS,YAAY,iBAAU,EAAE;wBACnC,SAAS,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;qBAC3C;yBAAM;wBACL,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;qBACtC;iBACF;gBAAC,OAAO,CAAC,EAAE;oBACV,cAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,CAAC,CAAC,CAAC;iBAC9C;gBACD,OAAO,oBAAoB,CAAC,YAAY,EAAE,CAAC;YAC7C,CAAC;YACD,MAAM,EAAE,CAAC,wBAAU,CAAC;SACrB,CAAC;QAEF,MAAM,iBAAiB,GAAG;YACxB,OAAO,EAAE,+BAAc;YACvB,QAAQ,EAAE,OAAO,CAAC,SAAS,IAAI,EAAE;SAClC,CAAC;QAEF,OAAO;YACL,MAAM,EAAE,YAAU;YAClB,SAAS,EAAE;gBACT,EAAE,OAAO,EAAE,eAAM,EAAE,QAAQ,EAAE,cAAM,EAAE;gBACrC;oBACE,OAAO,EAAE,sBAAe;oBACxB,QAAQ,EAAE,mDAAuB;iBAClC;gBACD,0BAAW;gBACX,WAAW;gBACX,oBAAoB;gBACpB,iBAAiB;gBACjB,iBAAiB;gBACjB,kBAAkB;gBAClB,yBAAyB;gBACzB,4BAA4B;gBAC5B,8BAA8B;gBAC9B,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,SAAS,CAAC;aACnD;YACD,OAAO,EAAE,CAAC,6BAAY,EAAE,+BAAc,EAAE,0BAAW,EAAE,iBAAiB,CAAC;SACxE,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,OAAyB;QAC3C,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAE5C,MAAM,oBAAoB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;QACtE,MAAM,wBAAwB,GAAG,IAAI,CAAC,8BAA8B,EAAE,CAAC;QACvE,MAAM,sBAAsB,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACnE,MAAM,0BAA0B,GAAG,IAAI,CAAC,gCAAgC,EAAE,CAAC;QAE3E,MAAM,oBAAoB,GAAG,IAAI,sBAAe,CAAW,EAAE,CAAC,CAAC;QAC/D,MAAM,sBAAsB,GAAG,IAAI,sBAAe,CAAkB,EAAE,CAAC,CAAC;QAExE,MAAM,iBAAiB,GAAkB;YACvC,OAAO,EAAE,+BAAc;YACvB,QAAQ,EAAE,OAAO,CAAC,SAAS,IAAI,EAAE;SAClC,CAAC;QAEF,MAAM,kBAAkB,GAA8B;YACpD,OAAO,EAAE,wBAAU;YACnB,QAAQ,EAAE,OAAO,CAAC,MAAM;SACzB,CAAC;QAEF,MAAM,4BAA4B,GAAkB;YAClD,OAAO,EAAE,uCAAsB;YAC/B,QAAQ,EAAE,oBAAoB;SAC/B,CAAC;QAEF,MAAM,8BAA8B,GAAkB;YACpD,OAAO,EAAE,0CAAyB;YAClC,QAAQ,EAAE,sBAAsB;SACjC,CAAC;QAEF,OAAO;YACL,MAAM,EAAE,YAAU;YAClB,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE;YAC9B,SAAS,EAAE;gBACT,EAAE,OAAO,EAAE,eAAM,EAAE,QAAQ,EAAE,cAAM,EAAE;gBACrC;oBACE,OAAO,EAAE,sBAAe;oBACxB,QAAQ,EAAE,mDAAuB;iBAClC;gBACD,oBAAoB;gBACpB,wBAAwB;gBACxB,sBAAsB;gBACtB,0BAA0B;gBAC1B,0BAAW;gBACX,iBAAiB;gBACjB,kBAAkB;gBAClB,4BAA4B;gBAC5B,8BAA8B;gBAC9B,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,SAAS,CAAC;aACnD;YACD,OAAO,EAAE;gBACP,6BAAY;gBACZ,+BAAc;gBACd,0BAAW;gBACX,sBAAsB;aACvB;SACF,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,0BAA0B,CACvC,OAAyB;QAEzB,IAAI,OAAO,CAAC,UAAU,EAAE;YACtB,OAAO;gBACL,OAAO,EAAE,6BAAY;gBACrB,UAAU,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,EAAE;oBAC5B,OAAO,IAAI,CAAC,mBAAmB,CAC7B,CAAC,MAAM,OAAO,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,CAAQ,CAC3C,CAAC;gBACJ,CAAC;gBACD,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,EAAE;aAC7B,CAAC;SACH;QACD,OAAO;YACL,OAAO,EAAE,6BAAY;YACrB,UAAU,EAAE,KAAK,EAAE,cAAkC,EAAE,EAAE,CACvD,IAAI,CAAC,mBAAmB,CACtB,CAAC,MAAM,cAAc,CAAC,iBAAiB,EAAE,CAAQ,CAClD;YACH,MAAM,EAAE,CAAC,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,WAAW,CAAC;SAClD,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,gCAAgC;QAC7C,OAAO;YACL,OAAO,EAAE,oCAAmB;YAC5B,UAAU,EAAE,KAAK,EAAE,OAAoB,EAAgB,EAAE;gBACvD,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YACzD,CAAC;YACD,MAAM,EAAE,CAAC,6BAAY,CAAC;SACvB,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,8BAA8B;QAC3C,OAAO;YACL,OAAO,EAAE,kCAAiB;YAC1B,UAAU,EAAE,KAAK,EACf,MAAkB,EAClB,mBAAqD,EACf,EAAE;gBACxC,IAAI;oBACF,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;oBACxC,IAAI,WAAW,YAAY,iBAAU,EAAE;wBACrC,WAAW,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;qBAC5C;yBAAM;wBACL,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;qBACvC;iBACF;gBAAC,OAAO,CAAC,EAAE;oBACV,cAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,CAAC,CAAC,CAAC;iBAC9C;gBACD,OAAO,mBAAmB,CAAC,YAAY,EAAE,CAAC;YAC5C,CAAC;YACD,MAAM,EAAE,CAAC,wBAAU,EAAE,0CAAyB,CAAC;SAChD,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,4BAA4B;QACzC,OAAO;YACL,OAAO,EAAE,+BAAc;YACvB,UAAU,EAAE,KAAK,EACf,MAAkB,EAClB,gBAA2C,EACZ,EAAE;gBACjC,IAAI;oBACF,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,SAAS,EAAE,CAAC;oBAC3C,IAAI,SAAS,YAAY,iBAAU,EAAE;wBACnC,SAAS,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;qBACvC;yBAAM;wBACL,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;qBAClC;iBACF;gBAAC,OAAO,CAAC,EAAE;oBACV,cAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,CAAC,CAAC,CAAC;iBAC9C;gBACD,OAAO,gBAAgB,CAAC,YAAY,EAAE,CAAC;YACzC,CAAC;YACD,MAAM,EAAE,CAAC,wBAAU,EAAE,uCAAsB,CAAC;SAC7C,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,mBAAmB,CAChC,OAAU;QAEV,OAAO,GAAG,EAAE,GAAG,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC;QAC5C,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,MAAM,CAAC,uBAAuB,CAAC,SAAgC;QACrE,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACxC,cAAM,CAAC,KAAK,CACV,kIAAkI,CACnI,CAAC;SACH;QACD,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;aACrB,MAAM,CAAC,qBAAa,CAAC;aACrB,MAAM,CAAa,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE;YACnC,IAAI,CAAC,CAAC,KAAK,CAAC,EAAE;gBACZ,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG,CAAQ,CAAC;gBACrD,MAAM,YAAY,GAAG,IAAA,wCAA2B,EAC9C,QAAiC,CAClC,CAAC;gBACF,SAAS,CAAC,IAAI,CAAC;oBACb,OAAO,EAAE,QAAQ;oBACjB,QAAQ,EAAE,QAAQ;iBACnB,CAAC,CAAC;gBACH,IAAI,OAAO,EAAE;oBACV,IAAY,CAAC,QAAQ,GAAG,OAAO,CAAC;iBAClC;gBACD,SAAS,CAAC,IAAI,CAAC;oBACb,OAAO,EAAE,YAAY;oBACrB,GAAI,IAAY;iBACjB,CAAC,CAAC;aACJ;iBAAM;gBACL,MAAM,YAAY,GAAG,IAAA,wCAA2B,EAC9C,CAA0B,CAC3B,CAAC;gBACF,SAAS,CAAC,IAAI,CAAC;oBACb,OAAO,EAAE,CAAC;oBACV,QAAQ,EAAE,CAAC;oBACX,MAAM,EAAE,CAAC,YAAY,CAAC;iBAChB,CAAC,CAAC;gBACV,SAAS,CAAC,IAAI,CAAC;oBACb,OAAO,EAAE,YAAY;oBACrB,UAAU,EAAE,GAAG,EAAE,CAAC,SAAS;iBAC5B,CAAC,CAAC;aACJ;YAED,OAAO,SAAS,CAAC;QACnB,CAAC,EAAE,EAAE,CAAC,CAAC;IACX,CAAC;CACF,CAAA;AAhYY,UAAU;IAFtB,IAAA,eAAM,GAAE;IACR,IAAA,eAAM,EAAC,EAAE,CAAC;IAMN,WAAA,IAAA,eAAM,EAAC,kCAAiB,CAAC,CAAA;IAEzB,WAAA,IAAA,eAAM,EAAC,6BAAY,CAAC,CAAA;qCAHE,0BAAW;QAEZ,iBAAU,UAEf,sBAAe;GARvB,UAAU,CAgYtB;AAhYY,gCAAU"}