{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "edit": "Edit", "delete": "Delete", "create": "Create", "update": "Update", "search": "Search", "filter": "Filter", "reset": "Reset", "submit": "Submit", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "view": "View", "download": "Download", "upload": "Upload"}, "navigation": {"home": "Home", "projects": "Projects", "contractors": "Contractors", "profile": "Profile", "dashboard": "Dashboard", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "settings": "Settings"}, "auth": {"login": {"title": "Login to HomeReno", "subtitle": "Welcome back! Please sign in to your account.", "email": "Email Address", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "loginButton": "Sign In", "noAccount": "Don't have an account?", "signUp": "Sign up here"}, "register": {"title": "Create Account", "subtitle": "Join <PERSON> and find the perfect contractor for your project.", "firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "phone": "Phone Number", "password": "Password", "confirmPassword": "Confirm Password", "role": "I am a", "homeowner": "Homeowner", "contractor": "Contractor", "terms": "I agree to the Terms of Service and Privacy Policy", "registerButton": "Create Account", "hasAccount": "Already have an account?", "signIn": "Sign in here"}, "errors": {"invalidCredentials": "Invalid email or password", "emailRequired": "Email is required", "passwordRequired": "Password is required", "passwordTooShort": "Password must be at least 8 characters", "passwordsNotMatch": "Passwords do not match", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "roleRequired": "Please select your role", "termsRequired": "You must agree to the terms"}}, "projects": {"title": "Projects", "createProject": "Create New Project", "myProjects": "My Projects", "allProjects": "All Projects", "projectDetails": "Project Details", "categories": {"kitchen_renovation": "Kitchen Renovation", "bathroom_renovation": "Bathroom Renovation", "basement_finishing": "Basement Finishing", "flooring": "Flooring", "painting": "Painting", "electrical": "Electrical", "plumbing": "Plumbing", "roofing": "Roofing", "landscaping": "Landscaping", "other": "Other"}, "status": {"draft": "Draft", "published": "Published", "in_progress": "In Progress", "completed": "Completed", "cancelled": "Cancelled"}, "form": {"title": "Project Title", "description": "Description", "category": "Category", "budget": "Budget", "budgetMin": "Minimum Budget", "budgetMax": "Maximum Budget", "timeline": "Timeline", "startDate": "Start Date", "endDate": "End Date", "location": "Location", "requirements": "Requirements", "images": "Images"}}, "contractors": {"title": "Contractors", "findContractors": "Find Contractors", "contractorProfile": "Contractor Profile", "services": "Services", "experience": "Experience", "rating": "Rating", "reviews": "Reviews", "portfolio": "Portfolio", "contact": "Contact", "requestQuote": "Request Quote", "verified": "Verified", "insurance": "Insured"}, "quotes": {"title": "Quotes", "requestQuote": "Request Quote", "viewQuote": "View Quote", "acceptQuote": "Accept Quote", "rejectQuote": "Reject Quote", "amount": "Amount", "timeline": "Timeline", "breakdown": "Breakdown", "terms": "Terms & Conditions", "validUntil": "<PERSON>id <PERSON>", "status": {"pending": "Pending", "accepted": "Accepted", "rejected": "Rejected", "expired": "Expired"}}, "profile": {"title": "Profile", "editProfile": "Edit Profile", "personalInfo": "Personal Information", "contactInfo": "Contact Information", "address": "Address", "preferences": "Preferences", "security": "Security", "changePassword": "Change Password"}, "notifications": {"title": "Notifications", "markAsRead": "<PERSON> <PERSON>", "markAllAsRead": "<PERSON> as <PERSON>", "noNotifications": "No notifications", "types": {"project_update": "Project Update", "new_quote": "New Quote", "quote_accepted": "Quote Accepted", "payment_received": "Payment Received", "system": "System"}}, "errors": {"generic": "Something went wrong. Please try again.", "network": "Network error. Please check your connection.", "unauthorized": "You are not authorized to perform this action.", "notFound": "The requested resource was not found.", "validation": "Please check your input and try again."}}