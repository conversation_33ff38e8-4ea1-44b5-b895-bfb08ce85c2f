# HomeReno Backend API

HomeReno是一个加拿大家装平台的后端API，基于NestJS框架开发，提供用户管理、项目管理、服务商匹配、支付处理等核心功能。

## 🚀 快速开始

### 前提条件
- Node.js (版本 18 或更高)
- Docker Desktop (用于运行数据库)

### 一键启动 (推荐)
1. 双击运行 `start-with-db.bat` 脚本
2. 等待数据库和应用启动完成
3. 访问 http://localhost:3000/api 查看API文档

### 手动启动
```bash
# 启动数据库服务
docker-compose up -d

# 安装依赖
npm install

# 启动开发服务器
npm run start:dev
```

### 访问地址
- **API服务**: http://localhost:3000
- **API文档**: http://localhost:3000/api
- **MongoDB管理**: http://localhost:8081

### 停止服务
- 双击运行 `stop-db.bat` 或执行 `docker-compose down`

## 技术栈

- **框架**: NestJS (Node.js)
- **语言**: TypeScript
- **数据库**: MongoDB
- **缓存**: Redis
- **认证**: JWT
- **文档**: Swagger/OpenAPI
- **队列**: Bull (Redis)
- **文件存储**: AWS S3 / 本地存储
- **支付**: Stripe
- **通知**: 邮件 (Nodemailer) + 短信 (Twilio)

## 项目结构

```
src/
├── config/                 # 配置文件
│   ├── auth.config.ts
│   ├── database.config.ts
│   ├── email.config.ts
│   ├── file.config.ts
│   ├── redis.config.ts
│   └── sms.config.ts
├── common/                 # 通用模块
│   ├── dto/               # 通用DTO
│   └── interfaces/        # 通用接口
├── modules/               # 功能模块
│   ├── auth/             # 认证模块
│   ├── users/            # 用户管理
│   ├── projects/         # 项目管理
│   ├── service-providers/ # 服务商管理
│   ├── quotes/           # 报价管理
│   ├── payments/         # 支付处理
│   ├── files/            # 文件管理
│   ├── notifications/    # 通知系统
│   ├── reviews/          # 评价系统
│   └── tasks/            # 任务管理
├── shared/               # 共享模块
│   ├── database/
│   ├── redis/
│   ├── email/
│   ├── sms/
│   └── translation/
├── app.module.ts         # 主应用模块
└── main.ts              # 应用入口
```

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 环境配置

复制环境变量示例文件并配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库连接、Redis、JWT密钥等。

### 3. 启动服务

开发模式：
```bash
npm run start:dev
```

生产模式：
```bash
npm run build
npm run start:prod
```

### 4. 访问API文档

启动后访问 Swagger 文档：
```
http://localhost:3000/api/docs
```

## 核心功能

### 用户管理
- 用户注册/登录
- JWT认证
- 角色权限管理
- 个人资料管理
- 地理位置服务

### 项目管理
- 项目创建/编辑
- 项目状态跟踪
- 文件上传管理
- 项目匹配算法

### 服务商系统
- 服务商注册认证
- 技能和服务管理
- 作品集展示
- 评价系统

### 报价系统
- 智能报价匹配
- 报价比较
- 合同管理

### 支付系统
- Stripe集成
- 托管支付
- 分期付款
- 退款处理

### 通知系统
- 邮件通知
- 短信通知
- 实时推送
- 多语言支持

## API端点

### 认证相关
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh` - 刷新令牌
- `POST /api/v1/auth/forgot-password` - 忘记密码
- `POST /api/v1/auth/reset-password` - 重置密码

### 用户管理
- `GET /api/v1/users/me` - 获取当前用户信息
- `PATCH /api/v1/users/me` - 更新用户信息
- `POST /api/v1/users/me/change-password` - 修改密码
- `GET /api/v1/users/nearby` - 查找附近用户

### 项目管理
- `GET /api/v1/projects` - 获取项目列表
- `POST /api/v1/projects` - 创建项目
- `GET /api/v1/projects/:id` - 获取项目详情
- `PATCH /api/v1/projects/:id` - 更新项目

## 开发指南

### 代码规范
- 使用 TypeScript 严格模式
- 遵循 NestJS 最佳实践
- 使用 ESLint 和 Prettier
- 编写单元测试和集成测试

### 数据库设计
- 使用 MongoDB 作为主数据库
- 合理设计索引提升查询性能
- 使用 Mongoose ODM

### 安全考虑
- JWT 令牌认证
- 密码哈希存储
- API 限流保护
- 输入验证和清理
- CORS 配置

### 性能优化
- Redis 缓存策略
- 数据库查询优化
- 文件上传优化
- CDN 集成

## 测试

运行测试：
```bash
# 单元测试
npm run test

# 测试覆盖率
npm run test:cov

# 端到端测试
npm run test:e2e
```

## 部署

### Docker 部署
```bash
docker build -t homereno-api .
docker run -p 3000:3000 homereno-api
```

### 生产环境
1. 配置环境变量
2. 设置数据库连接
3. 配置 Redis 缓存
4. 设置文件存储
5. 配置监控和日志

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发团队。
