# HomeReno - 加拿大家装平台

HomeReno 是一个专为加拿大市场设计的家装服务平台，类似于土巴兔，连接房主和装修服务商。

## 🏗️ 项目架构

本项目采用前后端分离架构：

```
HomeReno/
├── backend/          # NestJS 后端 API 服务
├── frontend/         # React 前端应用 (待创建)
├── docs/            # 项目文档
└── README.md        # 项目说明
```

## 🚀 快速开始

### 后端服务

```bash
cd backend
npm install
npm run start:dev
```

后端服务将在 http://localhost:3000 启动
API 文档可在 http://localhost:3000/api 查看

### 前端应用

```bash
cd frontend
# 待创建前端项目
```

## 📚 技术栈

### 后端 (Backend)
- **框架**: NestJS + TypeScript
- **数据库**: MongoDB + Mongoose
- **认证**: JWT + Passport
- **文档**: Swagger/OpenAPI
- **3D渲染**: Three.js 集成
- **国际化**: i18next (中英双语)

### 前端 (Frontend) - 计划中
- **框架**: React + TypeScript
- **状态管理**: Redux Toolkit / Zustand
- **UI组件**: Ant Design / Material-UI
- **3D渲染**: Three.js + React Three Fiber
- **国际化**: react-i18next

## 🌟 核心功能

- ✅ 用户认证与授权
- ✅ 项目管理系统
- ✅ 服务商管理
- ✅ 报价系统
- ✅ 文件上传管理
- ✅ 通知系统
- ✅ 评价系统
- ✅ 3D设计工具集成
- ✅ 多语言支持 (中英文)
- 🔄 支付系统 (开发中)
- 🔄 前端界面 (开发中)

## 📖 文档

详细文档请查看 `docs/` 目录：

- [产品需求文档 (PRD)](docs/PRD.md)
- [系统架构设计](docs/Architecture.md)
- [数据库设计](docs/Database_Design.md)
- [API接口设计](docs/API_Design.md)
- [UI/UX设计](docs/UI_UX_Design.md)

## 🔧 开发环境

### 环境要求
- Node.js >= 16.x
- MongoDB >= 4.4
- npm >= 8.x

### 环境配置
1. 复制环境配置文件
2. 配置数据库连接
3. 启动开发服务器

## 📝 开发状态

### 后端开发进度: 85% ✅
- [x] 产品架构设计
- [x] 数据库设计  
- [x] API接口设计
- [x] 核心功能开发
- [x] 多语言支持
- [x] 3D设计工具集成
- [ ] 支付系统集成

### 前端开发进度: 0% 🚧
- [ ] 前端项目初始化
- [ ] UI组件库集成
- [ ] API客户端集成
- [ ] 用户认证界面
- [ ] 项目管理界面
- [ ] 服务商管理界面
- [ ] 3D设计工具界面
- [ ] 多语言前端支持

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
