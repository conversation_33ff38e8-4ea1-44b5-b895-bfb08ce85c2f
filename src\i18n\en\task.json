{"title": "Tasks", "create": "Create Task", "edit": "Edit Task", "delete": "Delete Task", "view": "View Task", "assign": "Assign Task", "complete": "Complete Task", "fields": {"title": "Task Title", "description": "Description", "type": "Task Type", "priority": "Priority", "status": "Status", "assignee": "Assignee", "dueDate": "Due Date", "estimatedHours": "Estimated Hours", "actualHours": "Actual Hours", "project": "Project", "dependencies": "Dependencies", "tags": "Tags", "attachments": "Attachments", "comments": "Comments"}, "types": {"project_task": "Project Task", "milestone": "Milestone", "inspection": "Inspection", "approval": "Approval", "payment": "Payment", "communication": "Communication", "documentation": "Documentation", "other": "Other"}, "priority": {"low": "Low", "medium": "Medium", "high": "High", "urgent": "<PERSON><PERSON>"}, "status": {"todo": "To Do", "in_progress": "In Progress", "review": "Under Review", "completed": "Completed", "cancelled": "Cancelled", "on_hold": "On Hold"}, "actions": {"start": "Start Task", "pause": "Pause Task", "resume": "Resume Task", "complete": "Complete Task", "cancel": "Cancel Task", "reopen": "Reopen Task", "assign": "Assign Task", "unassign": "Unassign Task", "addComment": "Add Comment", "addTimeLog": "Log Time", "uploadFile": "Upload File", "setDueDate": "Set Due Date", "setPriority": "Set Priority", "addDependency": "Add Dependency"}, "timeTracking": {"title": "Time Tracking", "startTime": "Start Time", "endTime": "End Time", "duration": "Duration", "description": "Description", "logTime": "Log Time", "totalTime": "Total Time", "todayTime": "Today's Time", "weekTime": "This Week's Time"}, "comments": {"title": "Comments", "add": "Add Comment", "edit": "Edit Comment", "delete": "Delete Comment", "reply": "Reply", "noComments": "No comments yet"}, "filters": {"all": "All Tasks", "myTasks": "My Tasks", "assignedToMe": "Assigned to Me", "createdByMe": "Created by <PERSON>", "overdue": "Overdue", "dueToday": "Due Today", "dueThisWeek": "Due This Week", "completed": "Completed", "inProgress": "In Progress", "byPriority": "By Priority", "byProject": "By Project"}, "messages": {"created": "Task created successfully!", "updated": "Task updated successfully!", "deleted": "Task deleted successfully!", "assigned": "Task assigned successfully!", "unassigned": "Task unassigned successfully!", "started": "Task started successfully!", "paused": "Task paused successfully!", "resumed": "Task resumed successfully!", "completed": "Task completed successfully!", "cancelled": "Task cancelled successfully!", "reopened": "Task reopened successfully!", "commentAdded": "Comment added successfully!", "timeLogged": "Time logged successfully!", "fileUploaded": "File uploaded successfully!", "dueDateSet": "Due date set successfully!", "prioritySet": "Priority set successfully!"}}