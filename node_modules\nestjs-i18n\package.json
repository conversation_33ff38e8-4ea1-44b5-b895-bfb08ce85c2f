{"name": "nestjs-i18n", "version": "10.5.1", "homepage": "https://nestjs-i18n.com", "description": "The i18n module for Nest.", "author": "<PERSON><PERSON>", "license": "MIT", "engines": {"node": ">=18"}, "keywords": ["<PERSON><PERSON><PERSON>", "i18n", "internationalization", "internationalisation", "internationalize", "internationalise", "international", "internationalize", "locale"], "repository": {"type": "git", "url": "https://github.com/ToonvanStrijp/nestjs-i18n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"prepare": "npm run build", "build": "npm run build:lib", "build:lib": "npx rimraf dist && tsc -p tsconfig.build.json", "prerelease": "npm run build && bumpp", "release": "npm publish --access public", "prerelease:next": "npm run build", "release:next": "npm publish --access public --tag next", "format": "prettier --write **/*.ts", "format:check": "prettier --check **/*.ts", "test": "jest --config ./tests/jest-test.json", "test:cov": "jest --config ./tests/jest-test.json --coverage", "test:watch": "jest --config ./tests/jest-test.json --watch", "coveralls": "npm run test:cov && cat ./coverage/lcov.info | coveralls", "lint": "eslint .", "lint:fix": "eslint . --fix", "docusaurus": "<PERSON>cusaurus", "doc:start": "docusaurus start", "doc:build": "docusaurus build", "doc:swizzle": "docusaurus swizzle", "doc:deploy": "docusaurus deploy", "doc:clear": "docusaurus clear", "doc:serve": "docusaurus serve", "doc:write-translations": "docusaurus write-translations", "doc:write-heading-ids": "docusaurus write-heading-ids", "typecheck": "tsc", "verify": "npm run format:check && npm run lint && npm run test:cov"}, "devDependencies": {"@apollo/client": "^3.6.9", "@docusaurus/core": "^2.4.3", "@docusaurus/module-type-aliases": "^2.4.3", "@docusaurus/preset-classic": "^2.4.3", "@grpc/grpc-js": "^1.6.7", "@grpc/proto-loader": "^0.7.4", "@nestjs/apollo": "^10.1.0", "@nestjs/common": "^9.0.11", "@nestjs/core": "^9.0.11", "@nestjs/graphql": "^10.1.1", "@nestjs/microservices": "^9.0.11", "@nestjs/platform-express": "^9.0.11", "@nestjs/platform-fastify": "^9.0.11", "@nestjs/testing": "^9.0.11", "@tsconfig/docusaurus": "^2.0.1", "@types/accept-language-parser": "^1.5.3", "@types/cookie": "^0.5.2", "@types/hbs": "^4.0.2", "@types/jest": "^29.5.5", "@types/js-yaml": "^4.0.6", "@types/node": "^20.6.3", "@types/string-format": "^2.0.0", "@types/supertest": "^2.0.12", "@types/validator": "^13.11.1", "@typescript-eslint/eslint-plugin": "^6.7.2", "@typescript-eslint/parser": "^6.7.2", "apollo-cache-inmemory": "^1.6.6", "apollo-client": "^2.6.10", "apollo-link-ws": "^1.0.20", "apollo-server-core": "^3.9.0", "apollo-server-express": "^3.9.0", "bumpp": "^9.2.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "docusaurus-plugin-typedoc": "^0.20.1", "ejs": "^3.1.9", "eslint": "^8.49.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "graphql-subscriptions": "^2.0.0", "graphql-tag": "^2.12.6", "hbs": "^4.2.0", "jest": "^29.7.0", "pug": "^3.0.2", "reflect-metadata": "^0.1.13", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "typedoc": "^0.25.1", "typedoc-plugin-markdown": "^3.16.0", "typescript": "^4.8.3"}, "dependencies": {"accept-language-parser": "^1.5.0", "chokidar": "^3.6.0", "cookie": "^0.7.0", "iterare": "^1.2.1", "js-yaml": "^4.1.0", "string-format": "^2.0.0"}, "peerDependencies": {"@nestjs/common": "*", "@nestjs/core": "*", "class-validator": "*", "rxjs": "*"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}