import { Module, DynamicModule } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigService } from '@nestjs/config';

@Module({})
export class DatabaseModule {
  static forRoot(): DynamicModule {
    const useMockDatabase = process.env.USE_MOCK_DATABASE === 'true';
    
    if (useMockDatabase) {
      console.log('🔧 Using Mock Database (In-Memory)');
      return {
        module: DatabaseModule,
        imports: [],
        providers: [],
        exports: [],
      };
    }

    console.log('🔧 Using MongoDB Database');
    return {
      module: DatabaseModule,
      imports: [
        MongooseModule.forRootAsync({
          useFactory: (configService: ConfigService) => ({
            uri: configService.get<string>('database.uri'),
            ...configService.get('database.options'),
          }),
          inject: [ConfigService],
        }),
      ],
      providers: [],
      exports: [MongooseModule],
    };
  }
}
