import { Module, DynamicModule } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigService, ConfigModule } from '@nestjs/config';

@Module({})
export class DatabaseModule {
  static forRoot(): DynamicModule {
    return {
      module: DatabaseModule,
      imports: [
        {
          module: class ConditionalMongooseModule {},
          providers: [
            {
              provide: 'MONGOOSE_CONNECTION_CHECK',
              useFactory: async (configService: ConfigService) => {
                const useMockDatabase = configService.get('database.useMockDatabase') ||
                                      process.env.USE_MOCK_DATABASE === 'true';

                if (useMockDatabase) {
                  console.log('🔧 Using Mock Database (In-Memory) - MongoDB connection skipped');
                  return null;
                } else {
                  console.log('🔧 Using MongoDB Database');
                  // 这里不实际连接，让MongooseModule处理
                  return 'mongodb';
                }
              },
              inject: [ConfigService],
            },
          ],
          exports: ['MONGOOSE_CONNECTION_CHECK'],
        },
      ],
      providers: [],
      exports: [],
    };
  }
}
