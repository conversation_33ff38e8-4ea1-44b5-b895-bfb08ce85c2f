// MongoDB初始化脚本
// 创建应用数据库和用户

// 切换到homereno数据库
db = db.getSiblingDB('homereno');

// 创建应用用户
db.createUser({
  user: 'homereno_user',
  pwd: 'homereno123',
  roles: [
    {
      role: 'readWrite',
      db: 'homereno'
    }
  ]
});

// 创建基础集合和索引
db.createCollection('users');
db.createCollection('projects');
db.createCollection('serviceproviders');
db.createCollection('quotes');
db.createCollection('notifications');
db.createCollection('reviews');
db.createCollection('payments');
db.createCollection('tasks');
db.createCollection('files');

// 创建用户集合索引
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ 'profile.phone': 1 });
db.users.createIndex({ userType: 1 });
db.users.createIndex({ isActive: 1 });
db.users.createIndex({ createdAt: -1 });

// 创建项目集合索引
db.projects.createIndex({ homeownerId: 1 });
db.projects.createIndex({ status: 1 });
db.projects.createIndex({ category: 1 });
db.projects.createIndex({ 'location.coordinates': '2dsphere' });
db.projects.createIndex({ createdAt: -1 });

// 创建服务提供商集合索引
db.serviceproviders.createIndex({ userId: 1 }, { unique: true });
db.serviceproviders.createIndex({ 'services.category': 1 });
db.serviceproviders.createIndex({ 'location.coordinates': '2dsphere' });
db.serviceproviders.createIndex({ isVerified: 1 });
db.serviceproviders.createIndex({ rating: -1 });

// 创建报价集合索引
db.quotes.createIndex({ projectId: 1 });
db.quotes.createIndex({ serviceProviderId: 1 });
db.quotes.createIndex({ status: 1 });
db.quotes.createIndex({ createdAt: -1 });

// 创建通知集合索引
db.notifications.createIndex({ userId: 1 });
db.notifications.createIndex({ isRead: 1 });
db.notifications.createIndex({ type: 1 });
db.notifications.createIndex({ createdAt: -1 });

// 创建评价集合索引
db.reviews.createIndex({ projectId: 1 });
db.reviews.createIndex({ serviceProviderId: 1 });
db.reviews.createIndex({ reviewerId: 1 });
db.reviews.createIndex({ rating: -1 });

// 创建支付集合索引
db.payments.createIndex({ projectId: 1 });
db.payments.createIndex({ payerId: 1 });
db.payments.createIndex({ payeeId: 1 });
db.payments.createIndex({ status: 1 });
db.payments.createIndex({ createdAt: -1 });

// 创建任务集合索引
db.tasks.createIndex({ projectId: 1 });
db.tasks.createIndex({ assignedTo: 1 });
db.tasks.createIndex({ status: 1 });
db.tasks.createIndex({ dueDate: 1 });

// 创建文件集合索引
db.files.createIndex({ uploadedBy: 1 });
db.files.createIndex({ projectId: 1 });
db.files.createIndex({ fileType: 1 });
db.files.createIndex({ createdAt: -1 });

print('MongoDB初始化完成！');
print('数据库: homereno');
print('用户: homereno_user');
print('密码: homereno123');
