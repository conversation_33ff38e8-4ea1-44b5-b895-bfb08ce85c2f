import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type ReviewDocument = Review & Document;

export enum ReviewType {
  PROJECT_REVIEW = 'project_review',
  SERVICE_PROVIDER_REVIEW = 'service_provider_review',
  CUSTOMER_REVIEW = 'customer_review',
}

export enum ReviewStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  HIDDEN = 'hidden',
}

@Schema({ _id: false })
export class ReviewCriteria {
  @Prop({ required: true })
  @ApiProperty({ description: '评价维度名称' })
  name: string;

  @Prop({ required: true, min: 1, max: 5 })
  @ApiProperty({ description: '评分(1-5)', minimum: 1, maximum: 5 })
  rating: number;

  @Prop()
  @ApiProperty({ description: '评价说明' })
  comment?: string;
}

@Schema({ _id: false })
export class ReviewResponse {
  @Prop({ required: true })
  @ApiProperty({ description: '回复内容' })
  content: string;

  @Prop({ type: Types.ObjectId, required: true, ref: 'User' })
  @ApiProperty({ description: '回复者ID' })
  responderId: Types.ObjectId;

  @Prop({ default: Date.now })
  @ApiProperty({ description: '回复时间' })
  createdAt: Date;

  @Prop({ default: false })
  @ApiProperty({ description: '是否已编辑' })
  isEdited: boolean;

  @Prop()
  @ApiProperty({ description: '编辑时间' })
  editedAt?: Date;
}

@Schema({ timestamps: true })
export class Review {
  @Prop({ enum: ReviewType, required: true })
  @ApiProperty({ description: '评价类型', enum: ReviewType })
  type: ReviewType;

  @Prop({ type: Types.ObjectId, required: true, ref: 'User' })
  @ApiProperty({ description: '评价者ID' })
  reviewerId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, required: true, ref: 'User' })
  @ApiProperty({ description: '被评价者ID' })
  revieweeId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Project' })
  @ApiProperty({ description: '关联项目ID' })
  projectId?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'ServiceProvider' })
  @ApiProperty({ description: '关联服务商ID' })
  serviceProviderId?: Types.ObjectId;

  @Prop({ required: true, min: 1, max: 5 })
  @ApiProperty({ description: '总体评分(1-5)', minimum: 1, maximum: 5 })
  overallRating: number;

  @Prop([ReviewCriteria])
  @ApiProperty({ description: '分项评价', type: [ReviewCriteria] })
  criteria?: ReviewCriteria[];

  @Prop({ required: true })
  @ApiProperty({ description: '评价标题' })
  title: string;

  @Prop({ required: true })
  @ApiProperty({ description: '评价内容' })
  content: string;

  @Prop([String])
  @ApiProperty({ description: '评价图片', type: [String] })
  images?: string[];

  @Prop([String])
  @ApiProperty({ description: '评价标签', type: [String] })
  tags?: string[];

  @Prop({ enum: ReviewStatus, default: ReviewStatus.PENDING })
  @ApiProperty({ description: '评价状态', enum: ReviewStatus })
  status: ReviewStatus;

  @Prop({ default: 0 })
  @ApiProperty({ description: '点赞数' })
  likeCount: number;

  @Prop({ default: 0 })
  @ApiProperty({ description: '踩数' })
  dislikeCount: number;

  @Prop({ default: 0 })
  @ApiProperty({ description: '有用数' })
  helpfulCount: number;

  @Prop({ default: 0 })
  @ApiProperty({ description: '举报数' })
  reportCount: number;

  @Prop({ default: false })
  @ApiProperty({ description: '是否推荐' })
  isRecommended: boolean;

  @Prop({ default: false })
  @ApiProperty({ description: '是否匿名' })
  isAnonymous: boolean;

  @Prop({ default: false })
  @ApiProperty({ description: '是否已验证' })
  isVerified: boolean;

  @Prop()
  @ApiProperty({ description: '验证时间' })
  verifiedAt?: Date;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  @ApiProperty({ description: '验证者ID' })
  verifiedBy?: Types.ObjectId;

  @Prop([ReviewResponse])
  @ApiProperty({ description: '回复列表', type: [ReviewResponse] })
  responses?: ReviewResponse[];

  @Prop()
  @ApiProperty({ description: '审核备注' })
  moderationNote?: string;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  @ApiProperty({ description: '审核者ID' })
  moderatedBy?: Types.ObjectId;

  @Prop()
  @ApiProperty({ description: '审核时间' })
  moderatedAt?: Date;

  @Prop({ default: false })
  @ApiProperty({ description: '是否已删除' })
  isDeleted: boolean;

  @Prop()
  @ApiProperty({ description: '删除时间' })
  deletedAt?: Date;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  @ApiProperty({ description: '删除者ID' })
  deletedBy?: Types.ObjectId;
}

export const ReviewSchema = SchemaFactory.createForClass(Review);

// 创建索引
ReviewSchema.index({ revieweeId: 1, status: 1, createdAt: -1 });
ReviewSchema.index({ reviewerId: 1, createdAt: -1 });
ReviewSchema.index({ projectId: 1, status: 1 });
ReviewSchema.index({ serviceProviderId: 1, status: 1 });
ReviewSchema.index({ type: 1, status: 1 });
ReviewSchema.index({ overallRating: 1, status: 1 });
ReviewSchema.index({ isDeleted: 1, createdAt: -1 });
ReviewSchema.index({ status: 1, createdAt: -1 });

// 复合索引
ReviewSchema.index({ reviewerId: 1, revieweeId: 1, projectId: 1 }, { unique: true, sparse: true });

// 虚拟字段
ReviewSchema.virtual('averageCriteriaRating').get(function() {
  if (!this.criteria || this.criteria.length === 0) {
    return this.overallRating;
  }
  
  const sum = this.criteria.reduce((acc, criterion) => acc + criterion.rating, 0);
  return Math.round((sum / this.criteria.length) * 10) / 10;
});

ReviewSchema.virtual('totalInteractions').get(function() {
  return this.likeCount + this.dislikeCount + this.helpfulCount;
});

ReviewSchema.virtual('isPositive').get(function() {
  return this.overallRating >= 4;
});

ReviewSchema.virtual('isNegative').get(function() {
  return this.overallRating <= 2;
});

ReviewSchema.virtual('responseCount').get(function() {
  return this.responses ? this.responses.length : 0;
});

// 设置虚拟字段在JSON序列化时包含
ReviewSchema.set('toJSON', { virtuals: true });
ReviewSchema.set('toObject', { virtuals: true });
