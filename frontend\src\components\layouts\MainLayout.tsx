import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Layout,
  Menu,
  Button,
  Dropdown,
  Avatar,
  Space,
  Badge,
  Switch,
  Typography,
} from 'antd';
import {
  HomeOutlined,
  ProjectOutlined,
  TeamOutlined,
  UserOutlined,
  BellOutlined,
  LogoutOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import { useAuthStore } from '../../store/authStore';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { user, isAuthenticated, logout } = useAuthStore();

  // 菜单项配置
  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: t('navigation.home'),
    },
    {
      key: '/projects',
      icon: <ProjectOutlined />,
      label: t('navigation.projects'),
    },
    {
      key: '/contractors',
      icon: <TeamOutlined />,
      label: t('navigation.contractors'),
    },
  ];

  // 认证用户的额外菜单项
  const authenticatedMenuItems = [
    {
      key: '/dashboard',
      icon: <UserOutlined />,
      label: t('navigation.dashboard'),
    },
    {
      key: '/my/projects',
      icon: <ProjectOutlined />,
      label: t('projects.myProjects'),
    },
  ];

  const allMenuItems = isAuthenticated 
    ? [...menuItems, ...authenticatedMenuItems]
    : menuItems;

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: t('navigation.profile'),
      onClick: () => navigate('/my/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: t('navigation.settings'),
      onClick: () => navigate('/settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: t('navigation.logout'),
      onClick: logout,
    },
  ];

  // 语言切换
  const toggleLanguage = () => {
    const newLang = i18n.language === 'en' ? 'zh' : 'en';
    i18n.changeLanguage(newLang);
    localStorage.setItem('language', newLang);
  };

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        style={{
          background: '#fff',
          boxShadow: '2px 0 8px rgba(0,0,0,0.1)',
        }}
      >
        <div style={{ 
          height: 64, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <Text strong style={{ fontSize: collapsed ? 16 : 20, color: '#1890ff' }}>
            {collapsed ? 'HR' : 'HomeReno'}
          </Text>
        </div>
        
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={allMenuItems}
          onClick={handleMenuClick}
          style={{ border: 'none' }}
        />
      </Sider>

      <Layout>
        <Header style={{ 
          padding: '0 24px', 
          background: '#fff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{ fontSize: 16 }}
          />

          <Space size="large">
            {/* 语言切换 */}
            <Space>
              <GlobalOutlined />
              <Switch
                checkedChildren="中"
                unCheckedChildren="EN"
                checked={i18n.language === 'zh'}
                onChange={toggleLanguage}
              />
            </Space>

            {isAuthenticated ? (
              <>
                {/* 通知 */}
                <Badge count={0} showZero={false}>
                  <Button
                    type="text"
                    icon={<BellOutlined />}
                    onClick={() => navigate('/notifications')}
                  />
                </Badge>

                {/* 用户菜单 */}
                <Dropdown
                  menu={{ items: userMenuItems }}
                  placement="bottomRight"
                  arrow
                >
                  <Space style={{ cursor: 'pointer' }}>
                    <Avatar 
                      src={user?.avatar} 
                      icon={<UserOutlined />}
                      size="small"
                    />
                    <Text>{user?.firstName} {user?.lastName}</Text>
                  </Space>
                </Dropdown>
              </>
            ) : (
              <Space>
                <Button onClick={() => navigate('/auth/login')}>
                  {t('navigation.login')}
                </Button>
                <Button 
                  type="primary" 
                  onClick={() => navigate('/auth/register')}
                >
                  {t('navigation.register')}
                </Button>
              </Space>
            )}
          </Space>
        </Header>

        <Content style={{ 
          margin: '24px',
          padding: '24px',
          background: '#fff',
          borderRadius: '8px',
          minHeight: 'calc(100vh - 112px)',
        }}>
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
