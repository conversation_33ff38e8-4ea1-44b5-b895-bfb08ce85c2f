import {
  IsString,
  IsEnum,
  IsOptional,
  IsBoolean,
  IsNumber,
  IsMongoId,
  IsObject,
  IsDateString,
  ValidateNested,
  <PERSON><PERSON><PERSON>th,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  PaymentType,
  PaymentMethod,
  PaymentStatus,
  Currency,
} from '../schemas/payment.schema';

export class CreatePaymentDto {
  @ApiProperty({ description: '收款人ID' })
  @IsMongoId()
  payeeId: string;

  @ApiPropertyOptional({ description: '关联项目ID' })
  @IsOptional()
  @IsMongoId()
  projectId?: string;

  @ApiPropertyOptional({ description: '关联报价ID' })
  @IsOptional()
  @IsMongoId()
  quoteId?: string;

  @ApiProperty({ description: '支付类型', enum: PaymentType })
  @IsEnum(PaymentType)
  type: PaymentType;

  @ApiProperty({ description: '支付金额', minimum: 0.01 })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01)
  amount: number;

  @ApiPropertyOptional({ description: '货币', enum: Currency, default: Currency.CAD })
  @IsOptional()
  @IsEnum(Currency)
  currency?: Currency = Currency.CAD;

  @ApiProperty({ description: '支付方式', enum: PaymentMethod })
  @IsEnum(PaymentMethod)
  method: PaymentMethod;

  @ApiProperty({ description: '支付描述' })
  @IsString()
  @MinLength(1)
  @MaxLength(500)
  description: string;

  @ApiPropertyOptional({ description: '是否托管', default: false })
  @IsOptional()
  @IsBoolean()
  isEscrow?: boolean = false;

  @ApiPropertyOptional({ description: '到期时间' })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;

  @ApiPropertyOptional({ description: '备注' })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  notes?: string;

  @ApiPropertyOptional({ description: '元数据' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class PaymentQueryDto {
  @ApiPropertyOptional({ description: '页码', default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', default: 20 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number = 20;

  @ApiPropertyOptional({ description: '支付类型', enum: PaymentType })
  @IsOptional()
  @IsEnum(PaymentType)
  type?: PaymentType;

  @ApiPropertyOptional({ description: '支付状态', enum: PaymentStatus })
  @IsOptional()
  @IsEnum(PaymentStatus)
  status?: PaymentStatus;

  @ApiPropertyOptional({ description: '支付方式', enum: PaymentMethod })
  @IsOptional()
  @IsEnum(PaymentMethod)
  method?: PaymentMethod;

  @ApiPropertyOptional({ description: '货币', enum: Currency })
  @IsOptional()
  @IsEnum(Currency)
  currency?: Currency;

  @ApiPropertyOptional({ description: '付款人ID' })
  @IsOptional()
  @IsMongoId()
  payerId?: string;

  @ApiPropertyOptional({ description: '收款人ID' })
  @IsOptional()
  @IsMongoId()
  payeeId?: string;

  @ApiPropertyOptional({ description: '项目ID' })
  @IsOptional()
  @IsMongoId()
  projectId?: string;

  @ApiPropertyOptional({ description: '报价ID' })
  @IsOptional()
  @IsMongoId()
  quoteId?: string;

  @ApiPropertyOptional({ description: '最小金额' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minAmount?: number;

  @ApiPropertyOptional({ description: '最大金额' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  maxAmount?: number;

  @ApiPropertyOptional({ description: '是否托管' })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  isEscrow?: boolean;

  @ApiPropertyOptional({ description: '开始日期' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ description: '结束日期' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ description: '搜索关键词' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: '排序字段', enum: ['createdAt', 'amount', 'completedAt'] })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ description: '排序方向', enum: ['asc', 'desc'] })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class ProcessPaymentDto {
  @ApiProperty({ description: '支付方式令牌' })
  @IsString()
  paymentMethodToken: string;

  @ApiPropertyOptional({ description: '确认支付' })
  @IsOptional()
  @IsBoolean()
  confirm?: boolean = true;

  @ApiPropertyOptional({ description: '保存支付方式' })
  @IsOptional()
  @IsBoolean()
  savePaymentMethod?: boolean = false;
}

export class RefundPaymentDto {
  @ApiProperty({ description: '退款金额', minimum: 0.01 })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01)
  amount: number;

  @ApiProperty({ description: '退款原因' })
  @IsString()
  @MinLength(1)
  @MaxLength(500)
  reason: string;

  @ApiPropertyOptional({ description: '备注' })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  notes?: string;
}

export class EscrowReleaseDto {
  @ApiPropertyOptional({ description: '释放金额' })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01)
  amount?: number;

  @ApiPropertyOptional({ description: '释放原因' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  reason?: string;

  @ApiPropertyOptional({ description: '备注' })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  notes?: string;
}

export class UpdatePaymentStatusDto {
  @ApiProperty({ description: '支付状态', enum: PaymentStatus })
  @IsEnum(PaymentStatus)
  status: PaymentStatus;

  @ApiPropertyOptional({ description: '状态更新原因' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  reason?: string;

  @ApiPropertyOptional({ description: '备注' })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  notes?: string;
}
