'use strict';(function(k,n){"object"===typeof exports&&"undefined"!==typeof module?n(exports):"function"===typeof define&&define.amd?define(["exports"],n):(k="undefined"!==typeof globalThis?globalThis:k||self,n(k.troika_worker_utils={}))})(this,function(k){function n(){function b(a,d){var c=a.id,h=a.name,l=a.dependencies;void 0===l&&(l=[]);var g=a.init;void 0===g&&(g=function(){});a=a.getTransferables;void 0===a&&(a=null);if(!f[c])try{l=l.map(function(a){a&&a.isWorkerModule&&(b(a,function(a){if(a instanceof
Error)throw a;}),a=f[a.id].value);return a}),g=e("<"+h+">.init",g),a&&(a=e("<"+h+">.getTransferables",a)),h=null,"function"===typeof g?h=g.apply(void 0,l):console.error("worker module init function failed to rehydrate"),f[c]={id:c,value:h,getTransferables:a},d(h)}catch(m){m&&m.noLog||console.error(m),d(m)}}function c(a,d){function b(a){try{var b=f[e].getTransferables&&f[e].getTransferables(a);b&&Array.isArray(b)&&b.length||(b=void 0);d(a,b)}catch(v){console.error(v),d(v)}}var c,e=a.id;a=a.args;f[e]&&
"function"===typeof f[e].value||d(Error("Worker module "+e+": not found or its 'init' did not return a function"));try{var g=(c=f[e]).value.apply(c,a);g&&"function"===typeof g.then?g.then(b,function(a){return d(a instanceof Error?a:Error(""+a))}):b(g)}catch(m){d(m)}}function e(a,b){var d=void 0;self.troikaDefine=function(a){return d=a};a=URL.createObjectURL(new Blob(["/** "+a.replace(/\*/g,"")+" **/\n\ntroikaDefine(\n"+b+"\n)"],{type:"application/javascript"}));try{importScripts(a)}catch(h){console.error(h)}URL.revokeObjectURL(a);
delete self.troikaDefine;return d}var f=Object.create(null);self.addEventListener("message",function(a){var d=a.data,e=d.messageId;a=d.action;d=d.data;try{"registerModule"===a&&b(d,function(a){a instanceof Error?postMessage({messageId:e,success:!1,error:a.message}):postMessage({messageId:e,success:!0,result:{isCallable:"function"===typeof a}})}),"callModule"===a&&c(d,function(a,b){a instanceof Error?postMessage({messageId:e,success:!1,error:a.message}):postMessage({messageId:e,success:!0,result:a},
b||void 0)})}catch(h){postMessage({messageId:e,success:!1,error:h.stack})}})}function z(b){var c=function(){for(var b=[],f=arguments.length;f--;)b[f]=arguments[f];return c._getInitResult().then(function(a){if("function"===typeof a)return a.apply(void 0,b);throw Error("Worker module function was called but `init` did not return a callable function");})};c._getInitResult=function(){var e=b.dependencies,f=b.init;e=Array.isArray(e)?e.map(function(a){a&&(a=a.onMainThread||a,a._getInitResult&&(a=a._getInitResult()));
return a}):[];var a=Promise.all(e).then(function(a){return f.apply(null,a)});c._getInitResult=function(){return a};return a};return c}function w(b){function c(){for(var a=[],b=arguments.length;b--;)a[b]=arguments[b];if(!x())return k.apply(void 0,a);if(!g){g=y(d,"registerModule",c.workerModuleData);var e=function(){g=null;p[d].delete(e)};(p[d]||(p[d]=new Set)).add(e)}return g.then(function(b){if(b.isCallable)return y(d,"callModule",{id:h,args:a});throw Error("Worker module function was called but `init` did not return a callable function");
})}if(!(b&&"function"===typeof b.init||t))throw Error("requires `options.init` function");var e=b.dependencies,f=b.init,a=b.getTransferables,d=b.workerId,k=z(b);null==d&&(d="#default");var h="workerModule"+ ++A,l=b.name||h,g=null;e=e&&e.map(function(a){"function"!==typeof a||a.workerModuleData||(t=!0,a=w({workerId:d,name:"<"+l+"> function dependency: "+a.name,init:"function(){return (\n"+q(a)+"\n)}"}),t=!1);a&&a.workerModuleData&&(a=a.workerModuleData);return a});c.workerModuleData={isWorkerModule:!0,
id:h,name:l,dependencies:e,init:q(f),getTransferables:a&&q(a)};c.onMainThread=k;return c}function q(b){b=b.toString();!/^function/.test(b)&&/^\w+\s*\(/.test(b)&&(b="function "+b);return b}function B(b){var c=r[b];c||(c=q(n),c=r[b]=new Worker(URL.createObjectURL(new Blob(["/** Worker Module Bootstrap: "+b.replace(/\*/g,"")+" **/\n\n;("+c+")()"],{type:"application/javascript"}))),c.onmessage=function(b){b=b.data;var c=b.messageId,a=u[c];if(!a)throw Error("WorkerModule response with empty or unknown messageId");
delete u[c];a(b)});return c}function y(b,c,e){return new Promise(function(f,a){var d=++C;u[d]=function(b){b.success?f(b.result):a(Error("Error in worker "+c+" call: "+b.error))};B(b).postMessage({messageId:d,action:c,data:e})})}var x=function(){var b=!1;if("undefined"!==typeof window&&"undefined"!==typeof window.document)try{(new Worker(URL.createObjectURL(new Blob([""],{type:"application/javascript"})))).terminate(),b=!0}catch(c){"undefined"!==typeof process&&"test"===process.env.NODE_ENV||console.log("Troika createWorkerModule: web workers not allowed; falling back to main thread execution. Cause: ["+
c.message+"]")}x=function(){return b};return b},A=0,C=0,t=!1,r=Object.create(null),p=Object.create(null),u=Object.create(null);k.defineWorkerModule=w;k.stringifyFunction=q;k.terminateWorker=function(b){p[b]&&p[b].forEach(function(b){b()});r[b]&&(r[b].terminate(),delete r[b])};Object.defineProperty(k,"__esModule",{value:!0})})
