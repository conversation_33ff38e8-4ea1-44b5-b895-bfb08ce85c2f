import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Quote, QuoteDocument, QuoteStatus } from './schemas/quote.schema';
import { CreateQuoteDto } from './dto/create-quote.dto';
import { UpdateQuoteDto } from './dto/update-quote.dto';
import { QuoteQueryDto } from './dto/quote-query.dto';
import { PaginatedResult } from '../../common/interfaces/paginated-result.interface';
import { User, UserType } from '../users/schemas/user.schema';

@Injectable()
export class QuotesService {
  constructor(
    @InjectModel(Quote.name) private quoteModel: Model<QuoteDocument>,
  ) {}

  /**
   * 创建报价
   */
  async create(createQuoteDto: CreateQuoteDto, user: User): Promise<Quote> {
    // 验证用户是否为服务商
    if (user.userType !== UserType.SERVICE_PROVIDER) {
      throw new ForbiddenException('Only service providers can create quotes');
    }

    // 验证金额计算
    const calculatedSubtotal = createQuoteDto.items.reduce(
      (sum, item) => sum + item.totalPrice,
      0,
    );
    
    if (Math.abs(calculatedSubtotal - createQuoteDto.subtotal) > 0.01) {
      throw new BadRequestException('Subtotal does not match sum of item totals');
    }

    const calculatedTotal = createQuoteDto.subtotal + createQuoteDto.tax - createQuoteDto.discount;
    if (Math.abs(calculatedTotal - createQuoteDto.totalAmount) > 0.01) {
      throw new BadRequestException('Total amount calculation is incorrect');
    }

    // 验证付款计划
    if (createQuoteDto.paymentSchedule?.length) {
      const totalPercentage = createQuoteDto.paymentSchedule.reduce(
        (sum, payment) => sum + payment.percentage,
        0,
      );
      if (Math.abs(totalPercentage - 100) > 0.01) {
        throw new BadRequestException('Payment schedule percentages must sum to 100%');
      }

      const totalAmount = createQuoteDto.paymentSchedule.reduce(
        (sum, payment) => sum + payment.amount,
        0,
      );
      if (Math.abs(totalAmount - createQuoteDto.totalAmount) > 0.01) {
        throw new BadRequestException('Payment schedule amounts must sum to total amount');
      }
    }

    // 设置过期时间
    let expiresAt: Date | undefined;
    if (createQuoteDto.terms?.validityDays) {
      expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + createQuoteDto.terms.validityDays);
    }

    const quoteData = {
      ...createQuoteDto,
      providerId: user._id, // 假设用户就是服务商
      customerId: new Types.ObjectId(), // 这里需要从项目中获取客户ID
      status: QuoteStatus.DRAFT,
      expiresAt,
    };

    const createdQuote = new this.quoteModel(quoteData);
    return createdQuote.save();
  }

  /**
   * 获取报价列表（分页）
   */
  async findAll(query: QuoteQueryDto): Promise<PaginatedResult<Quote>> {
    const {
      page = 1,
      limit = 20,
      status,
      projectId,
      providerId,
      customerId,
      minAmount,
      maxAmount,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = query;

    const filter: any = {};

    // 状态过滤
    if (status) {
      filter.status = status;
    }

    // 项目过滤
    if (projectId) {
      filter.projectId = projectId;
    }

    // 服务商过滤
    if (providerId) {
      filter.providerId = providerId;
    }

    // 客户过滤
    if (customerId) {
      filter.customerId = customerId;
    }

    // 金额过滤
    if (minAmount !== undefined || maxAmount !== undefined) {
      filter.totalAmount = {};
      if (minAmount !== undefined) {
        filter.totalAmount.$gte = minAmount;
      }
      if (maxAmount !== undefined) {
        filter.totalAmount.$lte = maxAmount;
      }
    }

    // 关键词搜索
    if (search) {
      filter.$or = [
        { title: new RegExp(search, 'i') },
        { description: new RegExp(search, 'i') },
        { 'items.name': new RegExp(search, 'i') },
      ];
    }

    // 排序
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [items, total] = await Promise.all([
      this.quoteModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('projectId', 'title category')
        .populate('providerId', 'businessInfo.companyName')
        .populate('customerId', 'profile.firstName profile.lastName')
        .exec(),
      this.quoteModel.countDocuments(filter),
    ]);

    return {
      items,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * 根据ID获取报价详情
   */
  async findOne(id: string): Promise<Quote> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid quote ID');
    }

    const quote = await this.quoteModel
      .findById(id)
      .populate('projectId', 'title description category location budget')
      .populate('providerId', 'businessInfo.companyName businessInfo.description')
      .populate('customerId', 'profile.firstName profile.lastName email profile.phone')
      .exec();

    if (!quote) {
      throw new NotFoundException('Quote not found');
    }

    return quote;
  }

  /**
   * 更新报价
   */
  async update(id: string, updateQuoteDto: UpdateQuoteDto, user: User): Promise<Quote> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid quote ID');
    }

    const quote = await this.quoteModel.findById(id);
    if (!quote) {
      throw new NotFoundException('Quote not found');
    }

    // 检查权限
    if (quote.providerId.toString() !== user._id.toString()) {
      throw new ForbiddenException('You can only update your own quotes');
    }

    // 检查报价状态
    if (quote.status === QuoteStatus.ACCEPTED) {
      throw new BadRequestException('Cannot update accepted quote');
    }

    if (quote.status === QuoteStatus.EXPIRED) {
      throw new BadRequestException('Cannot update expired quote');
    }

    // 验证金额计算（如果有更新）
    if (updateQuoteDto.items && updateQuoteDto.subtotal) {
      const calculatedSubtotal = updateQuoteDto.items.reduce(
        (sum, item) => sum + item.totalPrice,
        0,
      );
      
      if (Math.abs(calculatedSubtotal - updateQuoteDto.subtotal) > 0.01) {
        throw new BadRequestException('Subtotal does not match sum of item totals');
      }
    }

    const updatedQuote = await this.quoteModel
      .findByIdAndUpdate(id, updateQuoteDto, { new: true })
      .populate('projectId', 'title category')
      .populate('providerId', 'businessInfo.companyName')
      .populate('customerId', 'profile.firstName profile.lastName')
      .exec();

    return updatedQuote;
  }

  /**
   * 删除报价
   */
  async remove(id: string, user: User): Promise<void> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid quote ID');
    }

    const quote = await this.quoteModel.findById(id);
    if (!quote) {
      throw new NotFoundException('Quote not found');
    }

    // 检查权限
    if (quote.providerId.toString() !== user._id.toString()) {
      throw new ForbiddenException('You can only delete your own quotes');
    }

    // 检查报价状态
    if (quote.status === QuoteStatus.ACCEPTED) {
      throw new BadRequestException('Cannot delete accepted quote');
    }

    await this.quoteModel.findByIdAndDelete(id);
  }

  /**
   * 提交报价
   */
  async submit(id: string, user: User): Promise<Quote> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid quote ID');
    }

    const quote = await this.quoteModel.findById(id);
    if (!quote) {
      throw new NotFoundException('Quote not found');
    }

    // 检查权限
    if (quote.providerId.toString() !== user._id.toString()) {
      throw new ForbiddenException('You can only submit your own quotes');
    }

    // 检查报价状态
    if (quote.status !== QuoteStatus.DRAFT) {
      throw new BadRequestException('Only draft quotes can be submitted');
    }

    const updatedQuote = await this.quoteModel
      .findByIdAndUpdate(
        id,
        {
          status: QuoteStatus.SUBMITTED,
          submittedAt: new Date(),
        },
        { new: true }
      )
      .populate('projectId', 'title category')
      .populate('providerId', 'businessInfo.companyName')
      .populate('customerId', 'profile.firstName profile.lastName')
      .exec();

    return updatedQuote;
  }

  /**
   * 接受报价（客户）
   */
  async accept(id: string, user: User): Promise<Quote> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid quote ID');
    }

    const quote = await this.quoteModel.findById(id);
    if (!quote) {
      throw new NotFoundException('Quote not found');
    }

    // 检查权限（客户）
    if (quote.customerId.toString() !== user._id.toString()) {
      throw new ForbiddenException('You can only accept quotes for your own projects');
    }

    // 检查报价状态
    if (quote.status !== QuoteStatus.SUBMITTED) {
      throw new BadRequestException('Only submitted quotes can be accepted');
    }

    // 检查是否过期
    if (quote.expiresAt && new Date() > quote.expiresAt) {
      throw new BadRequestException('Quote has expired');
    }

    const updatedQuote = await this.quoteModel
      .findByIdAndUpdate(
        id,
        {
          status: QuoteStatus.ACCEPTED,
          acceptedAt: new Date(),
        },
        { new: true }
      )
      .populate('projectId', 'title category')
      .populate('providerId', 'businessInfo.companyName')
      .populate('customerId', 'profile.firstName profile.lastName')
      .exec();

    return updatedQuote;
  }

  /**
   * 拒绝报价（客户）
   */
  async decline(id: string, reason: string, user: User): Promise<Quote> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid quote ID');
    }

    const quote = await this.quoteModel.findById(id);
    if (!quote) {
      throw new NotFoundException('Quote not found');
    }

    // 检查权限（客户）
    if (quote.customerId.toString() !== user._id.toString()) {
      throw new ForbiddenException('You can only decline quotes for your own projects');
    }

    // 检查报价状态
    if (quote.status !== QuoteStatus.SUBMITTED) {
      throw new BadRequestException('Only submitted quotes can be declined');
    }

    const updatedQuote = await this.quoteModel
      .findByIdAndUpdate(
        id,
        {
          status: QuoteStatus.DECLINED,
          declinedAt: new Date(),
          declineReason: reason,
        },
        { new: true }
      )
      .populate('projectId', 'title category')
      .populate('providerId', 'businessInfo.companyName')
      .populate('customerId', 'profile.firstName profile.lastName')
      .exec();

    return updatedQuote;
  }

  /**
   * 获取项目的所有报价
   */
  async findByProject(projectId: string, query: QuoteQueryDto): Promise<PaginatedResult<Quote>> {
    if (!Types.ObjectId.isValid(projectId)) {
      throw new BadRequestException('Invalid project ID');
    }

    const modifiedQuery = {
      ...query,
      projectId,
    };

    return this.findAll(modifiedQuery);
  }

  /**
   * 获取服务商的所有报价
   */
  async findByProvider(providerId: string, query: QuoteQueryDto): Promise<PaginatedResult<Quote>> {
    if (!Types.ObjectId.isValid(providerId)) {
      throw new BadRequestException('Invalid provider ID');
    }

    const modifiedQuery = {
      ...query,
      providerId,
    };

    return this.findAll(modifiedQuery);
  }

  /**
   * 获取客户的所有报价
   */
  async findByCustomer(customerId: string, query: QuoteQueryDto): Promise<PaginatedResult<Quote>> {
    if (!Types.ObjectId.isValid(customerId)) {
      throw new BadRequestException('Invalid customer ID');
    }

    const modifiedQuery = {
      ...query,
      customerId,
    };

    return this.findAll(modifiedQuery);
  }
}
