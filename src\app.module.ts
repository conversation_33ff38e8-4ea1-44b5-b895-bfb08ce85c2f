import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { CacheModule } from '@nestjs/cache-manager';
import { ThrottlerModule } from '@nestjs/throttler';
import { BullModule } from '@nestjs/bull';
import { WinstonModule } from 'nest-winston';
import * as winston from 'winston';

// 功能模块
import { AuthModule } from './modules/auth/auth.module';
import { UsersModule } from './modules/users/users.module';
import { ProjectsModule } from './modules/projects/projects.module';
import { ServiceProvidersModule } from './modules/service-providers/service-providers.module';
import { QuotesModule } from './modules/quotes/quotes.module';
import { FilesModule } from './modules/files/files.module';
import { NotificationsModule } from './modules/notifications/notifications.module';
import { ReviewsModule } from './modules/reviews/reviews.module';
import { PaymentsModule } from './modules/payments/payments.module';
import { TasksModule } from './modules/tasks/tasks.module';
import { I18nModule } from './i18n/i18n.module';
import { DesignToolsModule } from './modules/design-tools/design-tools.module';
import { MockDatabaseService } from './common/services/mock-database.service';
import { HealthModule } from './modules/health/health.module';

// 共享模块
import { DatabaseModule as SharedDatabaseModule } from './shared/database/database.module';
import { DatabaseModule as ConditionalDatabaseModule } from './modules/database/database.module';
import { RedisModule } from './shared/redis/redis.module';
import { EmailModule } from './shared/email/email.module';
import { SmsModule } from './shared/sms/sms.module';
import { TranslationModule } from './shared/translation/translation.module';

// 配置
import { databaseConfig } from './config/database.config';
import { redisConfig } from './config/redis.config';
import { authConfig } from './config/auth.config';
import { emailConfig } from './config/email.config';
import { smsConfig } from './config/sms.config';

// 加载环境变量
import { config } from 'dotenv';
config({ path: '.env.development' });

// 检查环境变量
console.log('🔍 Environment Check:');
console.log('  USE_MOCK_DATABASE:', process.env.USE_MOCK_DATABASE);
console.log('  NODE_ENV:', process.env.NODE_ENV);
console.log('  MONGODB_URI:', process.env.MONGODB_URI ? 'Configured' : 'Not configured');
import { fileConfig } from './config/file.config';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      load: [
        databaseConfig,
        redisConfig,
        authConfig,
        emailConfig,
        smsConfig,
        fileConfig,
      ],
      envFilePath: ['.env.local', '.env'],
    }),

    // 日志模块
    WinstonModule.forRootAsync({
      useFactory: (configService: ConfigService) => ({
        transports: [
          new winston.transports.Console({
            level: configService.get('LOG_LEVEL') || 'info',
            format: winston.format.combine(
              winston.format.timestamp(),
              winston.format.colorize(),
              winston.format.simple(),
            ),
          }),
          new winston.transports.File({
            filename: 'logs/error.log',
            level: 'error',
            format: winston.format.combine(
              winston.format.timestamp(),
              winston.format.json(),
            ),
          }),
          new winston.transports.File({
            filename: 'logs/combined.log',
            format: winston.format.combine(
              winston.format.timestamp(),
              winston.format.json(),
            ),
          }),
        ],
      }),
      inject: [ConfigService],
    }),

    // 数据库模块 - 条件加载
    ...(process.env.USE_MOCK_DATABASE === 'true' ? [] : [
      MongooseModule.forRootAsync({
        useFactory: (configService: ConfigService) => {
          console.log('🔧 Connecting to MongoDB Database');
          return {
            uri: configService.get<string>('database.uri'),
            ...configService.get('database.options'),
          };
        },
        inject: [ConfigService],
      })
    ]),

    // 缓存模块 - 始终使用内存缓存
    CacheModule.register({
      isGlobal: true,
      ttl: 300, // 5分钟默认TTL
      max: 1000, // 最大缓存项数
    }),

    // 限流模块
    ThrottlerModule.forRootAsync({
      useFactory: (configService: ConfigService) => ({
        ttl: configService.get('THROTTLE_TTL') || 60,
        limit: configService.get('THROTTLE_LIMIT') || 100,
      }),
      inject: [ConfigService],
    }),

    // 队列模块 - 条件加载
    ...(process.env.USE_MOCK_DATABASE === 'true' ? [] : [
      BullModule.forRootAsync({
        useFactory: (configService: ConfigService) => ({
          redis: {
            host: configService.get('redis.host'),
            port: configService.get('redis.port'),
            password: configService.get('redis.password'),
          },
        }),
        inject: [ConfigService],
      })
    ]),

    // 共享模块
    SharedDatabaseModule,
    RedisModule,
    EmailModule,
    SmsModule,
    TranslationModule,

    // 功能模块
    AuthModule,
    UsersModule,
    ProjectsModule,
    ServiceProvidersModule,
    QuotesModule,
    FilesModule,
    NotificationsModule,
    ReviewsModule,
    PaymentsModule,
    TasksModule,
    I18nModule,
    DesignToolsModule,
    HealthModule,
  ],
  controllers: [],
  providers: [
    // 条件提供MockDatabaseService
    ...(process.env.USE_MOCK_DATABASE === 'true' ? [MockDatabaseService] : []),
  ],
})
export class AppModule {}
