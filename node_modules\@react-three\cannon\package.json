{"name": "@react-three/cannon", "version": "6.6.0", "description": "physics based hooks for react-three-fiber", "keywords": ["cannon", "three", "react", "react-three-fiber", "physics"], "author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>> (https://github.com/codynova)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/bjornstar)"], "homepage": "https://github.com/pmndrs/use-cannon/tree/master/packages/react-three-cannon#readme", "repository": "github:pmndrs/use-cannon", "license": "MIT", "module": "dist/index.js", "types": "dist/index.d.ts", "sideEffects": false, "scripts": {"build": "tsc && rollup -c", "clean": "rm -rf dist/*", "test": "echo tests are missing", "eslint": "eslint .", "eslint-fix": "eslint --fix .", "prettier": "prettier --list-different .", "prettier-fix": "prettier --write ."}, "dependencies": {"@pmndrs/cannon-worker-api": "^2.4.0", "cannon-es": "^0.20.0", "cannon-es-debugger": "^1.0.0"}, "devDependencies": {"@babel/core": "^7.17.8", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/preset-env": "^7.16.11", "@babel/preset-react": "^7.16.7", "@babel/preset-typescript": "^7.16.7", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-node-resolve": "^13.1.3", "@types/react": "^18.0.5", "@types/three": "^0.155.0", "@typescript-eslint/eslint-plugin": "^5.17.0", "@typescript-eslint/parser": "^5.17.0", "eslint": "^8.12.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-es": "^4.1.0", "eslint-plugin-react": "^7.29.4", "eslint-plugin-simple-import-sort": "^7.0.0", "eslint-plugin-typescript-enum": "^2.1.0", "prettier": "^2.6.1", "rollup": "^2.70.1", "typescript": "^4.6.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": "eslint --cache --fix", "*.{js,jsx,ts,tsx,md}": "prettier --write"}, "peerDependencies": {"@react-three/fiber": ">=8", "react": ">=18", "three": ">=0.139"}}