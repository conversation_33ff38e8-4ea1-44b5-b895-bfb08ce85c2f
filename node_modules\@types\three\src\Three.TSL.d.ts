import * as TSL from "./nodes/TSL.js";

export const BRDF_GGX: typeof TSL.BRDF_GGX;
export const BRDF_Lambert: typeof TSL.BRDF_Lambert;
export const BasicShadowFilter: typeof TSL.BasicShadowFilter;
export const Break: typeof TSL.Break;
export const Continue: typeof TSL.Continue;
export const DFGApprox: typeof TSL.DFGApprox;
export const D_GGX: typeof TSL.D_GGX;
export const Discard: typeof TSL.Discard;
export const EPSILON: typeof TSL.EPSILON;
export const F_Schlick: typeof TSL.F_Schlick;
export const Fn: typeof TSL.Fn;
export const INFINITY: typeof TSL.INFINITY;
export const If: typeof TSL.If;
export const Switch: typeof TSL.Switch;
export const Loop: typeof TSL.Loop;
export const NodeShaderStage: typeof TSL.NodeShaderStage;
export const NodeType: typeof TSL.NodeType;
export const NodeUpdateType: typeof TSL.NodeUpdateType;
export const NodeAccess: typeof TSL.NodeAccess;
export const PCFShadowFilter: typeof TSL.PCFShadowFilter;
export const PCFSoftShadowFilter: typeof TSL.PCFSoftShadowFilter;
export const PI: typeof TSL.PI;
export const PI2: typeof TSL.PI2;
export const Return: typeof TSL.Return;
export const Schlick_to_F0: typeof TSL.Schlick_to_F0;
export const ScriptableNodeResources: typeof TSL.ScriptableNodeResources;
export const ShaderNode: typeof TSL.ShaderNode;
export const TBNViewMatrix: typeof TSL.TBNViewMatrix;
export const VSMShadowFilter: typeof TSL.VSMShadowFilter;
export const V_GGX_SmithCorrelated: typeof TSL.V_GGX_SmithCorrelated;
export const abs: typeof TSL.abs;
export const acesFilmicToneMapping: typeof TSL.acesFilmicToneMapping;
export const acos: typeof TSL.acos;
export const add: typeof TSL.add;
export const addNodeElement: typeof TSL.addNodeElement;
export const agxToneMapping: typeof TSL.agxToneMapping;
export const all: typeof TSL.all;
export const alphaT: typeof TSL.alphaT;
export const and: typeof TSL.and;
export const anisotropy: typeof TSL.anisotropy;
export const anisotropyB: typeof TSL.anisotropyB;
export const anisotropyT: typeof TSL.anisotropyT;
export const any: typeof TSL.any;
export const append: typeof TSL.append;
export const array: typeof TSL.array;
export const arrayBuffer: typeof TSL.arrayBuffer;
export const asin: typeof TSL.asin;
export const assign: typeof TSL.assign;
export const atan: typeof TSL.atan;
export const atan2: typeof TSL.atan2;
export const atomicAdd: typeof TSL.atomicAdd;
export const atomicAnd: typeof TSL.atomicAnd;
export const atomicFunc: typeof TSL.atomicFunc;
export const atomicMax: typeof TSL.atomicMax;
export const atomicMin: typeof TSL.atomicMin;
export const atomicOr: typeof TSL.atomicOr;
export const atomicStore: typeof TSL.atomicStore;
export const atomicSub: typeof TSL.atomicSub;
export const atomicXor: typeof TSL.atomicXor;
export const atomicLoad: typeof TSL.atomicLoad;
export const attenuationColor: typeof TSL.attenuationColor;
export const attenuationDistance: typeof TSL.attenuationDistance;
export const attribute: typeof TSL.attribute;
export const attributeArray: typeof TSL.attributeArray;
export const backgroundBlurriness: typeof TSL.backgroundBlurriness;
export const backgroundIntensity: typeof TSL.backgroundIntensity;
export const backgroundRotation: typeof TSL.backgroundRotation;
export const batch: typeof TSL.batch;
export const bentNormalView: typeof TSL.bentNormalView;
export const billboarding: typeof TSL.billboarding;
export const bitAnd: typeof TSL.bitAnd;
export const bitNot: typeof TSL.bitNot;
export const bitOr: typeof TSL.bitOr;
export const bitXor: typeof TSL.bitXor;
export const bitangentGeometry: typeof TSL.bitangentGeometry;
export const bitangentLocal: typeof TSL.bitangentLocal;
export const bitangentView: typeof TSL.bitangentView;
export const bitangentWorld: typeof TSL.bitangentWorld;
export const bitcast: typeof TSL.bitcast;
export const blendBurn: typeof TSL.blendBurn;
export const blendColor: typeof TSL.blendColor;
export const blendDodge: typeof TSL.blendDodge;
export const blendOverlay: typeof TSL.blendOverlay;
export const blendScreen: typeof TSL.blendScreen;
export const blur: typeof TSL.blur;
export const bool: typeof TSL.bool;
export const buffer: typeof TSL.buffer;
export const bufferAttribute: typeof TSL.bufferAttribute;
export const bumpMap: typeof TSL.bumpMap;
export const burn: typeof TSL.burn;
export const bvec2: typeof TSL.bvec2;
export const bvec3: typeof TSL.bvec3;
export const bvec4: typeof TSL.bvec4;
export const bypass: typeof TSL.bypass;
export const cache: typeof TSL.cache;
export const call: typeof TSL.call;
export const cameraFar: typeof TSL.cameraFar;
export const cameraIndex: typeof TSL.cameraIndex;
export const cameraNear: typeof TSL.cameraNear;
export const cameraNormalMatrix: typeof TSL.cameraNormalMatrix;
export const cameraPosition: typeof TSL.cameraPosition;
export const cameraProjectionMatrix: typeof TSL.cameraProjectionMatrix;
export const cameraProjectionMatrixInverse: typeof TSL.cameraProjectionMatrixInverse;
export const cameraViewMatrix: typeof TSL.cameraViewMatrix;
export const cameraWorldMatrix: typeof TSL.cameraWorldMatrix;
export const cbrt: typeof TSL.cbrt;
export const cdl: typeof TSL.cdl;
export const ceil: typeof TSL.ceil;
export const checker: typeof TSL.checker;
export const cineonToneMapping: typeof TSL.cineonToneMapping;
export const clamp: typeof TSL.clamp;
export const clearcoat: typeof TSL.clearcoat;
export const clearcoatRoughness: typeof TSL.clearcoatRoughness;
export const code: typeof TSL.code;
export const color: typeof TSL.color;
export const colorSpaceToWorking: typeof TSL.colorSpaceToWorking;
export const colorToDirection: typeof TSL.colorToDirection;
export const compute: typeof TSL.compute;
export const computeSkinning: typeof TSL.computeSkinning;
export const Const: typeof TSL.Const;
export const context: typeof TSL.context;
export const convert: typeof TSL.convert;
export const convertColorSpace: typeof TSL.convertColorSpace;
export const convertToTexture: typeof TSL.convertToTexture;
export const cos: typeof TSL.cos;
export const cross: typeof TSL.cross;
export const cubeTexture: typeof TSL.cubeTexture;
export const dFdx: typeof TSL.dFdx;
export const dFdy: typeof TSL.dFdy;
export const dashSize: typeof TSL.dashSize;
export const debug: typeof TSL.debug;
export const decrement: typeof TSL.decrement;
export const decrementBefore: typeof TSL.decrementBefore;
export const defaultBuildStages: typeof TSL.defaultBuildStages;
export const defaultShaderStages: typeof TSL.defaultShaderStages;
export const defined: typeof TSL.defined;
export const degrees: typeof TSL.degrees;
export const deltaTime: typeof TSL.deltaTime;
export const densityFog: typeof TSL.densityFog;
export const densityFogFactor: typeof TSL.densityFogFactor;
export const depth: typeof TSL.depth;
export const depthPass: typeof TSL.depthPass;
export const difference: typeof TSL.difference;
export const diffuseColor: typeof TSL.diffuseColor;
export const directPointLight: typeof TSL.directPointLight;
export const directionToColor: typeof TSL.directionToColor;
export const dispersion: typeof TSL.dispersion;
export const distance: typeof TSL.distance;
export const div: typeof TSL.div;
export const dodge: typeof TSL.dodge;
export const dot: typeof TSL.dot;
export const drawIndex: typeof TSL.drawIndex;
export const dynamicBufferAttribute: typeof TSL.dynamicBufferAttribute;
export const element: typeof TSL.element;
export const emissive: typeof TSL.emissive;
export const equal: typeof TSL.equal;
export const equals: typeof TSL.equals;
export const equirectUV: typeof TSL.equirectUV;
export const exp: typeof TSL.exp;
export const exp2: typeof TSL.exp2;
export const expression: typeof TSL.expression;
export const faceDirection: typeof TSL.faceDirection;
export const faceForward: typeof TSL.faceForward;
export const faceforward: typeof TSL.faceforward;
export const float: typeof TSL.float;
export const floor: typeof TSL.floor;
export const fog: typeof TSL.fog;
export const fract: typeof TSL.fract;
export const frameGroup: typeof TSL.frameGroup;
export const frameId: typeof TSL.frameId;
export const frontFacing: typeof TSL.frontFacing;
export const fwidth: typeof TSL.fwidth;
export const gain: typeof TSL.gain;
export const gapSize: typeof TSL.gapSize;
export const getConstNodeType: typeof TSL.getConstNodeType;
export const getCurrentStack: typeof TSL.getCurrentStack;
export const getDirection: typeof TSL.getDirection;
export const getDistanceAttenuation: typeof TSL.getDistanceAttenuation;
export const getGeometryRoughness: typeof TSL.getGeometryRoughness;
export const getNormalFromDepth: typeof TSL.getNormalFromDepth;
export const getParallaxCorrectNormal: typeof TSL.getParallaxCorrectNormal;
export const getRoughness: typeof TSL.getRoughness;
export const getScreenPosition: typeof TSL.getScreenPosition;
export const getShIrradianceAt: typeof TSL.getShIrradianceAt;
export const getTextureIndex: typeof TSL.getTextureIndex;
export const getViewPosition: typeof TSL.getViewPosition;
export const getShadowMaterial: typeof TSL.getShadowMaterial;
export const getShadowRenderObjectFunction: typeof TSL.getShadowRenderObjectFunction;
export const glsl: typeof TSL.glsl;
export const glslFn: typeof TSL.glslFn;
export const grayscale: typeof TSL.grayscale;
export const greaterThan: typeof TSL.greaterThan;
export const greaterThanEqual: typeof TSL.greaterThanEqual;
export const hash: typeof TSL.hash;
export const highpModelNormalViewMatrix: typeof TSL.highpModelNormalViewMatrix;
export const highPrecisionModelViewMatrix: typeof TSL.highpModelViewMatrix;
export const hue: typeof TSL.hue;
export const increment: typeof TSL.increment;
export const incrementBefore: typeof TSL.incrementBefore;
export const instance: typeof TSL.instance;
export const instanceIndex: typeof TSL.instanceIndex;
export const instancedArray: typeof TSL.instancedArray;
export const instancedBufferAttribute: typeof TSL.instancedBufferAttribute;
export const instancedDynamicBufferAttribute: typeof TSL.instancedDynamicBufferAttribute;
export const instancedMesh: typeof TSL.instancedMesh;
export const int: typeof TSL.int;
export const inverseSqrt: typeof TSL.inverseSqrt;
export const inversesqrt: typeof TSL.inversesqrt;
export const invocationLocalIndex: typeof TSL.invocationLocalIndex;
export const invocationSubgroupIndex: typeof TSL.invocationSubgroupIndex;
export const ior: typeof TSL.ior;
export const iridescence: typeof TSL.iridescence;
export const iridescenceIOR: typeof TSL.iridescenceIOR;
export const iridescenceThickness: typeof TSL.iridescenceThickness;
export const ivec2: typeof TSL.ivec2;
export const ivec3: typeof TSL.ivec3;
export const ivec4: typeof TSL.ivec4;
export const js: typeof TSL.js;
export const label: typeof TSL.label;
export const length: typeof TSL.length;
export const lengthSq: typeof TSL.lengthSq;
export const lessThan: typeof TSL.lessThan;
export const lessThanEqual: typeof TSL.lessThanEqual;
export const lightPosition: typeof TSL.lightPosition;
export const lightShadowMatrix: typeof TSL.lightShadowMatrix;
export const lightTargetDirection: typeof TSL.lightTargetDirection;
export const lightTargetPosition: typeof TSL.lightTargetPosition;
export const lightViewPosition: typeof TSL.lightViewPosition;
export const lightingContext: typeof TSL.lightingContext;
export const lights: typeof TSL.lights;
export const linearDepth: typeof TSL.linearDepth;
export const linearToneMapping: typeof TSL.linearToneMapping;
export const localId: typeof TSL.localId;
export const globalId: typeof TSL.globalId;
export const log: typeof TSL.log;
export const log2: typeof TSL.log2;
export const logarithmicDepthToViewZ: typeof TSL.logarithmicDepthToViewZ;
export const luminance: typeof TSL.luminance;
export const mediumpModelViewMatrix: typeof TSL.mediumpModelViewMatrix;
export const mat2: typeof TSL.mat2;
export const mat3: typeof TSL.mat3;
export const mat4: typeof TSL.mat4;
export const matcapUV: typeof TSL.matcapUV;
export const materialAO: typeof TSL.materialAO;
export const materialAlphaTest: typeof TSL.materialAlphaTest;
export const materialAnisotropy: typeof TSL.materialAnisotropy;
export const materialAnisotropyVector: typeof TSL.materialAnisotropyVector;
export const materialAttenuationColor: typeof TSL.materialAttenuationColor;
export const materialAttenuationDistance: typeof TSL.materialAttenuationDistance;
export const materialClearcoat: typeof TSL.materialClearcoat;
export const materialClearcoatNormal: typeof TSL.materialClearcoatNormal;
export const materialClearcoatRoughness: typeof TSL.materialClearcoatRoughness;
export const materialColor: typeof TSL.materialColor;
export const materialDispersion: typeof TSL.materialDispersion;
export const materialEmissive: typeof TSL.materialEmissive;
export const materialIOR: typeof TSL.materialIOR;
export const materialIridescence: typeof TSL.materialIridescence;
export const materialIridescenceIOR: typeof TSL.materialIridescenceIOR;
export const materialIridescenceThickness: typeof TSL.materialIridescenceThickness;
export const materialLightMap: typeof TSL.materialLightMap;
export const materialLineDashOffset: typeof TSL.materialLineDashOffset;
export const materialLineDashSize: typeof TSL.materialLineDashSize;
export const materialLineGapSize: typeof TSL.materialLineGapSize;
export const materialLineScale: typeof TSL.materialLineScale;
export const materialLineWidth: typeof TSL.materialLineWidth;
export const materialMetalness: typeof TSL.materialMetalness;
export const materialNormal: typeof TSL.materialNormal;
export const materialOpacity: typeof TSL.materialOpacity;
export const materialPointSize: typeof TSL.materialPointSize;
export const materialReference: typeof TSL.materialReference;
export const materialReflectivity: typeof TSL.materialReflectivity;
export const materialRefractionRatio: typeof TSL.materialRefractionRatio;
export const materialRotation: typeof TSL.materialRotation;
export const materialRoughness: typeof TSL.materialRoughness;
export const materialSheen: typeof TSL.materialSheen;
export const materialSheenRoughness: typeof TSL.materialSheenRoughness;
export const materialShininess: typeof TSL.materialShininess;
export const materialSpecular: typeof TSL.materialSpecular;
export const materialSpecularColor: typeof TSL.materialSpecularColor;
export const materialSpecularIntensity: typeof TSL.materialSpecularIntensity;
export const materialSpecularStrength: typeof TSL.materialSpecularStrength;
export const materialThickness: typeof TSL.materialThickness;
export const materialTransmission: typeof TSL.materialTransmission;
export const max: typeof TSL.max;
export const maxMipLevel: typeof TSL.maxMipLevel;
export const metalness: typeof TSL.metalness;
export const min: typeof TSL.min;
export const mix: typeof TSL.mix;
export const mixElement: typeof TSL.mixElement;
export const mod: typeof TSL.mod;
export const modInt: typeof TSL.modInt;
export const modelDirection: typeof TSL.modelDirection;
export const modelNormalMatrix: typeof TSL.modelNormalMatrix;
export const modelPosition: typeof TSL.modelPosition;
export const modelRadius: typeof TSL.modelRadius;
export const modelScale: typeof TSL.modelScale;
export const modelViewMatrix: typeof TSL.modelViewMatrix;
export const modelViewPosition: typeof TSL.modelViewPosition;
export const modelViewProjection: typeof TSL.modelViewProjection;
export const modelWorldMatrix: typeof TSL.modelWorldMatrix;
export const modelWorldMatrixInverse: typeof TSL.modelWorldMatrixInverse;
export const morphReference: typeof TSL.morphReference;
export const mrt: typeof TSL.mrt;
export const mul: typeof TSL.mul;
export const mx_aastep: typeof TSL.mx_aastep;
export const mx_cell_noise_float: typeof TSL.mx_cell_noise_float;
export const mx_contrast: typeof TSL.mx_contrast;
export const mx_fractal_noise_float: typeof TSL.mx_fractal_noise_float;
export const mx_fractal_noise_vec2: typeof TSL.mx_fractal_noise_vec2;
export const mx_fractal_noise_vec3: typeof TSL.mx_fractal_noise_vec3;
export const mx_fractal_noise_vec4: typeof TSL.mx_fractal_noise_vec4;
export const mx_hsvtorgb: typeof TSL.mx_hsvtorgb;
export const mx_noise_float: typeof TSL.mx_noise_float;
export const mx_noise_vec3: typeof TSL.mx_noise_vec3;
export const mx_noise_vec4: typeof TSL.mx_noise_vec4;
export const mx_ramplr: typeof TSL.mx_ramplr;
export const mx_ramptb: typeof TSL.mx_ramptb;
export const mx_rgbtohsv: typeof TSL.mx_rgbtohsv;
export const mx_safepower: typeof TSL.mx_safepower;
export const mx_splitlr: typeof TSL.mx_splitlr;
export const mx_splittb: typeof TSL.mx_splittb;
export const mx_srgb_texture_to_lin_rec709: typeof TSL.mx_srgb_texture_to_lin_rec709;
export const mx_transform_uv: typeof TSL.mx_transform_uv;
export const mx_worley_noise_float: typeof TSL.mx_worley_noise_float;
export const mx_worley_noise_vec2: typeof TSL.mx_worley_noise_vec2;
export const mx_worley_noise_vec3: typeof TSL.mx_worley_noise_vec3;
export const negate: typeof TSL.negate;
export const neutralToneMapping: typeof TSL.neutralToneMapping;
export const nodeArray: typeof TSL.nodeArray;
export const nodeImmutable: typeof TSL.nodeImmutable;
export const nodeObject: typeof TSL.nodeObject;
export const nodeObjects: typeof TSL.nodeObjects;
export const nodeProxy: typeof TSL.nodeProxy;
export const normalFlat: typeof TSL.normalFlat;
export const normalGeometry: typeof TSL.normalGeometry;
export const normalLocal: typeof TSL.normalLocal;
export const normalMap: typeof TSL.normalMap;
export const normalView: typeof TSL.normalView;
export const normalViewGeometry: typeof TSL.normalViewGeometry;
export const normalWorld: typeof TSL.normalWorld;
export const normalWorldGeometry: typeof TSL.normalWorldGeometry;
export const normalize: typeof TSL.normalize;
export const not: typeof TSL.not;
export const notEqual: typeof TSL.notEqual;
export const numWorkgroups: typeof TSL.numWorkgroups;
export const objectDirection: typeof TSL.objectDirection;
export const objectGroup: typeof TSL.objectGroup;
export const objectPosition: typeof TSL.objectPosition;
export const objectRadius: typeof TSL.objectRadius;
export const objectScale: typeof TSL.objectScale;
export const objectViewPosition: typeof TSL.objectViewPosition;
export const objectWorldMatrix: typeof TSL.objectWorldMatrix;
export const oneMinus: typeof TSL.oneMinus;
export const or: typeof TSL.or;
export const orthographicDepthToViewZ: typeof TSL.orthographicDepthToViewZ;
export const oscSawtooth: typeof TSL.oscSawtooth;
export const oscSine: typeof TSL.oscSine;
export const oscSquare: typeof TSL.oscSquare;
export const oscTriangle: typeof TSL.oscTriangle;
export const output: typeof TSL.output;
export const outputStruct: typeof TSL.outputStruct;
export const overlay: typeof TSL.overlay;
export const overloadingFn: typeof TSL.overloadingFn;
export const parabola: typeof TSL.parabola;
export const parallaxDirection: typeof TSL.parallaxDirection;
export const parallaxUV: typeof TSL.parallaxUV;
export const parameter: typeof TSL.parameter;
export const pass: typeof TSL.pass;
export const passTexture: typeof TSL.passTexture;
export const pcurve: typeof TSL.pcurve;
export const perspectiveDepthToViewZ: typeof TSL.perspectiveDepthToViewZ;
export const pmremTexture: typeof TSL.pmremTexture;
export const pointUV: typeof TSL.pointUV;
export const pointWidth: typeof TSL.pointWidth;
export const positionGeometry: typeof TSL.positionGeometry;
export const positionLocal: typeof TSL.positionLocal;
export const positionPrevious: typeof TSL.positionPrevious;
export const positionView: typeof TSL.positionView;
export const positionViewDirection: typeof TSL.positionViewDirection;
export const positionWorld: typeof TSL.positionWorld;
export const positionWorldDirection: typeof TSL.positionWorldDirection;
export const posterize: typeof TSL.posterize;
export const pow: typeof TSL.pow;
export const pow2: typeof TSL.pow2;
export const pow3: typeof TSL.pow3;
export const pow4: typeof TSL.pow4;
export const premultiplyAlpha: typeof TSL.premultiplyAlpha;
export const property: typeof TSL.property;
export const radians: typeof TSL.radians;
export const rand: typeof TSL.rand;
export const range: typeof TSL.range;
export const rangeFog: typeof TSL.rangeFog;
export const rangeFogFactor: typeof TSL.rangeFogFactor;
export const reciprocal: typeof TSL.reciprocal;
export const lightProjectionUV: typeof TSL.lightProjectionUV;
export const reference: typeof TSL.reference;
export const referenceBuffer: typeof TSL.referenceBuffer;
export const reflect: typeof TSL.reflect;
export const reflectVector: typeof TSL.reflectVector;
export const reflectView: typeof TSL.reflectView;
export const reflector: typeof TSL.reflector;
export const refract: typeof TSL.refract;
export const refractVector: typeof TSL.refractVector;
export const refractView: typeof TSL.refractView;
export const reinhardToneMapping: typeof TSL.reinhardToneMapping;
export const remap: typeof TSL.remap;
export const remapClamp: typeof TSL.remapClamp;
export const renderGroup: typeof TSL.renderGroup;
export const renderOutput: typeof TSL.renderOutput;
export const rendererReference: typeof TSL.rendererReference;
export const rotate: typeof TSL.rotate;
export const rotateUV: typeof TSL.rotateUV;
export const roughness: typeof TSL.roughness;
export const round: typeof TSL.round;
export const rtt: typeof TSL.rtt;
export const sRGBTransferEOTF: typeof TSL.sRGBTransferEOTF;
export const sRGBTransferOETF: typeof TSL.sRGBTransferOETF;
export const sample: typeof TSL.sample;
export const sampler: typeof TSL.sampler;
export const samplerComparison: typeof TSL.samplerComparison;
export const saturate: typeof TSL.saturate;
export const saturation: typeof TSL.saturation;
export const screen: typeof TSL.screen;
export const screenCoordinate: typeof TSL.screenCoordinate;
export const screenSize: typeof TSL.screenSize;
export const screenUV: typeof TSL.screenUV;
export const scriptable: typeof TSL.scriptable;
export const scriptableValue: typeof TSL.scriptableValue;
export const select: typeof TSL.select;
export const setCurrentStack: typeof TSL.setCurrentStack;
export const shaderStages: typeof TSL.shaderStages;
export const shadow: typeof TSL.shadow;
export const pointShadow: typeof TSL.pointShadow;
export const shadowPositionWorld: typeof TSL.shadowPositionWorld;
export const sharedUniformGroup: typeof TSL.sharedUniformGroup;
export const shapeCircle: typeof TSL.shapeCircle;
export const sheen: typeof TSL.sheen;
export const sheenRoughness: typeof TSL.sheenRoughness;
export const shiftLeft: typeof TSL.shiftLeft;
export const shiftRight: typeof TSL.shiftRight;
export const shininess: typeof TSL.shininess;
export const sign: typeof TSL.sign;
export const sin: typeof TSL.sin;
export const sinc: typeof TSL.sinc;
export const skinning: typeof TSL.skinning;
export const smoothstep: typeof TSL.smoothstep;
export const smoothstepElement: typeof TSL.smoothstepElement;
export const specularColor: typeof TSL.specularColor;
export const specularF90: typeof TSL.specularF90;
export const spherizeUV: typeof TSL.spherizeUV;
export const split: typeof TSL.split;
export const spritesheetUV: typeof TSL.spritesheetUV;
export const sqrt: typeof TSL.sqrt;
export const stack: typeof TSL.stack;
export const step: typeof TSL.step;
export const storage: typeof TSL.storage;
export const storageBarrier: typeof TSL.storageBarrier;
export const storageObject: typeof TSL.storageObject;
export const storageTexture: typeof TSL.storageTexture;
export const string: typeof TSL.string;
export const struct: typeof TSL.struct;
export const sub: typeof TSL.sub;
export const subBuild: typeof TSL.subBuild;
export const subgroupIndex: typeof TSL.subgroupIndex;
export const subgroupSize: typeof TSL.subgroupSize;
export const tan: typeof TSL.tan;
export const tangentGeometry: typeof TSL.tangentGeometry;
export const tangentLocal: typeof TSL.tangentLocal;
export const tangentView: typeof TSL.tangentView;
export const tangentWorld: typeof TSL.tangentWorld;
export const temp: typeof TSL.temp;
export const texture: typeof TSL.texture;
export const texture3D: typeof TSL.texture3D;
export const textureBarrier: typeof TSL.textureBarrier;
export const textureBicubic: typeof TSL.textureBicubic;
export const textureBicubicLevel: typeof TSL.textureBicubicLevel;
export const textureCubeUV: typeof TSL.textureCubeUV;
export const textureLoad: typeof TSL.textureLoad;
export const textureSize: typeof TSL.textureSize;
export const textureStore: typeof TSL.textureStore;
export const thickness: typeof TSL.thickness;
export const time: typeof TSL.time;
export const timerDelta: typeof TSL.timerDelta;
export const timerGlobal: typeof TSL.timerGlobal;
export const timerLocal: typeof TSL.timerLocal;
export const toneMapping: typeof TSL.toneMapping;
export const toneMappingExposure: typeof TSL.toneMappingExposure;
export const toonOutlinePass: typeof TSL.toonOutlinePass;
export const transformDirection: typeof TSL.transformDirection;
export const transformNormal: typeof TSL.transformNormal;
export const transformNormalToView: typeof TSL.transformNormalToView;
export const transformedClearcoatNormalView: typeof TSL.transformedClearcoatNormalView;
export const transformedNormalView: typeof TSL.transformedNormalView;
export const transformedNormalWorld: typeof TSL.transformedNormalWorld;
export const transmission: typeof TSL.transmission;
export const transpose: typeof TSL.transpose;
export const triNoise3D: typeof TSL.triNoise3D;
export const triplanarTexture: typeof TSL.triplanarTexture;
export const triplanarTextures: typeof TSL.triplanarTextures;
export const trunc: typeof TSL.trunc;
export const uint: typeof TSL.uint;
export const uniform: typeof TSL.uniform;
export const uniformCubeTexture: typeof TSL.uniformCubeTexture;
export const uniformArray: typeof TSL.uniformArray;
export const uniformGroup: typeof TSL.uniformGroup;
export const uniformTexture: typeof TSL.uniformTexture;
export const unpremultiplyAlpha: typeof TSL.unpremultiplyAlpha;
export const userData: typeof TSL.userData;
export const uv: typeof TSL.uv;
export const uvec2: typeof TSL.uvec2;
export const uvec3: typeof TSL.uvec3;
export const uvec4: typeof TSL.uvec4;
export const Var: typeof TSL.Var;
export const varying: typeof TSL.varying;
export const varyingProperty: typeof TSL.varyingProperty;
export const vec2: typeof TSL.vec2;
export const vec3: typeof TSL.vec3;
export const vec4: typeof TSL.vec4;
export const vectorComponents: typeof TSL.vectorComponents;
export const velocity: typeof TSL.velocity;
export const vertexColor: typeof TSL.vertexColor;
export const vertexIndex: typeof TSL.vertexIndex;
export const vibrance: typeof TSL.vibrance;
export const viewZToLogarithmicDepth: typeof TSL.viewZToLogarithmicDepth;
export const viewZToOrthographicDepth: typeof TSL.viewZToOrthographicDepth;
export const viewZToPerspectiveDepth: typeof TSL.viewZToPerspectiveDepth;
export const viewport: typeof TSL.viewport;
export const viewportCoordinate: typeof TSL.viewportCoordinate;
export const viewportDepthTexture: typeof TSL.viewportDepthTexture;
export const viewportLinearDepth: typeof TSL.viewportLinearDepth;
export const viewportMipTexture: typeof TSL.viewportMipTexture;
export const viewportResolution: typeof TSL.viewportResolution;
export const viewportSafeUV: typeof TSL.viewportSafeUV;
export const viewportSharedTexture: typeof TSL.viewportSharedTexture;
export const viewportSize: typeof TSL.viewportSize;
export const viewportTexture: typeof TSL.viewportTexture;
export const viewportUV: typeof TSL.viewportUV;
export const wgsl: typeof TSL.wgsl;
export const wgslFn: typeof TSL.wgslFn;
export const workgroupArray: typeof TSL.workgroupArray;
export const workgroupBarrier: typeof TSL.workgroupBarrier;
export const workgroupId: typeof TSL.workgroupId;
export const workingToColorSpace: typeof TSL.workingToColorSpace;
export const xor: typeof TSL.xor;

export type { ProxiedObject, ShaderNodeObject, Swizzable } from "./nodes/TSL.js";
