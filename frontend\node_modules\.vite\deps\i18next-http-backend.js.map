{"version": 3, "sources": ["../../i18next-http-backend/esm/utils.js", "../../i18next-http-backend/esm/request.js", "../../i18next-http-backend/esm/index.js"], "sourcesContent": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nvar arr = [];\nvar each = arr.forEach;\nvar slice = arr.slice;\nexport function defaults(obj) {\n  each.call(slice.call(arguments, 1), function (source) {\n    if (source) {\n      for (var prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\nexport function hasXMLHttpRequest() {\n  return typeof XMLHttpRequest === 'function' || (typeof XMLHttpRequest === \"undefined\" ? \"undefined\" : _typeof(XMLHttpRequest)) === 'object';\n}\nfunction isPromise(maybePromise) {\n  return !!maybePromise && typeof maybePromise.then === 'function';\n}\nexport function makePromise(maybePromise) {\n  if (isPromise(maybePromise)) {\n    return maybePromise;\n  }\n  return Promise.resolve(maybePromise);\n}", "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nimport { hasXMLHttpRequest } from './utils.js';\nvar fetchApi = typeof fetch === 'function' ? fetch : undefined;\nif (typeof global !== 'undefined' && global.fetch) {\n  fetchApi = global.fetch;\n} else if (typeof window !== 'undefined' && window.fetch) {\n  fetchApi = window.fetch;\n}\nvar XmlHttpRequestApi;\nif (hasXMLHttpRequest()) {\n  if (typeof global !== 'undefined' && global.XMLHttpRequest) {\n    XmlHttpRequestApi = global.XMLHttpRequest;\n  } else if (typeof window !== 'undefined' && window.XMLHttpRequest) {\n    XmlHttpRequestApi = window.XMLHttpRequest;\n  }\n}\nvar ActiveXObjectApi;\nif (typeof ActiveXObject === 'function') {\n  if (typeof global !== 'undefined' && global.ActiveXObject) {\n    ActiveXObjectApi = global.ActiveXObject;\n  } else if (typeof window !== 'undefined' && window.ActiveXObject) {\n    ActiveXObjectApi = window.ActiveXObject;\n  }\n}\nif (typeof fetchApi !== 'function') fetchApi = undefined;\nif (!fetchApi && !XmlHttpRequestApi && !ActiveXObjectApi) {\n  try {\n    import('cross-fetch').then(function (mod) {\n      fetchApi = mod.default;\n    }).catch(function () {});\n  } catch (e) {}\n}\nvar addQueryString = function addQueryString(url, params) {\n  if (params && _typeof(params) === 'object') {\n    var queryString = '';\n    for (var paramName in params) {\n      queryString += '&' + encodeURIComponent(paramName) + '=' + encodeURIComponent(params[paramName]);\n    }\n    if (!queryString) return url;\n    url = url + (url.indexOf('?') !== -1 ? '&' : '?') + queryString.slice(1);\n  }\n  return url;\n};\nvar fetchIt = function fetchIt(url, fetchOptions, callback, altFetch) {\n  var resolver = function resolver(response) {\n    if (!response.ok) return callback(response.statusText || 'Error', {\n      status: response.status\n    });\n    response.text().then(function (data) {\n      callback(null, {\n        status: response.status,\n        data: data\n      });\n    }).catch(callback);\n  };\n  if (altFetch) {\n    var altResponse = altFetch(url, fetchOptions);\n    if (altResponse instanceof Promise) {\n      altResponse.then(resolver).catch(callback);\n      return;\n    }\n  }\n  if (typeof fetch === 'function') {\n    fetch(url, fetchOptions).then(resolver).catch(callback);\n  } else {\n    fetchApi(url, fetchOptions).then(resolver).catch(callback);\n  }\n};\nvar omitFetchOptions = false;\nvar requestWithFetch = function requestWithFetch(options, url, payload, callback) {\n  if (options.queryStringParams) {\n    url = addQueryString(url, options.queryStringParams);\n  }\n  var headers = _objectSpread({}, typeof options.customHeaders === 'function' ? options.customHeaders() : options.customHeaders);\n  if (typeof window === 'undefined' && typeof global !== 'undefined' && typeof global.process !== 'undefined' && global.process.versions && global.process.versions.node) {\n    headers['User-Agent'] = \"i18next-http-backend (node/\".concat(global.process.version, \"; \").concat(global.process.platform, \" \").concat(global.process.arch, \")\");\n  }\n  if (payload) headers['Content-Type'] = 'application/json';\n  var reqOptions = typeof options.requestOptions === 'function' ? options.requestOptions(payload) : options.requestOptions;\n  var fetchOptions = _objectSpread({\n    method: payload ? 'POST' : 'GET',\n    body: payload ? options.stringify(payload) : undefined,\n    headers: headers\n  }, omitFetchOptions ? {} : reqOptions);\n  var altFetch = typeof options.alternateFetch === 'function' && options.alternateFetch.length >= 1 ? options.alternateFetch : undefined;\n  try {\n    fetchIt(url, fetchOptions, callback, altFetch);\n  } catch (e) {\n    if (!reqOptions || Object.keys(reqOptions).length === 0 || !e.message || e.message.indexOf('not implemented') < 0) {\n      return callback(e);\n    }\n    try {\n      Object.keys(reqOptions).forEach(function (opt) {\n        delete fetchOptions[opt];\n      });\n      fetchIt(url, fetchOptions, callback, altFetch);\n      omitFetchOptions = true;\n    } catch (err) {\n      callback(err);\n    }\n  }\n};\nvar requestWithXmlHttpRequest = function requestWithXmlHttpRequest(options, url, payload, callback) {\n  if (payload && _typeof(payload) === 'object') {\n    payload = addQueryString('', payload).slice(1);\n  }\n  if (options.queryStringParams) {\n    url = addQueryString(url, options.queryStringParams);\n  }\n  try {\n    var x = XmlHttpRequestApi ? new XmlHttpRequestApi() : new ActiveXObjectApi('MSXML2.XMLHTTP.3.0');\n    x.open(payload ? 'POST' : 'GET', url, 1);\n    if (!options.crossDomain) {\n      x.setRequestHeader('X-Requested-With', 'XMLHttpRequest');\n    }\n    x.withCredentials = !!options.withCredentials;\n    if (payload) {\n      x.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');\n    }\n    if (x.overrideMimeType) {\n      x.overrideMimeType('application/json');\n    }\n    var h = options.customHeaders;\n    h = typeof h === 'function' ? h() : h;\n    if (h) {\n      for (var i in h) {\n        x.setRequestHeader(i, h[i]);\n      }\n    }\n    x.onreadystatechange = function () {\n      x.readyState > 3 && callback(x.status >= 400 ? x.statusText : null, {\n        status: x.status,\n        data: x.responseText\n      });\n    };\n    x.send(payload);\n  } catch (e) {\n    console && console.log(e);\n  }\n};\nvar request = function request(options, url, payload, callback) {\n  if (typeof payload === 'function') {\n    callback = payload;\n    payload = undefined;\n  }\n  callback = callback || function () {};\n  if (fetchApi && url.indexOf('file:') !== 0) {\n    return requestWithFetch(options, url, payload, callback);\n  }\n  if (hasXMLHttpRequest() || typeof ActiveXObject === 'function') {\n    return requestWithXmlHttpRequest(options, url, payload, callback);\n  }\n  callback(new Error('No fetch and no xhr implementation found!'));\n};\nexport default request;", "function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { makePromise } from './utils.js';\nimport request from './request.js';\nvar getDefaults = function getDefaults() {\n  return {\n    loadPath: '/locales/{{lng}}/{{ns}}.json',\n    addPath: '/locales/add/{{lng}}/{{ns}}',\n    parse: function parse(data) {\n      return JSON.parse(data);\n    },\n    stringify: JSON.stringify,\n    parsePayload: function parsePayload(namespace, key, fallbackValue) {\n      return _defineProperty({}, key, fallbackValue || '');\n    },\n    parseLoadPayload: function parseLoadPayload(languages, namespaces) {\n      return undefined;\n    },\n    request: request,\n    reloadInterval: typeof window !== 'undefined' ? false : 60 * 60 * 1000,\n    customHeaders: {},\n    queryStringParams: {},\n    crossDomain: false,\n    withCredentials: false,\n    overrideMimeType: false,\n    requestOptions: {\n      mode: 'cors',\n      credentials: 'same-origin',\n      cache: 'default'\n    }\n  };\n};\nvar Backend = function () {\n  function Backend(services) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var allOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    _classCallCheck(this, Backend);\n    this.services = services;\n    this.options = options;\n    this.allOptions = allOptions;\n    this.type = 'backend';\n    this.init(services, options, allOptions);\n  }\n  return _createClass(Backend, [{\n    key: \"init\",\n    value: function init(services) {\n      var _this = this;\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var allOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      this.services = services;\n      this.options = _objectSpread(_objectSpread(_objectSpread({}, getDefaults()), this.options || {}), options);\n      this.allOptions = allOptions;\n      if (this.services && this.options.reloadInterval) {\n        var timer = setInterval(function () {\n          return _this.reload();\n        }, this.options.reloadInterval);\n        if (_typeof(timer) === 'object' && typeof timer.unref === 'function') timer.unref();\n      }\n    }\n  }, {\n    key: \"readMulti\",\n    value: function readMulti(languages, namespaces, callback) {\n      this._readAny(languages, languages, namespaces, namespaces, callback);\n    }\n  }, {\n    key: \"read\",\n    value: function read(language, namespace, callback) {\n      this._readAny([language], language, [namespace], namespace, callback);\n    }\n  }, {\n    key: \"_readAny\",\n    value: function _readAny(languages, loadUrlLanguages, namespaces, loadUrlNamespaces, callback) {\n      var _this2 = this;\n      var loadPath = this.options.loadPath;\n      if (typeof this.options.loadPath === 'function') {\n        loadPath = this.options.loadPath(languages, namespaces);\n      }\n      loadPath = makePromise(loadPath);\n      loadPath.then(function (resolvedLoadPath) {\n        if (!resolvedLoadPath) return callback(null, {});\n        var url = _this2.services.interpolator.interpolate(resolvedLoadPath, {\n          lng: languages.join('+'),\n          ns: namespaces.join('+')\n        });\n        _this2.loadUrl(url, callback, loadUrlLanguages, loadUrlNamespaces);\n      });\n    }\n  }, {\n    key: \"loadUrl\",\n    value: function loadUrl(url, callback, languages, namespaces) {\n      var _this3 = this;\n      var lng = typeof languages === 'string' ? [languages] : languages;\n      var ns = typeof namespaces === 'string' ? [namespaces] : namespaces;\n      var payload = this.options.parseLoadPayload(lng, ns);\n      this.options.request(this.options, url, payload, function (err, res) {\n        if (res && (res.status >= 500 && res.status < 600 || !res.status)) return callback('failed loading ' + url + '; status code: ' + res.status, true);\n        if (res && res.status >= 400 && res.status < 500) return callback('failed loading ' + url + '; status code: ' + res.status, false);\n        if (!res && err && err.message) {\n          var errorMessage = err.message.toLowerCase();\n          var isNetworkError = ['failed', 'fetch', 'network', 'load'].find(function (term) {\n            return errorMessage.indexOf(term) > -1;\n          });\n          if (isNetworkError) {\n            return callback('failed loading ' + url + ': ' + err.message, true);\n          }\n        }\n        if (err) return callback(err, false);\n        var ret, parseErr;\n        try {\n          if (typeof res.data === 'string') {\n            ret = _this3.options.parse(res.data, languages, namespaces);\n          } else {\n            ret = res.data;\n          }\n        } catch (e) {\n          parseErr = 'failed parsing ' + url + ' to json';\n        }\n        if (parseErr) return callback(parseErr, false);\n        callback(null, ret);\n      });\n    }\n  }, {\n    key: \"create\",\n    value: function create(languages, namespace, key, fallbackValue, callback) {\n      var _this4 = this;\n      if (!this.options.addPath) return;\n      if (typeof languages === 'string') languages = [languages];\n      var payload = this.options.parsePayload(namespace, key, fallbackValue);\n      var finished = 0;\n      var dataArray = [];\n      var resArray = [];\n      languages.forEach(function (lng) {\n        var addPath = _this4.options.addPath;\n        if (typeof _this4.options.addPath === 'function') {\n          addPath = _this4.options.addPath(lng, namespace);\n        }\n        var url = _this4.services.interpolator.interpolate(addPath, {\n          lng: lng,\n          ns: namespace\n        });\n        _this4.options.request(_this4.options, url, payload, function (data, res) {\n          finished += 1;\n          dataArray.push(data);\n          resArray.push(res);\n          if (finished === languages.length) {\n            if (typeof callback === 'function') callback(dataArray, resArray);\n          }\n        });\n      });\n    }\n  }, {\n    key: \"reload\",\n    value: function reload() {\n      var _this5 = this;\n      var _this$services = this.services,\n        backendConnector = _this$services.backendConnector,\n        languageUtils = _this$services.languageUtils,\n        logger = _this$services.logger;\n      var currentLanguage = backendConnector.language;\n      if (currentLanguage && currentLanguage.toLowerCase() === 'cimode') return;\n      var toLoad = [];\n      var append = function append(lng) {\n        var lngs = languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(function (l) {\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      append(currentLanguage);\n      if (this.allOptions.preload) this.allOptions.preload.forEach(function (l) {\n        return append(l);\n      });\n      toLoad.forEach(function (lng) {\n        _this5.allOptions.ns.forEach(function (ns) {\n          backendConnector.read(lng, ns, 'read', null, null, function (err, data) {\n            if (err) logger.warn(\"loading namespace \".concat(ns, \" for language \").concat(lng, \" failed\"), err);\n            if (!err && data) logger.log(\"loaded namespace \".concat(ns, \" for language \").concat(lng), data);\n            backendConnector.loaded(\"\".concat(lng, \"|\").concat(ns), err, data);\n          });\n        });\n      });\n    }\n  }]);\n}();\nBackend.type = 'backend';\nexport default Backend;"], "mappings": ";;;AAAA,SAAS,QAAQ,GAAG;AAAE;AAA2B,SAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUA,IAAG;AAAE,WAAO,OAAOA;AAAA,EAAG,IAAI,SAAUA,IAAG;AAAE,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EAAG,GAAG,QAAQ,CAAC;AAAG;AAC7T,IAAI,MAAM,CAAC;AACX,IAAI,OAAO,IAAI;AACf,IAAI,QAAQ,IAAI;AAWT,SAAS,oBAAoB;AAClC,SAAO,OAAO,mBAAmB,eAAe,OAAO,mBAAmB,cAAc,cAAc,QAAQ,cAAc,OAAO;AACrI;AACA,SAAS,UAAU,cAAc;AAC/B,SAAO,CAAC,CAAC,gBAAgB,OAAO,aAAa,SAAS;AACxD;AACO,SAAS,YAAY,cAAc;AACxC,MAAI,UAAU,YAAY,GAAG;AAC3B,WAAO;AAAA,EACT;AACA,SAAO,QAAQ,QAAQ,YAAY;AACrC;;;ACzBA,SAAS,QAAQ,GAAG,GAAG;AAAE,MAAI,IAAI,OAAO,KAAK,CAAC;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,IAAI,OAAO,sBAAsB,CAAC;AAAG,UAAM,IAAI,EAAE,OAAO,SAAUC,IAAG;AAAE,aAAO,OAAO,yBAAyB,GAAGA,EAAC,EAAE;AAAA,IAAY,CAAC,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,EAAG;AAAE,SAAO;AAAG;AAC9P,SAAS,cAAc,GAAG;AAAE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,QAAI,IAAI,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAAG,QAAI,IAAI,QAAQ,OAAO,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAUA,IAAG;AAAE,sBAAgB,GAAGA,IAAG,EAAEA,EAAC,CAAC;AAAA,IAAG,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,CAAC,CAAC,IAAI,QAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUA,IAAG;AAAE,aAAO,eAAe,GAAGA,IAAG,OAAO,yBAAyB,GAAGA,EAAC,CAAC;AAAA,IAAG,CAAC;AAAA,EAAG;AAAE,SAAO;AAAG;AACtb,SAAS,gBAAgB,GAAG,GAAG,GAAG;AAAE,UAAQ,IAAI,eAAe,CAAC,MAAM,IAAI,OAAO,eAAe,GAAG,GAAG,EAAE,OAAO,GAAG,YAAY,MAAI,cAAc,MAAI,UAAU,KAAG,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AAAG;AACnL,SAAS,eAAe,GAAG;AAAE,MAAI,IAAI,aAAa,GAAG,QAAQ;AAAG,SAAO,YAAYC,SAAQ,CAAC,IAAI,IAAI,IAAI;AAAI;AAC5G,SAAS,aAAa,GAAG,GAAG;AAAE,MAAI,YAAYA,SAAQ,CAAC,KAAK,CAAC,EAAG,QAAO;AAAG,MAAI,IAAI,EAAE,OAAO,WAAW;AAAG,MAAI,WAAW,GAAG;AAAE,QAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAAG,QAAI,YAAYA,SAAQ,CAAC,EAAG,QAAO;AAAG,UAAM,IAAI,UAAU,8CAA8C;AAAA,EAAG;AAAE,UAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAAG;AAC3T,SAASA,SAAQ,GAAG;AAAE;AAA2B,SAAOA,WAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,IAAG;AAAE,WAAO,OAAOA;AAAA,EAAG,IAAI,SAAUA,IAAG;AAAE,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EAAG,GAAGD,SAAQ,CAAC;AAAG;AAE7T,IAAI,WAAW,OAAO,UAAU,aAAa,QAAQ;AACrD,IAAI,OAAO,WAAW,eAAe,OAAO,OAAO;AACjD,aAAW,OAAO;AACpB,WAAW,OAAO,WAAW,eAAe,OAAO,OAAO;AACxD,aAAW,OAAO;AACpB;AACA,IAAI;AACJ,IAAI,kBAAkB,GAAG;AACvB,MAAI,OAAO,WAAW,eAAe,OAAO,gBAAgB;AAC1D,wBAAoB,OAAO;AAAA,EAC7B,WAAW,OAAO,WAAW,eAAe,OAAO,gBAAgB;AACjE,wBAAoB,OAAO;AAAA,EAC7B;AACF;AACA,IAAI;AACJ,IAAI,OAAO,kBAAkB,YAAY;AACvC,MAAI,OAAO,WAAW,eAAe,OAAO,eAAe;AACzD,uBAAmB,OAAO;AAAA,EAC5B,WAAW,OAAO,WAAW,eAAe,OAAO,eAAe;AAChE,uBAAmB,OAAO;AAAA,EAC5B;AACF;AACA,IAAI,OAAO,aAAa,WAAY,YAAW;AAC/C,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,kBAAkB;AACxD,MAAI;AACF,WAAO,gCAAa,EAAE,KAAK,SAAU,KAAK;AACxC,iBAAW,IAAI;AAAA,IACjB,CAAC,EAAE,MAAM,WAAY;AAAA,IAAC,CAAC;AAAA,EACzB,SAAS,GAAG;AAAA,EAAC;AACf;AACA,IAAI,iBAAiB,SAASE,gBAAe,KAAK,QAAQ;AACxD,MAAI,UAAUF,SAAQ,MAAM,MAAM,UAAU;AAC1C,QAAI,cAAc;AAClB,aAAS,aAAa,QAAQ;AAC5B,qBAAe,MAAM,mBAAmB,SAAS,IAAI,MAAM,mBAAmB,OAAO,SAAS,CAAC;AAAA,IACjG;AACA,QAAI,CAAC,YAAa,QAAO;AACzB,UAAM,OAAO,IAAI,QAAQ,GAAG,MAAM,KAAK,MAAM,OAAO,YAAY,MAAM,CAAC;AAAA,EACzE;AACA,SAAO;AACT;AACA,IAAI,UAAU,SAASG,SAAQ,KAAK,cAAc,UAAU,UAAU;AACpE,MAAI,WAAW,SAASC,UAAS,UAAU;AACzC,QAAI,CAAC,SAAS,GAAI,QAAO,SAAS,SAAS,cAAc,SAAS;AAAA,MAChE,QAAQ,SAAS;AAAA,IACnB,CAAC;AACD,aAAS,KAAK,EAAE,KAAK,SAAU,MAAM;AACnC,eAAS,MAAM;AAAA,QACb,QAAQ,SAAS;AAAA,QACjB;AAAA,MACF,CAAC;AAAA,IACH,CAAC,EAAE,MAAM,QAAQ;AAAA,EACnB;AACA,MAAI,UAAU;AACZ,QAAI,cAAc,SAAS,KAAK,YAAY;AAC5C,QAAI,uBAAuB,SAAS;AAClC,kBAAY,KAAK,QAAQ,EAAE,MAAM,QAAQ;AACzC;AAAA,IACF;AAAA,EACF;AACA,MAAI,OAAO,UAAU,YAAY;AAC/B,UAAM,KAAK,YAAY,EAAE,KAAK,QAAQ,EAAE,MAAM,QAAQ;AAAA,EACxD,OAAO;AACL,aAAS,KAAK,YAAY,EAAE,KAAK,QAAQ,EAAE,MAAM,QAAQ;AAAA,EAC3D;AACF;AACA,IAAI,mBAAmB;AACvB,IAAI,mBAAmB,SAASC,kBAAiB,SAAS,KAAK,SAAS,UAAU;AAChF,MAAI,QAAQ,mBAAmB;AAC7B,UAAM,eAAe,KAAK,QAAQ,iBAAiB;AAAA,EACrD;AACA,MAAI,UAAU,cAAc,CAAC,GAAG,OAAO,QAAQ,kBAAkB,aAAa,QAAQ,cAAc,IAAI,QAAQ,aAAa;AAC7H,MAAI,OAAO,WAAW,eAAe,OAAO,WAAW,eAAe,OAAO,OAAO,YAAY,eAAe,OAAO,QAAQ,YAAY,OAAO,QAAQ,SAAS,MAAM;AACtK,YAAQ,YAAY,IAAI,8BAA8B,OAAO,OAAO,QAAQ,SAAS,IAAI,EAAE,OAAO,OAAO,QAAQ,UAAU,GAAG,EAAE,OAAO,OAAO,QAAQ,MAAM,GAAG;AAAA,EACjK;AACA,MAAI,QAAS,SAAQ,cAAc,IAAI;AACvC,MAAI,aAAa,OAAO,QAAQ,mBAAmB,aAAa,QAAQ,eAAe,OAAO,IAAI,QAAQ;AAC1G,MAAI,eAAe,cAAc;AAAA,IAC/B,QAAQ,UAAU,SAAS;AAAA,IAC3B,MAAM,UAAU,QAAQ,UAAU,OAAO,IAAI;AAAA,IAC7C;AAAA,EACF,GAAG,mBAAmB,CAAC,IAAI,UAAU;AACrC,MAAI,WAAW,OAAO,QAAQ,mBAAmB,cAAc,QAAQ,eAAe,UAAU,IAAI,QAAQ,iBAAiB;AAC7H,MAAI;AACF,YAAQ,KAAK,cAAc,UAAU,QAAQ;AAAA,EAC/C,SAAS,GAAG;AACV,QAAI,CAAC,cAAc,OAAO,KAAK,UAAU,EAAE,WAAW,KAAK,CAAC,EAAE,WAAW,EAAE,QAAQ,QAAQ,iBAAiB,IAAI,GAAG;AACjH,aAAO,SAAS,CAAC;AAAA,IACnB;AACA,QAAI;AACF,aAAO,KAAK,UAAU,EAAE,QAAQ,SAAU,KAAK;AAC7C,eAAO,aAAa,GAAG;AAAA,MACzB,CAAC;AACD,cAAQ,KAAK,cAAc,UAAU,QAAQ;AAC7C,yBAAmB;AAAA,IACrB,SAAS,KAAK;AACZ,eAAS,GAAG;AAAA,IACd;AAAA,EACF;AACF;AACA,IAAI,4BAA4B,SAASC,2BAA0B,SAAS,KAAK,SAAS,UAAU;AAClG,MAAI,WAAWN,SAAQ,OAAO,MAAM,UAAU;AAC5C,cAAU,eAAe,IAAI,OAAO,EAAE,MAAM,CAAC;AAAA,EAC/C;AACA,MAAI,QAAQ,mBAAmB;AAC7B,UAAM,eAAe,KAAK,QAAQ,iBAAiB;AAAA,EACrD;AACA,MAAI;AACF,QAAI,IAAI,oBAAoB,IAAI,kBAAkB,IAAI,IAAI,iBAAiB,oBAAoB;AAC/F,MAAE,KAAK,UAAU,SAAS,OAAO,KAAK,CAAC;AACvC,QAAI,CAAC,QAAQ,aAAa;AACxB,QAAE,iBAAiB,oBAAoB,gBAAgB;AAAA,IACzD;AACA,MAAE,kBAAkB,CAAC,CAAC,QAAQ;AAC9B,QAAI,SAAS;AACX,QAAE,iBAAiB,gBAAgB,mCAAmC;AAAA,IACxE;AACA,QAAI,EAAE,kBAAkB;AACtB,QAAE,iBAAiB,kBAAkB;AAAA,IACvC;AACA,QAAI,IAAI,QAAQ;AAChB,QAAI,OAAO,MAAM,aAAa,EAAE,IAAI;AACpC,QAAI,GAAG;AACL,eAAS,KAAK,GAAG;AACf,UAAE,iBAAiB,GAAG,EAAE,CAAC,CAAC;AAAA,MAC5B;AAAA,IACF;AACA,MAAE,qBAAqB,WAAY;AACjC,QAAE,aAAa,KAAK,SAAS,EAAE,UAAU,MAAM,EAAE,aAAa,MAAM;AAAA,QAClE,QAAQ,EAAE;AAAA,QACV,MAAM,EAAE;AAAA,MACV,CAAC;AAAA,IACH;AACA,MAAE,KAAK,OAAO;AAAA,EAChB,SAAS,GAAG;AACV,eAAW,QAAQ,IAAI,CAAC;AAAA,EAC1B;AACF;AACA,IAAI,UAAU,SAASO,SAAQ,SAAS,KAAK,SAAS,UAAU;AAC9D,MAAI,OAAO,YAAY,YAAY;AACjC,eAAW;AACX,cAAU;AAAA,EACZ;AACA,aAAW,YAAY,WAAY;AAAA,EAAC;AACpC,MAAI,YAAY,IAAI,QAAQ,OAAO,MAAM,GAAG;AAC1C,WAAO,iBAAiB,SAAS,KAAK,SAAS,QAAQ;AAAA,EACzD;AACA,MAAI,kBAAkB,KAAK,OAAO,kBAAkB,YAAY;AAC9D,WAAO,0BAA0B,SAAS,KAAK,SAAS,QAAQ;AAAA,EAClE;AACA,WAAS,IAAI,MAAM,2CAA2C,CAAC;AACjE;AACA,IAAO,kBAAQ;;;AC/Jf,SAASC,SAAQ,GAAG;AAAE;AAA2B,SAAOA,WAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,IAAG;AAAE,WAAO,OAAOA;AAAA,EAAG,IAAI,SAAUA,IAAG;AAAE,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EAAG,GAAGD,SAAQ,CAAC;AAAG;AAC7T,SAASE,SAAQ,GAAG,GAAG;AAAE,MAAI,IAAI,OAAO,KAAK,CAAC;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,IAAI,OAAO,sBAAsB,CAAC;AAAG,UAAM,IAAI,EAAE,OAAO,SAAUC,IAAG;AAAE,aAAO,OAAO,yBAAyB,GAAGA,EAAC,EAAE;AAAA,IAAY,CAAC,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,EAAG;AAAE,SAAO;AAAG;AAC9P,SAASC,eAAc,GAAG;AAAE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,QAAI,IAAI,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAAG,QAAI,IAAIF,SAAQ,OAAO,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAUC,IAAG;AAAE,MAAAE,iBAAgB,GAAGF,IAAG,EAAEA,EAAC,CAAC;AAAA,IAAG,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,CAAC,CAAC,IAAID,SAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUC,IAAG;AAAE,aAAO,eAAe,GAAGA,IAAG,OAAO,yBAAyB,GAAGA,EAAC,CAAC;AAAA,IAAG,CAAC;AAAA,EAAG;AAAE,SAAO;AAAG;AACtb,SAAS,gBAAgB,GAAG,GAAG;AAAE,MAAI,EAAE,aAAa,GAAI,OAAM,IAAI,UAAU,mCAAmC;AAAG;AAClH,SAAS,kBAAkB,GAAG,GAAG;AAAE,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAAE,QAAI,IAAI,EAAE,CAAC;AAAG,MAAE,aAAa,EAAE,cAAc,OAAI,EAAE,eAAe,MAAI,WAAW,MAAM,EAAE,WAAW,OAAK,OAAO,eAAe,GAAGG,gBAAe,EAAE,GAAG,GAAG,CAAC;AAAA,EAAG;AAAE;AACvO,SAAS,aAAa,GAAG,GAAG,GAAG;AAAE,SAAO,KAAK,kBAAkB,EAAE,WAAW,CAAC,GAAG,KAAK,kBAAkB,GAAG,CAAC,GAAG,OAAO,eAAe,GAAG,aAAa,EAAE,UAAU,MAAG,CAAC,GAAG;AAAG;AAC1K,SAASD,iBAAgB,GAAG,GAAG,GAAG;AAAE,UAAQ,IAAIC,gBAAe,CAAC,MAAM,IAAI,OAAO,eAAe,GAAG,GAAG,EAAE,OAAO,GAAG,YAAY,MAAI,cAAc,MAAI,UAAU,KAAG,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AAAG;AACnL,SAASA,gBAAe,GAAG;AAAE,MAAI,IAAIC,cAAa,GAAG,QAAQ;AAAG,SAAO,YAAYP,SAAQ,CAAC,IAAI,IAAI,IAAI;AAAI;AAC5G,SAASO,cAAa,GAAG,GAAG;AAAE,MAAI,YAAYP,SAAQ,CAAC,KAAK,CAAC,EAAG,QAAO;AAAG,MAAI,IAAI,EAAE,OAAO,WAAW;AAAG,MAAI,WAAW,GAAG;AAAE,QAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAAG,QAAI,YAAYA,SAAQ,CAAC,EAAG,QAAO;AAAG,UAAM,IAAI,UAAU,8CAA8C;AAAA,EAAG;AAAE,UAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAAG;AAG3T,IAAI,cAAc,SAASQ,eAAc;AACvC,SAAO;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,IACT,OAAO,SAAS,MAAM,MAAM;AAC1B,aAAO,KAAK,MAAM,IAAI;AAAA,IACxB;AAAA,IACA,WAAW,KAAK;AAAA,IAChB,cAAc,SAAS,aAAa,WAAW,KAAK,eAAe;AACjE,aAAOH,iBAAgB,CAAC,GAAG,KAAK,iBAAiB,EAAE;AAAA,IACrD;AAAA,IACA,kBAAkB,SAAS,iBAAiB,WAAW,YAAY;AACjE,aAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,IACT,gBAAgB,OAAO,WAAW,cAAc,QAAQ,KAAK,KAAK;AAAA,IAClE,eAAe,CAAC;AAAA,IAChB,mBAAmB,CAAC;AAAA,IACpB,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACd,MAAM;AAAA,MACN,aAAa;AAAA,MACb,OAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,IAAI,UAAU,WAAY;AACxB,WAASI,SAAQ,UAAU;AACzB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI,aAAa,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACtF,oBAAgB,MAAMA,QAAO;AAC7B,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,OAAO;AACZ,SAAK,KAAK,UAAU,SAAS,UAAU;AAAA,EACzC;AACA,SAAO,aAAaA,UAAS,CAAC;AAAA,IAC5B,KAAK;AAAA,IACL,OAAO,SAAS,KAAK,UAAU;AAC7B,UAAI,QAAQ;AACZ,UAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,UAAI,aAAa,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACtF,WAAK,WAAW;AAChB,WAAK,UAAUL,eAAcA,eAAcA,eAAc,CAAC,GAAG,YAAY,CAAC,GAAG,KAAK,WAAW,CAAC,CAAC,GAAG,OAAO;AACzG,WAAK,aAAa;AAClB,UAAI,KAAK,YAAY,KAAK,QAAQ,gBAAgB;AAChD,YAAI,QAAQ,YAAY,WAAY;AAClC,iBAAO,MAAM,OAAO;AAAA,QACtB,GAAG,KAAK,QAAQ,cAAc;AAC9B,YAAIJ,SAAQ,KAAK,MAAM,YAAY,OAAO,MAAM,UAAU,WAAY,OAAM,MAAM;AAAA,MACpF;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,WAAW,YAAY,UAAU;AACzD,WAAK,SAAS,WAAW,WAAW,YAAY,YAAY,QAAQ;AAAA,IACtE;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,KAAK,UAAU,WAAW,UAAU;AAClD,WAAK,SAAS,CAAC,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG,WAAW,QAAQ;AAAA,IACtE;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,WAAW,kBAAkB,YAAY,mBAAmB,UAAU;AAC7F,UAAI,SAAS;AACb,UAAI,WAAW,KAAK,QAAQ;AAC5B,UAAI,OAAO,KAAK,QAAQ,aAAa,YAAY;AAC/C,mBAAW,KAAK,QAAQ,SAAS,WAAW,UAAU;AAAA,MACxD;AACA,iBAAW,YAAY,QAAQ;AAC/B,eAAS,KAAK,SAAU,kBAAkB;AACxC,YAAI,CAAC,iBAAkB,QAAO,SAAS,MAAM,CAAC,CAAC;AAC/C,YAAI,MAAM,OAAO,SAAS,aAAa,YAAY,kBAAkB;AAAA,UACnE,KAAK,UAAU,KAAK,GAAG;AAAA,UACvB,IAAI,WAAW,KAAK,GAAG;AAAA,QACzB,CAAC;AACD,eAAO,QAAQ,KAAK,UAAU,kBAAkB,iBAAiB;AAAA,MACnE,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ,KAAK,UAAU,WAAW,YAAY;AAC5D,UAAI,SAAS;AACb,UAAI,MAAM,OAAO,cAAc,WAAW,CAAC,SAAS,IAAI;AACxD,UAAI,KAAK,OAAO,eAAe,WAAW,CAAC,UAAU,IAAI;AACzD,UAAI,UAAU,KAAK,QAAQ,iBAAiB,KAAK,EAAE;AACnD,WAAK,QAAQ,QAAQ,KAAK,SAAS,KAAK,SAAS,SAAU,KAAK,KAAK;AACnE,YAAI,QAAQ,IAAI,UAAU,OAAO,IAAI,SAAS,OAAO,CAAC,IAAI,QAAS,QAAO,SAAS,oBAAoB,MAAM,oBAAoB,IAAI,QAAQ,IAAI;AACjJ,YAAI,OAAO,IAAI,UAAU,OAAO,IAAI,SAAS,IAAK,QAAO,SAAS,oBAAoB,MAAM,oBAAoB,IAAI,QAAQ,KAAK;AACjI,YAAI,CAAC,OAAO,OAAO,IAAI,SAAS;AAC9B,cAAI,eAAe,IAAI,QAAQ,YAAY;AAC3C,cAAI,iBAAiB,CAAC,UAAU,SAAS,WAAW,MAAM,EAAE,KAAK,SAAU,MAAM;AAC/E,mBAAO,aAAa,QAAQ,IAAI,IAAI;AAAA,UACtC,CAAC;AACD,cAAI,gBAAgB;AAClB,mBAAO,SAAS,oBAAoB,MAAM,OAAO,IAAI,SAAS,IAAI;AAAA,UACpE;AAAA,QACF;AACA,YAAI,IAAK,QAAO,SAAS,KAAK,KAAK;AACnC,YAAI,KAAK;AACT,YAAI;AACF,cAAI,OAAO,IAAI,SAAS,UAAU;AAChC,kBAAM,OAAO,QAAQ,MAAM,IAAI,MAAM,WAAW,UAAU;AAAA,UAC5D,OAAO;AACL,kBAAM,IAAI;AAAA,UACZ;AAAA,QACF,SAAS,GAAG;AACV,qBAAW,oBAAoB,MAAM;AAAA,QACvC;AACA,YAAI,SAAU,QAAO,SAAS,UAAU,KAAK;AAC7C,iBAAS,MAAM,GAAG;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,OAAO,WAAW,WAAW,KAAK,eAAe,UAAU;AACzE,UAAI,SAAS;AACb,UAAI,CAAC,KAAK,QAAQ,QAAS;AAC3B,UAAI,OAAO,cAAc,SAAU,aAAY,CAAC,SAAS;AACzD,UAAI,UAAU,KAAK,QAAQ,aAAa,WAAW,KAAK,aAAa;AACrE,UAAI,WAAW;AACf,UAAI,YAAY,CAAC;AACjB,UAAI,WAAW,CAAC;AAChB,gBAAU,QAAQ,SAAU,KAAK;AAC/B,YAAI,UAAU,OAAO,QAAQ;AAC7B,YAAI,OAAO,OAAO,QAAQ,YAAY,YAAY;AAChD,oBAAU,OAAO,QAAQ,QAAQ,KAAK,SAAS;AAAA,QACjD;AACA,YAAI,MAAM,OAAO,SAAS,aAAa,YAAY,SAAS;AAAA,UAC1D;AAAA,UACA,IAAI;AAAA,QACN,CAAC;AACD,eAAO,QAAQ,QAAQ,OAAO,SAAS,KAAK,SAAS,SAAU,MAAM,KAAK;AACxE,sBAAY;AACZ,oBAAU,KAAK,IAAI;AACnB,mBAAS,KAAK,GAAG;AACjB,cAAI,aAAa,UAAU,QAAQ;AACjC,gBAAI,OAAO,aAAa,WAAY,UAAS,WAAW,QAAQ;AAAA,UAClE;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,UAAI,SAAS;AACb,UAAI,iBAAiB,KAAK,UACxB,mBAAmB,eAAe,kBAClC,gBAAgB,eAAe,eAC/B,SAAS,eAAe;AAC1B,UAAI,kBAAkB,iBAAiB;AACvC,UAAI,mBAAmB,gBAAgB,YAAY,MAAM,SAAU;AACnE,UAAI,SAAS,CAAC;AACd,UAAI,SAAS,SAASU,QAAO,KAAK;AAChC,YAAI,OAAO,cAAc,mBAAmB,GAAG;AAC/C,aAAK,QAAQ,SAAU,GAAG;AACxB,cAAI,OAAO,QAAQ,CAAC,IAAI,EAAG,QAAO,KAAK,CAAC;AAAA,QAC1C,CAAC;AAAA,MACH;AACA,aAAO,eAAe;AACtB,UAAI,KAAK,WAAW,QAAS,MAAK,WAAW,QAAQ,QAAQ,SAAU,GAAG;AACxE,eAAO,OAAO,CAAC;AAAA,MACjB,CAAC;AACD,aAAO,QAAQ,SAAU,KAAK;AAC5B,eAAO,WAAW,GAAG,QAAQ,SAAU,IAAI;AACzC,2BAAiB,KAAK,KAAK,IAAI,QAAQ,MAAM,MAAM,SAAU,KAAK,MAAM;AACtE,gBAAI,IAAK,QAAO,KAAK,qBAAqB,OAAO,IAAI,gBAAgB,EAAE,OAAO,KAAK,SAAS,GAAG,GAAG;AAClG,gBAAI,CAAC,OAAO,KAAM,QAAO,IAAI,oBAAoB,OAAO,IAAI,gBAAgB,EAAE,OAAO,GAAG,GAAG,IAAI;AAC/F,6BAAiB,OAAO,GAAG,OAAO,KAAK,GAAG,EAAE,OAAO,EAAE,GAAG,KAAK,IAAI;AAAA,UACnE,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF,CAAC,CAAC;AACJ,EAAE;AACF,QAAQ,OAAO;AACf,IAAO,cAAQ;", "names": ["o", "r", "_typeof", "o", "addQueryString", "fetchIt", "resolver", "requestWithFetch", "requestWithXmlHttpRequest", "request", "_typeof", "o", "ownKeys", "r", "_objectSpread", "_defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "getDefaults", "Backend", "append"]}