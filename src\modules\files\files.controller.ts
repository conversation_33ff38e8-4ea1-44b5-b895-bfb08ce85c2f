import {
  Controller,
  Get,
  Post,
  Delete,
  Param,
  Query,
  UseGuards,
  Request,
  UseInterceptors,
  UploadedFile,
  Body,
  HttpCode,
  HttpStatus,
  Res,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { ThrottlerGuard } from '@nestjs/throttler';
import { Response } from 'express';
import { FilesService } from './files.service';
import { UploadFileDto, FileQueryDto } from './dto/upload-file.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { File } from './schemas/file.schema';
import { PaginatedResult } from '../../common/interfaces/paginated-result.interface';
import { diskStorage } from 'multer';
import { extname } from 'path';
import * as fs from 'fs';

// Multer配置
const storage = diskStorage({
  destination: './uploads',
  filename: (req, file, callback) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    const ext = extname(file.originalname);
    callback(null, `${file.fieldname}-${uniqueSuffix}${ext}`);
  },
});

@ApiTags('Files')
@Controller('files')
@UseGuards(ThrottlerGuard)
export class FilesController {
  constructor(private readonly filesService: FilesService) {}

  @Post('upload')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @UseInterceptors(
    FileInterceptor('file', {
      storage,
      limits: {
        fileSize: 50 * 1024 * 1024, // 50MB
      },
      fileFilter: (req, file, callback) => {
        // 允许的文件类型
        const allowedMimes = [
          'image/jpeg',
          'image/png',
          'image/gif',
          'image/webp',
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'video/mp4',
          'video/mpeg',
          'audio/mpeg',
          'audio/wav',
        ];

        if (allowedMimes.includes(file.mimetype)) {
          callback(null, true);
        } else {
          callback(new Error('File type not allowed'), false);
        }
      },
    }),
  )
  @ApiOperation({ summary: '上传文件' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        category: {
          type: 'string',
          description: '文件分类',
        },
        description: {
          type: 'string',
          description: '文件描述',
        },
        tags: {
          type: 'array',
          items: { type: 'string' },
          description: '文件标签',
        },
        isPublic: {
          type: 'boolean',
          description: '是否公开',
        },
        projectId: {
          type: 'string',
          description: '关联项目ID',
        },
        quoteId: {
          type: 'string',
          description: '关联报价ID',
        },
        serviceProviderId: {
          type: 'string',
          description: '关联服务商ID',
        },
      },
      required: ['file', 'category'],
    },
  })
  @ApiResponse({
    status: 201,
    description: '文件上传成功',
    type: File,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadFileDto: UploadFileDto,
    @Request() req: any,
  ): Promise<File> {
    if (!file) {
      throw new Error('No file uploaded');
    }

    return this.filesService.uploadFile(file, uploadFileDto, req.user);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取文件列表' })
  @ApiResponse({
    status: 200,
    description: '获取文件列表成功',
    type: [File],
  })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiQuery({ name: 'category', required: false, description: '文件分类' })
  @ApiQuery({ name: 'projectId', required: false, description: '项目ID' })
  @ApiQuery({ name: 'quoteId', required: false, description: '报价ID' })
  @ApiQuery({ name: 'serviceProviderId', required: false, description: '服务商ID' })
  @ApiQuery({ name: 'search', required: false, description: '搜索关键词' })
  async findAll(
    @Query() query: FileQueryDto,
    @Request() req: any,
  ): Promise<PaginatedResult<File>> {
    return this.filesService.findAll(query, req.user);
  }

  @Get('stats')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取用户文件统计' })
  @ApiResponse({
    status: 200,
    description: '获取统计信息成功',
  })
  async getUserStats(@Request() req: any): Promise<any> {
    return this.filesService.getUserStats(req.user._id.toString());
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据ID获取文件详情' })
  @ApiResponse({
    status: 200,
    description: '获取文件详情成功',
    type: File,
  })
  @ApiResponse({
    status: 404,
    description: '文件不存在',
  })
  @ApiParam({ name: 'id', description: '文件ID' })
  async findOne(@Param('id') id: string, @Request() req: any): Promise<File> {
    return this.filesService.findOne(id, req.user);
  }

  @Get(':id/download')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '下载文件' })
  @ApiResponse({
    status: 200,
    description: '文件下载成功',
  })
  @ApiResponse({
    status: 404,
    description: '文件不存在',
  })
  @ApiParam({ name: 'id', description: '文件ID' })
  async downloadFile(
    @Param('id') id: string,
    @Request() req: any,
    @Res() res: Response,
  ): Promise<void> {
    const file = await this.filesService.findOne(id, req.user);
    
    // 检查文件是否存在
    if (!fs.existsSync(file.path)) {
      throw new Error('Physical file not found');
    }

    // 设置响应头
    res.setHeader('Content-Type', file.mimeType);
    res.setHeader('Content-Disposition', `attachment; filename="${file.originalName}"`);
    res.setHeader('Content-Length', file.size);

    // 创建文件流并发送
    const fileStream = fs.createReadStream(file.path);
    fileStream.pipe(res);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除文件' })
  @ApiResponse({
    status: 204,
    description: '文件删除成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '文件不存在',
  })
  @ApiParam({ name: 'id', description: '文件ID' })
  async remove(@Param('id') id: string, @Request() req: any): Promise<void> {
    return this.filesService.remove(id, req.user);
  }
}
