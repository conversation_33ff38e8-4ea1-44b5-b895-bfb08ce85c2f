@echo off
echo ========================================
echo HomeReno 开发环境启动脚本
echo ========================================

echo.
echo 1. 检查Docker是否运行...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: Docker未安装或未运行
    echo 请先安装并启动Docker Desktop
    pause
    exit /b 1
)

echo Docker已就绪
echo.

echo 2. 启动数据库服务 (MongoDB + Redis)...
docker-compose up -d

echo.
echo 3. 等待数据库启动完成...
timeout /t 10 /nobreak >nul

echo.
echo 4. 检查数据库连接...
docker-compose ps

echo.
echo 5. 安装依赖包...
call npm install

echo.
echo 6. 创建必要的目录...
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads

echo.
echo 7. 启动应用开发服务器...
echo 应用将在 http://localhost:3000 启动
echo API文档将在 http://localhost:3000/api 可用
echo MongoDB管理界面在 http://localhost:8081 可用
echo.

call npm run start:dev

echo.
echo 应用已停止
pause
