import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';

const AppRouter: React.FC = () => {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<div style={{ padding: '20px' }}>
          <h1>HomeReno</h1>
          <p>Welcome to HomeReno - Your home renovation platform!</p>
          <p>This is a test page to verify the frontend is working.</p>
        </div>} />
        <Route path="*" element={<div style={{ padding: '20px' }}>
          <h1>404 - Page Not Found</h1>
          <p>The page you're looking for doesn't exist.</p>
        </div>} />
      </Routes>
    </BrowserRouter>
  );
};

export default AppRouter;
