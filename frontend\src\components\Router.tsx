import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';

// Layout Components
import MainLayout from './layouts/MainLayout';

// Page Components
import HomePageSimple from '../pages/HomePageSimple';
import ProjectsPage from '../pages/projects/ProjectsPage';
import ContractorsPage from '../pages/contractors/ContractorsPage';
import NotFoundPage from '../pages/NotFoundPage';

const AppRouter: React.FC = () => {
  return (
    <BrowserRouter>
      <Routes>
        {/* Public Routes */}
        <Route path="/" element={<MainLayout />}>
          <Route index element={<HomePageSimple />} />
          <Route path="projects" element={<ProjectsPage />} />
          <Route path="contractors" element={<ContractorsPage />} />
        </Route>

        {/* 404 Page */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </BrowserRouter>
  );
};

export default AppRouter;
