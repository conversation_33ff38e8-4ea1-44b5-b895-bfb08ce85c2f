import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';

// Layout Components
import SimpleLayout from './layouts/SimpleLayout';

// Page Components
import HomePageSimple from '../pages/HomePageSimple';
import ProjectsPage from '../pages/projects/ProjectsPage';
import ContractorsPage from '../pages/contractors/ContractorsPage';
import ApiTestPage from '../pages/ApiTestPage';
import NotFoundPage from '../pages/NotFoundPage';

const AppRouter: React.FC = () => {
  return (
    <BrowserRouter>
      <Routes>
        {/* Public Routes */}
        <Route path="/" element={<SimpleLayout />}>
          <Route index element={<HomePageSimple />} />
          <Route path="projects" element={<ProjectsPage />} />
          <Route path="contractors" element={<ContractorsPage />} />
          <Route path="api-test" element={<ApiTestPage />} />
        </Route>

        {/* 404 Page */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </BrowserRouter>
  );
};

export default AppRouter;
