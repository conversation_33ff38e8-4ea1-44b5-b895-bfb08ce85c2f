import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { DesignToolsController } from './design-tools.controller';
import { DesignToolsService } from './design-tools.service';
import { DesignProject, DesignProjectSchema } from './schemas/design-project.schema';
import { DesignTemplate, DesignTemplateSchema } from './schemas/design-template.schema';
import { DesignAsset, DesignAssetSchema } from './schemas/design-asset.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: DesignProject.name, schema: DesignProjectSchema },
      { name: DesignTemplate.name, schema: DesignTemplateSchema },
      { name: DesignAsset.name, schema: DesignAssetSchema },
    ]),
  ],
  controllers: [DesignToolsController],
  providers: [DesignToolsService],
  exports: [DesignToolsService],
})
export class DesignToolsModule {}
