import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsString, IsNumber, IsBoolean, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

export class QueryDesignProjectsDto {
  @ApiProperty({ description: '页码', minimum: 1, default: 1, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiProperty({ description: '每页数量', minimum: 1, maximum: 100, default: 10, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiProperty({ 
    description: '房屋类型',
    enum: ['apartment', 'house', 'condo', 'townhouse', 'other'],
    required: false
  })
  @IsOptional()
  @IsEnum(['apartment', 'house', 'condo', 'townhouse', 'other'])
  houseType?: string;

  @ApiProperty({ 
    description: '设计风格',
    enum: ['modern', 'traditional', 'contemporary', 'industrial', 'scandinavian', 'minimalist', 'rustic', 'other'],
    required: false
  })
  @IsOptional()
  @IsEnum(['modern', 'traditional', 'contemporary', 'industrial', 'scandinavian', 'minimalist', 'rustic', 'other'])
  style?: string;

  @ApiProperty({ 
    description: '项目状态',
    enum: ['draft', 'in_progress', 'completed', 'shared'],
    required: false
  })
  @IsOptional()
  @IsEnum(['draft', 'in_progress', 'completed', 'shared'])
  status?: string;

  @ApiProperty({ description: '是否只显示公开项目', required: false })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  publicOnly?: boolean;

  @ApiProperty({ description: '搜索关键词', required: false })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({ 
    description: '排序字段',
    enum: ['createdAt', 'updatedAt', 'name'],
    default: 'createdAt',
    required: false
  })
  @IsOptional()
  @IsEnum(['createdAt', 'updatedAt', 'name'])
  sortBy?: string = 'createdAt';

  @ApiProperty({ 
    description: '排序方向',
    enum: ['asc', 'desc'],
    default: 'desc',
    required: false
  })
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: string = 'desc';
}
