{"version": 3, "file": "Gyroscope.js", "sources": ["../../src/misc/Gyroscope.js"], "sourcesContent": ["import { Object3D, Quaternion, Vector3 } from 'three'\n\nconst _translationObject = /* @__PURE__ */ new Vector3()\nconst _quaternionObject = /* @__PURE__ */ new Quaternion()\nconst _scaleObject = /* @__PURE__ */ new Vector3()\n\nconst _translationWorld = /* @__PURE__ */ new Vector3()\nconst _quaternionWorld = /* @__PURE__ */ new Quaternion()\nconst _scaleWorld = /* @__PURE__ */ new Vector3()\n\nclass Gyroscope extends Object3D {\n  constructor() {\n    super()\n  }\n\n  updateMatrixWorld(force) {\n    this.matrixAutoUpdate && this.updateMatrix()\n\n    // update matrixWorld\n\n    if (this.matrixWorldNeedsUpdate || force) {\n      if (this.parent !== null) {\n        this.matrixWorld.multiplyMatrices(this.parent.matrixWorld, this.matrix)\n\n        this.matrixWorld.decompose(_translationWorld, _quaternionWorld, _scaleWorld)\n        this.matrix.decompose(_translationObject, _quaternionObject, _scaleObject)\n\n        this.matrixWorld.compose(_translationWorld, _quaternionObject, _scaleWorld)\n      } else {\n        this.matrixWorld.copy(this.matrix)\n      }\n\n      this.matrixWorldNeedsUpdate = false\n\n      force = true\n    }\n\n    // update children\n\n    for (let i = 0, l = this.children.length; i < l; i++) {\n      this.children[i].updateMatrixWorld(force)\n    }\n  }\n}\n\nexport { Gyroscope }\n"], "names": [], "mappings": ";AAEA,MAAM,qBAAqC,oBAAI,QAAS;AACxD,MAAM,oBAAoC,oBAAI,WAAY;AAC1D,MAAM,eAA+B,oBAAI,QAAS;AAElD,MAAM,oBAAoC,oBAAI,QAAS;AACvD,MAAM,mBAAmC,oBAAI,WAAY;AACzD,MAAM,cAA8B,oBAAI,QAAS;AAEjD,MAAM,kBAAkB,SAAS;AAAA,EAC/B,cAAc;AACZ,UAAO;AAAA,EACR;AAAA,EAED,kBAAkB,OAAO;AACvB,SAAK,oBAAoB,KAAK,aAAc;AAI5C,QAAI,KAAK,0BAA0B,OAAO;AACxC,UAAI,KAAK,WAAW,MAAM;AACxB,aAAK,YAAY,iBAAiB,KAAK,OAAO,aAAa,KAAK,MAAM;AAEtE,aAAK,YAAY,UAAU,mBAAmB,kBAAkB,WAAW;AAC3E,aAAK,OAAO,UAAU,oBAAoB,mBAAmB,YAAY;AAEzE,aAAK,YAAY,QAAQ,mBAAmB,mBAAmB,WAAW;AAAA,MAClF,OAAa;AACL,aAAK,YAAY,KAAK,KAAK,MAAM;AAAA,MAClC;AAED,WAAK,yBAAyB;AAE9B,cAAQ;AAAA,IACT;AAID,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,IAAI,GAAG,KAAK;AACpD,WAAK,SAAS,CAAC,EAAE,kBAAkB,KAAK;AAAA,IACzC;AAAA,EACF;AACH;"}