import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { I18nService } from 'nestjs-i18n';

@Injectable()
export class I18nResponseInterceptor implements NestInterceptor {
  constructor(private readonly i18n: I18nService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const lang = this.getLanguageFromRequest(request);

    return next.handle().pipe(
      map(data => {
        if (data && typeof data === 'object') {
          return this.translateResponse(data, lang);
        }
        return data;
      }),
    );
  }

  private getLanguageFromRequest(request: any): string {
    const langFromQuery = request.query?.lang;
    const langFromHeader = request.headers['x-lang'];
    const langFromAcceptLanguage = request.headers['accept-language'];
    
    if (langFromQuery && ['en', 'zh'].includes(langFromQuery)) {
      return langFromQuery;
    }
    
    if (langFromHeader && ['en', 'zh'].includes(langFromHeader)) {
      return langFromHeader;
    }
    
    if (langFromAcceptLanguage) {
      const languages = langFromAcceptLanguage
        .split(',')
        .map(lang => lang.split(';')[0].trim().toLowerCase());
      
      for (const lang of languages) {
        if (lang.startsWith('zh')) {
          return 'zh';
        }
        if (lang.startsWith('en')) {
          return 'en';
        }
      }
    }
    
    return 'en';
  }

  private translateResponse(data: any, lang: string): any {
    if (Array.isArray(data)) {
      return data.map(item => this.translateResponse(item, lang));
    }

    if (data && typeof data === 'object') {
      const translated = { ...data };

      // 翻译特定字段
      if (data.message && typeof data.message === 'string') {
        try {
          translated.message = this.i18n.translate(data.message, { lang });
        } catch (error) {
          // 如果翻译失败，保持原始消息
          translated.message = data.message;
        }
      }

      // 翻译状态文本
      if (data.status && typeof data.status === 'string') {
        try {
          const statusKey = `common.status.${data.status}`;
          const translatedStatus = this.i18n.translate(statusKey, { lang });
          if (translatedStatus !== statusKey) {
            translated.statusText = translatedStatus;
          }
        } catch (error) {
          // 忽略翻译错误
        }
      }

      // 递归翻译嵌套对象
      Object.keys(translated).forEach(key => {
        if (translated[key] && typeof translated[key] === 'object') {
          translated[key] = this.translateResponse(translated[key], lang);
        }
      });

      return translated;
    }

    return data;
  }
}
