import {
  IsString,
  IsEnum,
  IsOptional,
  IsNumber,
  IsArray,
  IsUrl,
  ValidateNested,
  Min,
  Max,
  Max<PERSON>ength,
  MinLength,
  IsInt,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  BusinessType,
  ServiceCategory,
} from '../schemas/service-provider.schema';

export class CreateBusinessInfoDto {
  @ApiProperty({ description: '公司名称' })
  @IsString()
  @MinLength(1)
  @MaxLength(200)
  companyName: string;

  @ApiProperty({ description: '业务类型', enum: BusinessType })
  @IsEnum(BusinessType)
  businessType: BusinessType;

  @ApiPropertyOptional({ description: '营业执照号' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  licenseNumber?: string;

  @ApiPropertyOptional({ description: '税号' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  taxNumber?: string;

  @ApiPropertyOptional({ description: '成立年份' })
  @IsOptional()
  @IsInt()
  @Min(1900)
  @Max(new Date().getFullYear())
  establishedYear?: number;

  @ApiPropertyOptional({ description: '员工数量' })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(10000)
  employeeCount?: number;

  @ApiPropertyOptional({ description: '公司描述' })
  @IsOptional()
  @IsString()
  @MaxLength(2000)
  description?: string;

  @ApiPropertyOptional({ description: '公司网站' })
  @IsOptional()
  @IsUrl()
  website?: string;

  @ApiPropertyOptional({ description: '营业执照图片', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  licenseImages?: string[];
}

export class CreateServiceAreaDto {
  @ApiProperty({ description: '省份' })
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  province: string;

  @ApiProperty({ description: '服务城市', type: [String] })
  @IsArray()
  @IsString({ each: true })
  cities: string[];

  @ApiPropertyOptional({ description: '服务半径(公里)' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(1000)
  radius?: number;
}

export class CreateServiceOfferedDto {
  @ApiProperty({ description: '服务类别', enum: ServiceCategory })
  @IsEnum(ServiceCategory)
  category: ServiceCategory;

  @ApiProperty({ description: '服务名称' })
  @IsString()
  @MinLength(1)
  @MaxLength(200)
  name: string;

  @ApiPropertyOptional({ description: '服务描述' })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @ApiPropertyOptional({ description: '起始价格' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  startingPrice?: number;

  @ApiPropertyOptional({ description: '价格单位' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  priceUnit?: string;

  @ApiPropertyOptional({ description: '服务图片', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  images?: string[];
}

export class CreateCertificationDto {
  @ApiProperty({ description: '认证名称' })
  @IsString()
  @MinLength(1)
  @MaxLength(200)
  name: string;

  @ApiProperty({ description: '颁发机构' })
  @IsString()
  @MinLength(1)
  @MaxLength(200)
  issuingOrganization: string;

  @ApiPropertyOptional({ description: '认证编号' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  certificationNumber?: string;

  @ApiPropertyOptional({ description: '获得日期' })
  @IsOptional()
  issuedDate?: Date;

  @ApiPropertyOptional({ description: '过期日期' })
  @IsOptional()
  expiryDate?: Date;

  @ApiPropertyOptional({ description: '认证文件URL' })
  @IsOptional()
  @IsUrl()
  documentUrl?: string;
}

export class CreateInsuranceDto {
  @ApiProperty({ description: '保险类型' })
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  type: string;

  @ApiProperty({ description: '保险公司' })
  @IsString()
  @MinLength(1)
  @MaxLength(200)
  provider: string;

  @ApiPropertyOptional({ description: '保单号' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  policyNumber?: string;

  @ApiPropertyOptional({ description: '保险金额' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  coverageAmount?: number;

  @ApiPropertyOptional({ description: '生效日期' })
  @IsOptional()
  effectiveDate?: Date;

  @ApiPropertyOptional({ description: '过期日期' })
  @IsOptional()
  expiryDate?: Date;

  @ApiPropertyOptional({ description: '保单文件URL' })
  @IsOptional()
  @IsUrl()
  documentUrl?: string;
}

export class CreatePortfolioDto {
  @ApiProperty({ description: '项目标题' })
  @IsString()
  @MinLength(1)
  @MaxLength(200)
  title: string;

  @ApiPropertyOptional({ description: '项目描述' })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @ApiPropertyOptional({ description: '项目类别', enum: ServiceCategory })
  @IsOptional()
  @IsEnum(ServiceCategory)
  category?: ServiceCategory;

  @ApiProperty({ description: '项目图片', type: [String] })
  @IsArray()
  @IsString({ each: true })
  images: string[];

  @ApiPropertyOptional({ description: '项目成本' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  cost?: number;

  @ApiPropertyOptional({ description: '项目时长(天)' })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(3650)
  duration?: number;

  @ApiPropertyOptional({ description: '完成日期' })
  @IsOptional()
  completedDate?: Date;

  @ApiPropertyOptional({ description: '客户评价' })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  clientTestimonial?: string;
}

export class CreateServiceProviderDto {
  @ApiProperty({ description: '业务信息', type: CreateBusinessInfoDto })
  @ValidateNested()
  @Type(() => CreateBusinessInfoDto)
  businessInfo: CreateBusinessInfoDto;

  @ApiProperty({ description: '服务区域', type: [CreateServiceAreaDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateServiceAreaDto)
  serviceAreas: CreateServiceAreaDto[];

  @ApiProperty({ description: '提供的服务', type: [CreateServiceOfferedDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateServiceOfferedDto)
  services: CreateServiceOfferedDto[];

  @ApiPropertyOptional({ description: '认证信息', type: [CreateCertificationDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateCertificationDto)
  certifications?: CreateCertificationDto[];

  @ApiPropertyOptional({ description: '保险信息', type: [CreateInsuranceDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateInsuranceDto)
  insurance?: CreateInsuranceDto[];

  @ApiPropertyOptional({ description: '作品集', type: [CreatePortfolioDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreatePortfolioDto)
  portfolio?: CreatePortfolioDto[];
}
