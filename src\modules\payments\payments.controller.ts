import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { PaymentsService } from './payments.service';
import {
  CreatePaymentDto,
  PaymentQueryDto,
  ProcessPaymentDto,
  RefundPaymentDto,
  EscrowReleaseDto,
  UpdatePaymentStatusDto,
} from './dto/create-payment.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Payment } from './schemas/payment.schema';
import { PaginatedResult } from '../../common/interfaces/paginated-result.interface';
import { UserType } from '../users/schemas/user.schema';

@ApiTags('Payments')
@Controller('payments')
@UseGuards(ThrottlerGuard)
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建支付' })
  @ApiResponse({
    status: 201,
    description: '支付创建成功',
    type: Payment,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  async create(@Body() createPaymentDto: CreatePaymentDto, @Request() req: any): Promise<Payment> {
    return this.paymentsService.create(createPaymentDto, req.user);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取支付列表' })
  @ApiResponse({
    status: 200,
    description: '获取支付列表成功',
    type: [Payment],
  })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiQuery({ name: 'type', required: false, description: '支付类型' })
  @ApiQuery({ name: 'status', required: false, description: '支付状态' })
  @ApiQuery({ name: 'method', required: false, description: '支付方式' })
  @ApiQuery({ name: 'currency', required: false, description: '货币' })
  async findAll(@Query() query: PaymentQueryDto, @Request() req: any): Promise<PaginatedResult<Payment>> {
    return this.paymentsService.findAll(query, req.user);
  }

  @Get('stats/:userId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取用户支付统计' })
  @ApiResponse({
    status: 200,
    description: '获取统计信息成功',
  })
  @ApiParam({ name: 'userId', description: '用户ID' })
  async getUserStats(@Param('userId') userId: string): Promise<any> {
    return this.paymentsService.getUserStats(userId);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据ID获取支付详情' })
  @ApiResponse({
    status: 200,
    description: '获取支付详情成功',
    type: Payment,
  })
  @ApiResponse({
    status: 404,
    description: '支付不存在',
  })
  @ApiParam({ name: 'id', description: '支付ID' })
  async findOne(@Param('id') id: string, @Request() req: any): Promise<Payment> {
    return this.paymentsService.findOne(id, req.user);
  }

  @Post(':id/process')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '处理支付' })
  @ApiResponse({
    status: 200,
    description: '支付处理成功',
    type: Payment,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 404,
    description: '支付不存在',
  })
  @ApiParam({ name: 'id', description: '支付ID' })
  async processPayment(
    @Param('id') id: string,
    @Body() processPaymentDto: ProcessPaymentDto,
    @Request() req: any,
  ): Promise<Payment> {
    return this.paymentsService.processPayment(id, processPaymentDto, req.user);
  }

  @Post(':id/confirm')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '确认支付完成' })
  @ApiResponse({
    status: 200,
    description: '支付确认成功',
    type: Payment,
  })
  @ApiResponse({
    status: 404,
    description: '支付不存在',
  })
  @ApiParam({ name: 'id', description: '支付ID' })
  async confirmPayment(@Param('id') id: string): Promise<Payment> {
    return this.paymentsService.confirmPayment(id);
  }

  @Post(':id/refund')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '退款' })
  @ApiResponse({
    status: 200,
    description: '退款成功',
    type: Payment,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '支付不存在',
  })
  @ApiParam({ name: 'id', description: '支付ID' })
  async refundPayment(
    @Param('id') id: string,
    @Body() refundDto: RefundPaymentDto,
    @Request() req: any,
  ): Promise<Payment> {
    return this.paymentsService.refundPayment(id, refundDto, req.user);
  }

  @Post(':id/release-escrow')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '释放托管资金' })
  @ApiResponse({
    status: 200,
    description: '托管资金释放成功',
    type: Payment,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '支付不存在',
  })
  @ApiParam({ name: 'id', description: '支付ID' })
  async releaseEscrow(
    @Param('id') id: string,
    @Body() releaseDto: EscrowReleaseDto,
    @Request() req: any,
  ): Promise<Payment> {
    return this.paymentsService.releaseEscrow(id, releaseDto, req.user);
  }

  @Post(':id/cancel')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '取消支付' })
  @ApiResponse({
    status: 200,
    description: '支付取消成功',
    type: Payment,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 404,
    description: '支付不存在',
  })
  @ApiParam({ name: 'id', description: '支付ID' })
  async cancelPayment(
    @Param('id') id: string,
    @Body() body: { reason: string },
    @Request() req: any,
  ): Promise<Payment> {
    return this.paymentsService.cancelPayment(id, body.reason, req.user);
  }

  @Patch(':id/status')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserType.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新支付状态（管理员）' })
  @ApiResponse({
    status: 200,
    description: '状态更新成功',
    type: Payment,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  @ApiResponse({
    status: 403,
    description: '无权限',
  })
  @ApiResponse({
    status: 404,
    description: '支付不存在',
  })
  @ApiParam({ name: 'id', description: '支付ID' })
  async updateStatus(
    @Param('id') id: string,
    @Body() updateStatusDto: UpdatePaymentStatusDto,
    @Request() req: any,
  ): Promise<Payment> {
    return this.paymentsService.updateStatus(id, updateStatusDto, req.user);
  }
}
