{"name": "stats-gl", "version": "2.4.2", "type": "module", "author": "Renaud ROHLINGER (https://github.com/RenaudRohlinger)", "homepage": "https://github.com/Renaud<PERSON><PERSON><PERSON>/stats-gl", "repository": "https://github.com/Renaud<PERSON><PERSON><PERSON>/stats-gl", "license": "MIT", "files": ["dist/*", "lib/*"], "types": "./dist/stats-gl.d.ts", "main": "./dist/main.cjs", "module": "./dist/main.js", "exports": {".": {"types": "./dist/stats-gl.d.ts", "require": "./dist/main.cjs", "import": "./dist/main.js"}}, "sideEffects": false, "scripts": {"dev": "vite dev --debug", "serve": "vite serve", "build": "tsc && vite build && rollup -c && node ./scripts/copyBuild.js", "preview": "vite preview"}, "dependencies": {"@types/three": "*", "three": "^0.170.0"}, "devDependencies": {"fs-extra": "^11.2.0", "path": "^0.12.7", "rollup": "^4.24.3", "rollup-plugin-dts": "^5.3.1", "typescript": "^5.6.3", "vite": "^4.5.5"}, "peerDependencies": {"three": "*", "@types/three": "*"}}