"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../core/Billboard.cjs.js"),r=require("../core/ScreenSpace.cjs.js"),s=require("../core/ScreenSizer.cjs.js"),o=require("../core/QuadraticBezierLine.cjs.js"),t=require("../core/CubicBezierLine.cjs.js"),i=require("../core/CatmullRomLine.cjs.js"),a=require("../core/Line.cjs.js"),c=require("../core/PositionalAudio.cjs.js"),u=require("../core/Text.cjs.js"),n=require("../core/Text3D.cjs.js"),p=require("../core/Effects.cjs.js"),j=require("../core/GradientTexture.cjs.js"),x=require("../core/Image.cjs.js"),l=require("../core/Edges.cjs.js"),d=require("../core/Outlines.cjs.js"),q=require("../core/Trail.cjs.js"),m=require("../core/Sampler.cjs.js"),h=require("../core/ComputedAttribute.cjs.js"),C=require("../core/Clone.cjs.js"),M=require("../core/MarchingCubes.cjs.js"),S=require("../core/Decal.cjs.js"),T=require("../core/Svg.cjs.js"),b=require("../core/Gltf.cjs.js"),P=require("../core/AsciiRenderer.cjs.js"),f=require("../core/Splat.cjs.js"),g=require("../core/OrthographicCamera.cjs.js"),B=require("../core/PerspectiveCamera.cjs.js"),v=require("../core/CubeCamera.cjs.js"),A=require("../core/DeviceOrientationControls.cjs.js"),F=require("../core/FlyControls.cjs.js"),G=require("../core/MapControls.cjs.js"),L=require("../core/OrbitControls.cjs.js"),D=require("../core/TrackballControls.cjs.js"),E=require("../core/ArcballControls.cjs.js"),R=require("../core/TransformControls.cjs.js"),k=require("../core/PointerLockControls.cjs.js"),I=require("../core/FirstPersonControls.cjs.js"),w=require("../core/CameraControls.cjs.js"),z=require("camera-controls"),y=require("../core/MotionPathControls.cjs.js"),O=require("../core/GizmoHelper.cjs.js"),H=require("../core/GizmoViewcube.cjs.js"),V=require("../core/GizmoViewport.cjs.js"),W=require("../core/Grid.cjs.js"),K=require("../core/CubeTexture.cjs.js"),Q=require("../core/Fbx.cjs.js"),N=require("../core/Ktx2.cjs.js"),U=require("../core/Progress.cjs.js"),X=require("../core/Texture.cjs.js"),_=require("../core/VideoTexture.cjs.js"),J=require("../core/useFont.cjs.js"),Y=require("../core/useSpriteLoader.cjs.js"),Z=require("../core/Helper.cjs.js"),$=require("../core/Stats.cjs.js"),ee=require("../core/StatsGl.cjs.js"),re=require("../core/useDepthBuffer.cjs.js"),se=require("../core/useAspect.cjs.js"),oe=require("../core/useCamera.cjs.js"),te=require("../core/DetectGPU.cjs.js"),ie=require("../core/Bvh.cjs.js"),ae=require("../core/useContextBridge.cjs.js"),ce=require("../core/useAnimations.cjs.js"),ue=require("../core/Fbo.cjs.js"),ne=require("../core/useIntersect.cjs.js"),pe=require("../core/useBoxProjectedEnv.cjs.js"),je=require("../core/BBAnchor.cjs.js"),xe=require("../core/TrailTexture.cjs.js"),le=require("../core/Example.cjs.js"),de=require("../core/SpriteAnimator.cjs.js"),qe=require("../core/CurveModifier.cjs.js"),me=require("../core/MeshDistortMaterial.cjs.js"),he=require("../core/MeshWobbleMaterial.cjs.js"),Ce=require("../core/MeshReflectorMaterial.cjs.js"),Me=require("../core/MeshRefractionMaterial.cjs.js"),Se=require("../core/MeshTransmissionMaterial.cjs.js"),Te=require("../core/MeshDiscardMaterial.cjs.js"),be=require("../core/MultiMaterial.cjs.js"),Pe=require("../core/PointMaterial.cjs.js"),fe=require("../core/shaderMaterial.cjs.js"),ge=require("../core/softShadows.cjs.js"),Be=require("../core/shapes.cjs.js"),ve=require("../core/RoundedBox.cjs.js"),Ae=require("../core/ScreenQuad.cjs.js"),Fe=require("../core/Center.cjs.js"),Ge=require("../core/Resize.cjs.js"),Le=require("../core/Bounds.cjs.js"),De=require("../core/CameraShake.cjs.js"),Ee=require("../core/Float.cjs.js"),Re=require("../core/Stage.cjs.js"),ke=require("../core/Backdrop.cjs.js"),Ie=require("../core/Shadow.cjs.js"),we=require("../core/Caustics.cjs.js"),ze=require("../core/ContactShadows.cjs.js"),ye=require("../core/AccumulativeShadows.cjs.js"),Oe=require("../core/SpotLight.cjs.js"),He=require("../core/Environment.cjs.js"),Ve=require("../core/Lightformer.cjs.js"),We=require("../core/Sky.cjs.js"),Ke=require("../core/Stars.cjs.js"),Qe=require("../core/Cloud.cjs.js"),Ne=require("../core/Sparkles.cjs.js"),Ue=require("../core/useEnvironment.cjs.js"),Xe=require("../core/MatcapTexture.cjs.js"),_e=require("../core/NormalTexture.cjs.js"),Je=require("../core/Wireframe.cjs.js"),Ye=require("../core/ShadowAlpha.cjs.js"),Ze=require("../core/Points.cjs.js"),$e=require("../core/Instances.cjs.js"),er=require("../core/Segments.cjs.js"),rr=require("../core/Detailed.cjs.js"),sr=require("../core/Preload.cjs.js"),or=require("../core/BakeShadows.cjs.js"),tr=require("../core/meshBounds.cjs.js"),ir=require("../core/AdaptiveDpr.cjs.js"),ar=require("../core/AdaptiveEvents.cjs.js"),cr=require("../core/PerformanceMonitor.cjs.js"),ur=require("../core/RenderTexture.cjs.js"),nr=require("../core/RenderCubeTexture.cjs.js"),pr=require("../core/Mask.cjs.js"),jr=require("../core/Hud.cjs.js"),xr=require("../core/Fisheye.cjs.js"),lr=require("../core/MeshPortalMaterial.cjs.js"),dr=require("../core/calculateScaleFactor.cjs.js");function qr(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}require("@babel/runtime/helpers/extends"),require("react"),require("three"),require("@react-three/fiber"),require("three-stdlib"),require("troika-three-text"),require("suspend-react"),require("../helpers/constants.cjs.js"),require("meshline"),require("maath"),require("zustand"),require("hls.js"),require("stats.js"),require("../helpers/useEffectfulState.cjs.js"),require("stats-gl"),require("detect-gpu"),require("three-mesh-bvh"),require("../helpers/deprecated.cjs.js"),require("../materials/BlurPass.cjs.js"),require("../materials/ConvolutionMaterial.cjs.js"),require("../materials/MeshReflectorMaterial.cjs.js"),require("../materials/MeshRefractionMaterial.cjs.js"),require("../materials/DiscardMaterial.cjs.js"),require("@monogrid/gainmap-js"),require("../helpers/environment-assets.cjs.js"),require("../materials/SpotLightMaterial.cjs.js"),require("../materials/WireframeMaterial.cjs.js");var mr=qr(z);exports.Billboard=e.Billboard,exports.ScreenSpace=r.ScreenSpace,exports.ScreenSizer=s.ScreenSizer,exports.QuadraticBezierLine=o.QuadraticBezierLine,exports.CubicBezierLine=t.CubicBezierLine,exports.CatmullRomLine=i.CatmullRomLine,exports.Line=a.Line,exports.PositionalAudio=c.PositionalAudio,exports.Text=u.Text,exports.Text3D=n.Text3D,exports.Effects=p.Effects,exports.isWebGL2Available=p.isWebGL2Available,exports.GradientTexture=j.GradientTexture,exports.GradientType=j.GradientType,exports.Image=x.Image,exports.Edges=l.Edges,exports.Outlines=d.Outlines,exports.Trail=q.Trail,exports.useTrail=q.useTrail,exports.Sampler=m.Sampler,exports.useSurfaceSampler=m.useSurfaceSampler,exports.ComputedAttribute=h.ComputedAttribute,exports.Clone=C.Clone,exports.MarchingCube=M.MarchingCube,exports.MarchingCubes=M.MarchingCubes,exports.MarchingPlane=M.MarchingPlane,exports.Decal=S.Decal,exports.Svg=T.Svg,exports.Gltf=b.Gltf,exports.useGLTF=b.useGLTF,exports.AsciiRenderer=P.AsciiRenderer,exports.Splat=f.Splat,exports.OrthographicCamera=g.OrthographicCamera,exports.PerspectiveCamera=B.PerspectiveCamera,exports.CubeCamera=v.CubeCamera,exports.useCubeCamera=v.useCubeCamera,exports.DeviceOrientationControls=A.DeviceOrientationControls,exports.FlyControls=F.FlyControls,exports.MapControls=G.MapControls,exports.OrbitControls=L.OrbitControls,exports.TrackballControls=D.TrackballControls,exports.ArcballControls=E.ArcballControls,exports.TransformControls=R.TransformControls,exports.PointerLockControls=k.PointerLockControls,exports.FirstPersonControls=I.FirstPersonControls,exports.CameraControls=w.CameraControls,Object.defineProperty(exports,"CameraControlsImpl",{enumerable:!0,get:function(){return mr.default}}),exports.MotionPathControls=y.MotionPathControls,exports.useMotion=y.useMotion,exports.GizmoHelper=O.GizmoHelper,exports.useGizmoContext=O.useGizmoContext,exports.GizmoViewcube=H.GizmoViewcube,exports.GizmoViewport=V.GizmoViewport,exports.Grid=W.Grid,exports.CubeTexture=K.CubeTexture,exports.useCubeTexture=K.useCubeTexture,exports.Fbx=Q.Fbx,exports.useFBX=Q.useFBX,exports.Ktx2=N.Ktx2,exports.useKTX2=N.useKTX2,exports.Progress=U.Progress,exports.useProgress=U.useProgress,exports.IsObject=X.IsObject,exports.Texture=X.Texture,exports.useTexture=X.useTexture,exports.VideoTexture=_.VideoTexture,exports.useVideoTexture=_.useVideoTexture,exports.useFont=J.useFont,exports.checkIfFrameIsEmpty=Y.checkIfFrameIsEmpty,exports.getFirstFrame=Y.getFirstFrame,exports.useSpriteLoader=Y.useSpriteLoader,exports.Helper=Z.Helper,exports.useHelper=Z.useHelper,exports.Stats=$.Stats,exports.StatsGl=ee.StatsGl,exports.useDepthBuffer=re.useDepthBuffer,exports.useAspect=se.useAspect,exports.useCamera=oe.useCamera,exports.DetectGPU=te.DetectGPU,exports.useDetectGPU=te.useDetectGPU,exports.Bvh=ie.Bvh,exports.useBVH=ie.useBVH,exports.useContextBridge=ae.useContextBridge,exports.useAnimations=ce.useAnimations,exports.Fbo=ue.Fbo,exports.useFBO=ue.useFBO,exports.useIntersect=ne.useIntersect,exports.useBoxProjectedEnv=pe.useBoxProjectedEnv,exports.BBAnchor=je.BBAnchor,exports.TrailTexture=xe.TrailTexture,exports.useTrailTexture=xe.useTrailTexture,exports.Example=le.Example,exports.SpriteAnimator=de.SpriteAnimator,exports.useSpriteAnimator=de.useSpriteAnimator,exports.CurveModifier=qe.CurveModifier,exports.MeshDistortMaterial=me.MeshDistortMaterial,exports.MeshWobbleMaterial=he.MeshWobbleMaterial,exports.MeshReflectorMaterial=Ce.MeshReflectorMaterial,exports.MeshRefractionMaterial=Me.MeshRefractionMaterial,exports.MeshTransmissionMaterial=Se.MeshTransmissionMaterial,exports.MeshDiscardMaterial=Te.MeshDiscardMaterial,exports.MultiMaterial=be.MultiMaterial,exports.PointMaterial=Pe.PointMaterial,exports.PointMaterialImpl=Pe.PointMaterialImpl,exports.shaderMaterial=fe.shaderMaterial,exports.SoftShadows=ge.SoftShadows,exports.Box=Be.Box,exports.Capsule=Be.Capsule,exports.Circle=Be.Circle,exports.Cone=Be.Cone,exports.Cylinder=Be.Cylinder,exports.Dodecahedron=Be.Dodecahedron,exports.Extrude=Be.Extrude,exports.Icosahedron=Be.Icosahedron,exports.Lathe=Be.Lathe,exports.Octahedron=Be.Octahedron,exports.Plane=Be.Plane,exports.Polyhedron=Be.Polyhedron,exports.Ring=Be.Ring,exports.Shape=Be.Shape,exports.Sphere=Be.Sphere,exports.Tetrahedron=Be.Tetrahedron,exports.Torus=Be.Torus,exports.TorusKnot=Be.TorusKnot,exports.Tube=Be.Tube,exports.RoundedBox=ve.RoundedBox,exports.RoundedBoxGeometry=ve.RoundedBoxGeometry,exports.ScreenQuad=Ae.ScreenQuad,exports.Center=Fe.Center,exports.Resize=Ge.Resize,exports.Bounds=Le.Bounds,exports.useBounds=Le.useBounds,exports.CameraShake=De.CameraShake,exports.Float=Ee.Float,exports.Stage=Re.Stage,exports.Backdrop=ke.Backdrop,exports.Shadow=Ie.Shadow,exports.Caustics=we.Caustics,exports.ContactShadows=ze.ContactShadows,exports.AccumulativeShadows=ye.AccumulativeShadows,exports.RandomizedLight=ye.RandomizedLight,exports.accumulativeContext=ye.accumulativeContext,exports.SpotLight=Oe.SpotLight,exports.SpotLightShadow=Oe.SpotLightShadow,exports.Environment=He.Environment,exports.EnvironmentCube=He.EnvironmentCube,exports.EnvironmentMap=He.EnvironmentMap,exports.EnvironmentPortal=He.EnvironmentPortal,exports.Lightformer=Ve.Lightformer,exports.Sky=We.Sky,exports.calcPosFromAngles=We.calcPosFromAngles,exports.Stars=Ke.Stars,exports.Cloud=Qe.Cloud,exports.CloudInstance=Qe.CloudInstance,exports.Clouds=Qe.Clouds,exports.Sparkles=Ne.Sparkles,exports.useEnvironment=Ue.useEnvironment,exports.MatcapTexture=Xe.MatcapTexture,exports.useMatcapTexture=Xe.useMatcapTexture,exports.NormalTexture=_e.NormalTexture,exports.useNormalTexture=_e.useNormalTexture,exports.Wireframe=Je.Wireframe,exports.ShadowAlpha=Ye.ShadowAlpha,exports.Point=Ze.Point,exports.Points=Ze.Points,exports.PointsBuffer=Ze.PointsBuffer,exports.PositionPoint=Ze.PositionPoint,exports.Instance=$e.Instance,exports.InstancedAttribute=$e.InstancedAttribute,exports.Instances=$e.Instances,exports.Merged=$e.Merged,exports.PositionMesh=$e.PositionMesh,exports.createInstances=$e.createInstances,exports.Segment=er.Segment,exports.SegmentObject=er.SegmentObject,exports.Segments=er.Segments,exports.Detailed=rr.Detailed,exports.Preload=sr.Preload,exports.BakeShadows=or.BakeShadows,exports.meshBounds=tr.meshBounds,exports.AdaptiveDpr=ir.AdaptiveDpr,exports.AdaptiveEvents=ar.AdaptiveEvents,exports.PerformanceMonitor=cr.PerformanceMonitor,exports.usePerformanceMonitor=cr.usePerformanceMonitor,exports.RenderTexture=ur.RenderTexture,exports.RenderCubeTexture=nr.RenderCubeTexture,exports.Mask=pr.Mask,exports.useMask=pr.useMask,exports.Hud=jr.Hud,exports.Fisheye=xr.Fisheye,exports.MeshPortalMaterial=lr.MeshPortalMaterial,exports.calculateScaleFactor=dr.calculateScaleFactor;
