import { ApiProperty } from '@nestjs/swagger';
import { 
  IsString, 
  IsOptional, 
  IsEnum, 
  IsNumber, 
  IsObject, 
  IsArray, 
  IsBoolean,
  Min,
  ValidateNested,
  IsMongoId
} from 'class-validator';
import { Type } from 'class-transformer';
import { Types } from 'mongoose';

class RoomsDto {
  @ApiProperty({ description: '卧室数量', minimum: 0, default: 0 })
  @IsNumber()
  @Min(0)
  bedrooms: number = 0;

  @ApiProperty({ description: '浴室数量', minimum: 0, default: 0 })
  @IsNumber()
  @Min(0)
  bathrooms: number = 0;

  @ApiProperty({ description: '客厅数量', minimum: 0, default: 0 })
  @IsNumber()
  @Min(0)
  livingRooms: number = 0;

  @ApiProperty({ description: '厨房数量', minimum: 0, default: 0 })
  @IsNumber()
  @Min(0)
  kitchens: number = 0;

  @ApiProperty({ description: '其他房间数量', minimum: 0, default: 0 })
  @IsNumber()
  @Min(0)
  others: number = 0;
}

class BudgetDto {
  @ApiProperty({ description: '最低预算', minimum: 0 })
  @IsNumber()
  @Min(0)
  min: number;

  @ApiProperty({ description: '最高预算', minimum: 0 })
  @IsNumber()
  @Min(0)
  max: number;

  @ApiProperty({ description: '货币单位', default: 'CAD' })
  @IsString()
  @IsOptional()
  currency: string = 'CAD';
}

class CameraDto {
  @ApiProperty({ description: '相机位置', type: [Number], default: [0, 5, 10] })
  @IsArray()
  @IsNumber({}, { each: true })
  position: number[] = [0, 5, 10];

  @ApiProperty({ description: '相机目标', type: [Number], default: [0, 0, 0] })
  @IsArray()
  @IsNumber({}, { each: true })
  target: number[] = [0, 0, 0];

  @ApiProperty({ description: '视野角度', default: 75 })
  @IsNumber()
  fov: number = 75;
}

class DirectionalLightDto {
  @ApiProperty({ description: '光照强度', default: 1 })
  @IsNumber()
  intensity: number = 1;

  @ApiProperty({ description: '光源位置', type: [Number], default: [10, 10, 5] })
  @IsArray()
  @IsNumber({}, { each: true })
  position: number[] = [10, 10, 5];
}

class LightingDto {
  @ApiProperty({ description: '环境光强度', default: 0.4 })
  @IsNumber()
  ambient: number = 0.4;

  @ApiProperty({ description: '方向光设置' })
  @ValidateNested()
  @Type(() => DirectionalLightDto)
  directional: DirectionalLightDto = new DirectionalLightDto();
}

class SceneDataDto {
  @ApiProperty({ description: '平面图数据(JSON字符串)', required: false })
  @IsString()
  @IsOptional()
  floorPlan?: string;

  @ApiProperty({ description: '3D场景数据(JSON字符串)', required: false })
  @IsString()
  @IsOptional()
  scene3D?: string;

  @ApiProperty({ description: '相机设置' })
  @ValidateNested()
  @Type(() => CameraDto)
  @IsOptional()
  camera?: CameraDto = new CameraDto();

  @ApiProperty({ description: '光照设置' })
  @ValidateNested()
  @Type(() => LightingDto)
  @IsOptional()
  lighting?: LightingDto = new LightingDto();
}

export class CreateDesignProjectDto {
  @ApiProperty({ description: '项目名称' })
  @IsString()
  name: string;

  @ApiProperty({ description: '项目描述', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: '关联的装修项目ID', required: false })
  @IsMongoId()
  @IsOptional()
  projectId?: Types.ObjectId;

  @ApiProperty({ 
    description: '房屋类型',
    enum: ['apartment', 'house', 'condo', 'townhouse', 'other'],
    default: 'apartment'
  })
  @IsEnum(['apartment', 'house', 'condo', 'townhouse', 'other'])
  houseType: string = 'apartment';

  @ApiProperty({ description: '房屋面积(平方英尺)', minimum: 0 })
  @IsNumber()
  @Min(0)
  area: number;

  @ApiProperty({ description: '房间数量' })
  @ValidateNested()
  @Type(() => RoomsDto)
  @IsOptional()
  rooms?: RoomsDto = new RoomsDto();

  @ApiProperty({ 
    description: '设计风格',
    enum: ['modern', 'traditional', 'contemporary', 'industrial', 'scandinavian', 'minimalist', 'rustic', 'other'],
    default: 'modern'
  })
  @IsEnum(['modern', 'traditional', 'contemporary', 'industrial', 'scandinavian', 'minimalist', 'rustic', 'other'])
  @IsOptional()
  style?: string = 'modern';

  @ApiProperty({ description: '预算范围' })
  @ValidateNested()
  @Type(() => BudgetDto)
  budget: BudgetDto;

  @ApiProperty({ description: '3D场景数据', required: false })
  @ValidateNested()
  @Type(() => SceneDataDto)
  @IsOptional()
  sceneData?: SceneDataDto = new SceneDataDto();

  @ApiProperty({ description: '使用的资产ID列表', type: [String], required: false })
  @IsArray()
  @IsMongoId({ each: true })
  @IsOptional()
  assets?: Types.ObjectId[] = [];

  @ApiProperty({ description: '是否公开', default: false })
  @IsBoolean()
  @IsOptional()
  isPublic?: boolean = false;
}
