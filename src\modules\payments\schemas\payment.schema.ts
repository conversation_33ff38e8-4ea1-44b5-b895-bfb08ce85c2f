import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type PaymentDocument = Payment & Document;

export enum PaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
  PARTIALLY_REFUNDED = 'partially_refunded',
}

export enum PaymentType {
  PROJECT_DEPOSIT = 'project_deposit',
  MILESTONE_PAYMENT = 'milestone_payment',
  FINAL_PAYMENT = 'final_payment',
  REFUND = 'refund',
  ESCROW_RELEASE = 'escrow_release',
}

export enum PaymentMethod {
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  BANK_TRANSFER = 'bank_transfer',
  PAYPAL = 'paypal',
  STRIPE = 'stripe',
  INTERAC = 'interac',
}

export enum Currency {
  CAD = 'CAD',
  USD = 'USD',
}

@Schema({ _id: false })
export class PaymentDetails {
  @Prop()
  @ApiProperty({ description: '支付网关交易ID' })
  gatewayTransactionId?: string;

  @Prop()
  @ApiProperty({ description: '支付网关' })
  gateway?: string;

  @Prop()
  @ApiProperty({ description: '卡片后四位' })
  cardLast4?: string;

  @Prop()
  @ApiProperty({ description: '卡片品牌' })
  cardBrand?: string;

  @Prop()
  @ApiProperty({ description: '银行名称' })
  bankName?: string;

  @Prop()
  @ApiProperty({ description: '支付确认号' })
  confirmationNumber?: string;

  @Prop()
  @ApiProperty({ description: '授权码' })
  authorizationCode?: string;

  @Prop()
  @ApiProperty({ description: '处理费用' })
  processingFee?: number;

  @Prop()
  @ApiProperty({ description: '汇率' })
  exchangeRate?: number;

  @Prop()
  @ApiProperty({ description: '原始金额' })
  originalAmount?: number;

  @Prop()
  @ApiProperty({ description: '原始货币' })
  originalCurrency?: string;
}

@Schema({ _id: false })
export class RefundInfo {
  @Prop({ required: true })
  @ApiProperty({ description: '退款金额' })
  amount: number;

  @Prop({ required: true })
  @ApiProperty({ description: '退款原因' })
  reason: string;

  @Prop({ type: Types.ObjectId, required: true, ref: 'User' })
  @ApiProperty({ description: '退款发起者ID' })
  initiatedBy: Types.ObjectId;

  @Prop({ default: Date.now })
  @ApiProperty({ description: '退款时间' })
  refundedAt: Date;

  @Prop()
  @ApiProperty({ description: '退款交易ID' })
  refundTransactionId?: string;

  @Prop()
  @ApiProperty({ description: '退款状态' })
  status?: string;
}

@Schema({ timestamps: true })
export class Payment {
  @Prop({ type: Types.ObjectId, required: true, ref: 'User' })
  @ApiProperty({ description: '付款人ID' })
  payerId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, required: true, ref: 'User' })
  @ApiProperty({ description: '收款人ID' })
  payeeId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Project' })
  @ApiProperty({ description: '关联项目ID' })
  projectId?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Quote' })
  @ApiProperty({ description: '关联报价ID' })
  quoteId?: Types.ObjectId;

  @Prop({ enum: PaymentType, required: true })
  @ApiProperty({ description: '支付类型', enum: PaymentType })
  type: PaymentType;

  @Prop({ required: true })
  @ApiProperty({ description: '支付金额' })
  amount: number;

  @Prop({ enum: Currency, default: Currency.CAD })
  @ApiProperty({ description: '货币', enum: Currency })
  currency: Currency;

  @Prop({ enum: PaymentMethod, required: true })
  @ApiProperty({ description: '支付方式', enum: PaymentMethod })
  method: PaymentMethod;

  @Prop({ enum: PaymentStatus, default: PaymentStatus.PENDING })
  @ApiProperty({ description: '支付状态', enum: PaymentStatus })
  status: PaymentStatus;

  @Prop({ required: true })
  @ApiProperty({ description: '支付描述' })
  description: string;

  @Prop({ type: PaymentDetails })
  @ApiProperty({ description: '支付详情', type: PaymentDetails })
  details?: PaymentDetails;

  @Prop()
  @ApiProperty({ description: '支付意图ID（Stripe）' })
  paymentIntentId?: string;

  @Prop()
  @ApiProperty({ description: '客户端密钥' })
  clientSecret?: string;

  @Prop()
  @ApiProperty({ description: '支付完成时间' })
  completedAt?: Date;

  @Prop()
  @ApiProperty({ description: '支付失败时间' })
  failedAt?: Date;

  @Prop()
  @ApiProperty({ description: '失败原因' })
  failureReason?: string;

  @Prop()
  @ApiProperty({ description: '取消时间' })
  cancelledAt?: Date;

  @Prop()
  @ApiProperty({ description: '取消原因' })
  cancellationReason?: string;

  @Prop([RefundInfo])
  @ApiProperty({ description: '退款信息', type: [RefundInfo] })
  refunds?: RefundInfo[];

  @Prop({ default: false })
  @ApiProperty({ description: '是否托管' })
  isEscrow: boolean;

  @Prop()
  @ApiProperty({ description: '托管释放时间' })
  escrowReleasedAt?: Date;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  @ApiProperty({ description: '托管释放者ID' })
  escrowReleasedBy?: Types.ObjectId;

  @Prop()
  @ApiProperty({ description: '到期时间' })
  expiresAt?: Date;

  @Prop()
  @ApiProperty({ description: '备注' })
  notes?: string;

  @Prop({ type: Object, default: {} })
  @ApiProperty({ description: '元数据' })
  metadata?: Record<string, any>;

  @Prop({ default: false })
  @ApiProperty({ description: '是否已删除' })
  isDeleted: boolean;

  @Prop()
  @ApiProperty({ description: '删除时间' })
  deletedAt?: Date;
}

export const PaymentSchema = SchemaFactory.createForClass(Payment);

// 创建索引
PaymentSchema.index({ payerId: 1, createdAt: -1 });
PaymentSchema.index({ payeeId: 1, createdAt: -1 });
PaymentSchema.index({ projectId: 1, type: 1 });
PaymentSchema.index({ quoteId: 1 });
PaymentSchema.index({ status: 1, createdAt: -1 });
PaymentSchema.index({ type: 1, status: 1 });
PaymentSchema.index({ paymentIntentId: 1 });
PaymentSchema.index({ isEscrow: 1, status: 1 });
PaymentSchema.index({ expiresAt: 1 });
PaymentSchema.index({ isDeleted: 1, createdAt: -1 });

// 虚拟字段
PaymentSchema.virtual('totalRefunded').get(function() {
  if (!this.refunds || this.refunds.length === 0) {
    return 0;
  }
  return this.refunds.reduce((total, refund) => total + refund.amount, 0);
});

PaymentSchema.virtual('remainingAmount').get(function() {
  const totalRefunded = this.refunds && this.refunds.length > 0
    ? this.refunds.reduce((total, refund) => total + refund.amount, 0)
    : 0;
  return this.amount - totalRefunded;
});

PaymentSchema.virtual('isExpired').get(function() {
  return this.expiresAt && new Date() > this.expiresAt;
});

PaymentSchema.virtual('isPending').get(function() {
  return this.status === PaymentStatus.PENDING;
});

PaymentSchema.virtual('isCompleted').get(function() {
  return this.status === PaymentStatus.COMPLETED;
});

PaymentSchema.virtual('isFailed').get(function() {
  return this.status === PaymentStatus.FAILED;
});

PaymentSchema.virtual('isRefunded').get(function() {
  return this.status === PaymentStatus.REFUNDED || this.status === PaymentStatus.PARTIALLY_REFUNDED;
});

PaymentSchema.virtual('canRefund').get(function() {
  const totalRefunded = this.refunds && this.refunds.length > 0
    ? this.refunds.reduce((total, refund) => total + refund.amount, 0)
    : 0;
  const remainingAmount = this.amount - totalRefunded;
  return this.status === PaymentStatus.COMPLETED && remainingAmount > 0;
});

// 设置虚拟字段在JSON序列化时包含
PaymentSchema.set('toJSON', { virtuals: true });
PaymentSchema.set('toObject', { virtuals: true });
