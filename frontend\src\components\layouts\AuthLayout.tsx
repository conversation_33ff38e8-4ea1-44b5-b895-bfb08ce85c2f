import React from 'react';
import { Outlet } from 'react-router-dom';
import { Layout, Row, Col, Typography, Space, Switch } from 'antd';
import { GlobalOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Content } = Layout;
const { Title, Text } = Typography;

const AuthLayout: React.FC = () => {
  const { i18n } = useTranslation();

  // 语言切换
  const toggleLanguage = () => {
    const newLang = i18n.language === 'en' ? 'zh' : 'en';
    i18n.changeLanguage(newLang);
    localStorage.setItem('language', newLang);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Content>
        <Row style={{ minHeight: '100vh' }}>
          {/* 左侧品牌展示区域 */}
          <Col xs={0} md={12} lg={14} xl={16}>
            <div
              style={{
                height: '100vh',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                padding: '40px',
                color: 'white',
                position: 'relative',
              }}
            >
              {/* 语言切换 - 右上角 */}
              <div style={{ position: 'absolute', top: 24, right: 24 }}>
                <Space>
                  <GlobalOutlined style={{ color: 'white' }} />
                  <Switch
                    checkedChildren="中"
                    unCheckedChildren="EN"
                    checked={i18n.language === 'zh'}
                    onChange={toggleLanguage}
                  />
                </Space>
              </div>

              <div style={{ textAlign: 'center', maxWidth: 500 }}>
                <Title level={1} style={{ color: 'white', fontSize: '3rem', marginBottom: 24 }}>
                  HomeReno
                </Title>
                <Text style={{ 
                  color: 'rgba(255,255,255,0.9)', 
                  fontSize: '1.2rem',
                  lineHeight: 1.6,
                  display: 'block',
                  marginBottom: 32
                }}>
                  {i18n.language === 'zh' 
                    ? '连接房主与专业装修师傅，让您的家装梦想成真'
                    : 'Connecting homeowners with professional contractors to make your renovation dreams come true'
                  }
                </Text>

                {/* 特色功能展示 */}
                <div style={{ textAlign: 'left' }}>
                  <div style={{ marginBottom: 16 }}>
                    <Text style={{ color: 'white', fontSize: '1rem' }}>
                      ✨ {i18n.language === 'zh' ? '专业认证的装修师傅' : 'Verified Professional Contractors'}
                    </Text>
                  </div>
                  <div style={{ marginBottom: 16 }}>
                    <Text style={{ color: 'white', fontSize: '1rem' }}>
                      🏠 {i18n.language === 'zh' ? '3D设计工具' : '3D Design Tools'}
                    </Text>
                  </div>
                  <div style={{ marginBottom: 16 }}>
                    <Text style={{ color: 'white', fontSize: '1rem' }}>
                      💰 {i18n.language === 'zh' ? '透明报价系统' : 'Transparent Pricing'}
                    </Text>
                  </div>
                  <div style={{ marginBottom: 16 }}>
                    <Text style={{ color: 'white', fontSize: '1rem' }}>
                      🔒 {i18n.language === 'zh' ? '安全支付保障' : 'Secure Payment Protection'}
                    </Text>
                  </div>
                </div>
              </div>
            </div>
          </Col>

          {/* 右侧表单区域 */}
          <Col xs={24} md={12} lg={10} xl={8}>
            <div
              style={{
                height: '100vh',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                padding: '40px',
                background: '#fafafa',
              }}
            >
              <div style={{ width: '100%', maxWidth: 400 }}>
                {/* 移动端语言切换 */}
                <div 
                  style={{ 
                    display: 'flex', 
                    justifyContent: 'flex-end', 
                    marginBottom: 24,
                    '@media (min-width: 768px)': {
                      display: 'none'
                    }
                  }}
                  className="mobile-lang-switch"
                >
                  <Space>
                    <GlobalOutlined />
                    <Switch
                      checkedChildren="中"
                      unCheckedChildren="EN"
                      checked={i18n.language === 'zh'}
                      onChange={toggleLanguage}
                    />
                  </Space>
                </div>

                <Outlet />
              </div>
            </div>
          </Col>
        </Row>
      </Content>

      <style>
        {`
          @media (max-width: 767px) {
            .mobile-lang-switch {
              display: flex !important;
            }
          }
        `}
      </style>
    </Layout>
  );
};

export default AuthLayout;
