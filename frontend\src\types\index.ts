// 用户相关类型
export interface User {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: 'homeowner' | 'contractor' | 'admin';
  avatar?: string;
  address?: Address;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Address {
  street: string;
  city: string;
  province: string;
  postalCode: string;
  country: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

// 认证相关类型
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: 'homeowner' | 'contractor';
}

export interface AuthResponse {
  user: User;
  access_token: string;
  refresh_token: string;
}

// 项目相关类型
export interface Project {
  _id: string;
  title: string;
  description: string;
  category: ProjectCategory;
  budget: {
    min: number;
    max: number;
    currency: string;
  };
  timeline: {
    startDate: string;
    endDate: string;
    isFlexible: boolean;
  };
  location: Address;
  status: ProjectStatus;
  homeowner: string | User;
  assignedContractor?: string | ServiceProvider;
  images: string[];
  requirements: string[];
  createdAt: string;
  updatedAt: string;
}

export enum ProjectCategory {
  KITCHEN_RENOVATION = 'kitchen_renovation',
  BATHROOM_RENOVATION = 'bathroom_renovation',
  BASEMENT_FINISHING = 'basement_finishing',
  FLOORING = 'flooring',
  PAINTING = 'painting',
  ELECTRICAL = 'electrical',
  PLUMBING = 'plumbing',
  ROOFING = 'roofing',
  LANDSCAPING = 'landscaping',
  OTHER = 'other'
}

export enum ProjectStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// 服务商相关类型
export interface ServiceProvider {
  _id: string;
  user: string | User;
  companyName: string;
  businessNumber?: string;
  services: ProjectCategory[];
  experience: number;
  description: string;
  portfolio: PortfolioItem[];
  certifications: string[];
  insurance: {
    hasInsurance: boolean;
    provider?: string;
    policyNumber?: string;
    expiryDate?: string;
  };
  serviceArea: {
    cities: string[];
    radius: number;
  };
  rating: {
    average: number;
    count: number;
  };
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PortfolioItem {
  _id: string;
  title: string;
  description: string;
  images: string[];
  category: ProjectCategory;
  completedAt: string;
}

// 报价相关类型
export interface Quote {
  _id: string;
  project: string | Project;
  contractor: string | ServiceProvider;
  amount: number;
  currency: string;
  breakdown: QuoteBreakdown[];
  timeline: {
    startDate: string;
    endDate: string;
  };
  description: string;
  terms: string;
  status: QuoteStatus;
  validUntil: string;
  createdAt: string;
  updatedAt: string;
}

export interface QuoteBreakdown {
  item: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

export enum QuoteStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  EXPIRED = 'expired'
}

// 通知相关类型
export interface Notification {
  _id: string;
  recipient: string;
  title: string;
  message: string;
  type: NotificationType;
  isRead: boolean;
  data?: any;
  createdAt: string;
}

export enum NotificationType {
  PROJECT_UPDATE = 'project_update',
  NEW_QUOTE = 'new_quote',
  QUOTE_ACCEPTED = 'quote_accepted',
  PAYMENT_RECEIVED = 'payment_received',
  SYSTEM = 'system'
}

// 文件相关类型
export interface FileUpload {
  _id: string;
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  url: string;
  uploadedBy: string;
  createdAt: string;
}

// 表单相关类型
export interface FormErrors {
  [key: string]: string | undefined;
}

// 分页相关类型
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
