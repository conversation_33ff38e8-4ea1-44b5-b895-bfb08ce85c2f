import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type DesignAssetDocument = DesignAsset & Document;

@Schema({ timestamps: true })
export class DesignAsset {
  @ApiProperty({ description: '资产ID' })
  _id: Types.ObjectId;

  @ApiProperty({ description: '资产名称' })
  @Prop({ required: true, trim: true })
  name: string;

  @ApiProperty({ description: '资产描述' })
  @Prop({ trim: true })
  description?: string;

  @ApiProperty({ description: '资产类型' })
  @Prop({ 
    required: true,
    enum: ['furniture', 'material', 'texture', 'model', 'lighting', 'decoration']
  })
  type: string;

  @ApiProperty({ description: '资产分类' })
  @Prop({ 
    required: true,
    enum: [
      // 家具类别
      'sofa', 'chair', 'table', 'bed', 'cabinet', 'shelf', 'desk', 'wardrobe',
      // 材质类别
      'wood', 'metal', 'fabric', 'leather', 'glass', 'stone', 'ceramic', 'plastic',
      // 装饰类别
      'painting', 'plant', 'lamp', 'mirror', 'curtain', 'rug', 'pillow',
      // 其他
      'appliance', 'fixture', 'other'
    ]
  })
  category: string;

  @ApiProperty({ description: '适用房间' })
  @Prop([{ 
    type: String,
    enum: ['bedroom', 'bathroom', 'living_room', 'kitchen', 'dining_room', 'office', 'any']
  }])
  suitableRooms: string[];

  @ApiProperty({ description: '设计风格' })
  @Prop([{ 
    type: String,
    enum: ['modern', 'traditional', 'contemporary', 'industrial', 'scandinavian', 'minimalist', 'rustic', 'any']
  }])
  styles: string[];

  @ApiProperty({ description: '文件信息' })
  @Prop({
    type: {
      modelUrl: { type: String }, // 3D模型文件URL
      textureUrls: [String], // 贴图文件URLs
      thumbnailUrl: { type: String, required: true }, // 缩略图URL
      previewUrls: [String], // 预览图URLs
      fileSize: { type: Number, min: 0 }, // 文件大小(字节)
      format: { type: String }, // 文件格式 (gltf, fbx, obj, etc.)
      dimensions: {
        type: {
          width: Number,
          height: Number,
          depth: Number,
          unit: { type: String, default: 'cm' }
        }
      }
    },
    required: true
  })
  fileInfo: {
    modelUrl?: string;
    textureUrls: string[];
    thumbnailUrl: string;
    previewUrls: string[];
    fileSize?: number;
    format?: string;
    dimensions?: {
      width: number;
      height: number;
      depth: number;
      unit: string;
    };
  };

  @ApiProperty({ description: '材质属性' })
  @Prop({
    type: {
      color: String, // 主色调
      roughness: { type: Number, min: 0, max: 1 }, // 粗糙度
      metalness: { type: Number, min: 0, max: 1 }, // 金属度
      opacity: { type: Number, min: 0, max: 1, default: 1 }, // 透明度
      emissive: String, // 发光颜色
      normalMap: String, // 法线贴图URL
      bumpMap: String, // 凹凸贴图URL
      specularMap: String // 高光贴图URL
    }
  })
  materialProperties?: {
    color?: string;
    roughness?: number;
    metalness?: number;
    opacity?: number;
    emissive?: string;
    normalMap?: string;
    bumpMap?: string;
    specularMap?: string;
  };

  @ApiProperty({ description: '价格信息' })
  @Prop({
    type: {
      amount: { type: Number, min: 0 },
      currency: { type: String, default: 'CAD' },
      unit: { type: String, default: 'piece' } // piece, sqft, sqm, linear_ft
    }
  })
  pricing?: {
    amount: number;
    currency: string;
    unit: string;
  };

  @ApiProperty({ description: '供应商信息' })
  @Prop({
    type: {
      name: String,
      website: String,
      contact: String
    }
  })
  supplier?: {
    name: string;
    website?: string;
    contact?: string;
  };

  @ApiProperty({ description: '标签' })
  @Prop([String])
  tags: string[];

  @ApiProperty({ description: '使用次数' })
  @Prop({ default: 0, min: 0 })
  usageCount: number;

  @ApiProperty({ description: '评分' })
  @Prop({ 
    type: {
      average: { type: Number, min: 0, max: 5, default: 0 },
      count: { type: Number, min: 0, default: 0 }
    },
    default: {}
  })
  rating: {
    average: number;
    count: number;
  };

  @ApiProperty({ description: '是否为官方资产' })
  @Prop({ default: false })
  isOfficial: boolean;

  @ApiProperty({ description: '是否免费' })
  @Prop({ default: true })
  isFree: boolean;

  @ApiProperty({ description: '许可证类型' })
  @Prop({ 
    enum: ['free', 'commercial', 'attribution', 'custom'],
    default: 'free'
  })
  license: string;

  @ApiProperty({ description: '上传者ID' })
  @Prop({ type: Types.ObjectId, ref: 'User' })
  uploadedBy?: Types.ObjectId;

  @ApiProperty({ description: '资产状态' })
  @Prop({ 
    enum: ['pending', 'approved', 'rejected', 'archived'],
    default: 'pending'
  })
  status: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export const DesignAssetSchema = SchemaFactory.createForClass(DesignAsset);

// 创建索引
DesignAssetSchema.index({ type: 1, category: 1 });
DesignAssetSchema.index({ status: 1, isOfficial: -1, 'rating.average': -1 });
DesignAssetSchema.index({ suitableRooms: 1, styles: 1 });
DesignAssetSchema.index({ tags: 1 });
DesignAssetSchema.index({ usageCount: -1 });
DesignAssetSchema.index({ isFree: 1, 'pricing.amount': 1 });
