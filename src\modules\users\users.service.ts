import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import * as bcrypt from 'bcryptjs';
import { ConfigService } from '@nestjs/config';
import { User, UserDocument, UserStatus, UserType } from './schemas/user.schema';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { PaginatedResult } from '../../common/interfaces/paginated-result.interface';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    private configService: ConfigService,
  ) {}

  /**
   * 创建新用户
   */
  async create(createUserDto: CreateUserDto): Promise<User> {
    // 检查邮箱是否已存在
    const existingUser = await this.userModel.findOne({
      email: createUserDto.email.toLowerCase(),
    });

    if (existingUser) {
      throw new ConflictException('Email already exists');
    }

    // 哈希密码
    const saltRounds = this.configService.get<number>('auth.bcrypt.saltRounds');
    const passwordHash = await bcrypt.hash(createUserDto.password, saltRounds);

    // 创建用户
    const user = new this.userModel({
      ...createUserDto,
      email: createUserDto.email.toLowerCase(),
      passwordHash,
    });

    return user.save();
  }

  /**
   * 根据ID查找用户
   */
  async findById(id: string): Promise<User> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid user ID');
    }

    const user = await this.userModel.findById(id);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  /**
   * 根据邮箱查找用户
   */
  async findByEmail(email: string): Promise<User | null> {
    return this.userModel.findOne({ email: email.toLowerCase() });
  }

  /**
   * 根据邮箱查找用户（包含密码）
   */
  async findByEmailWithPassword(email: string): Promise<UserDocument | null> {
    return this.userModel
      .findOne({ email: email.toLowerCase() })
      .select('+passwordHash');
  }

  /**
   * 获取用户列表（分页）
   */
  async findAll(
    paginationDto: PaginationDto,
    filters?: {
      userType?: UserType;
      status?: UserStatus;
      search?: string;
    },
  ): Promise<PaginatedResult<User>> {
    const { page = 1, limit = 20 } = paginationDto;
    const skip = (page - 1) * limit;

    // 构建查询条件
    const query: any = {};

    if (filters?.userType) {
      query.userType = filters.userType;
    }

    if (filters?.status) {
      query.status = filters.status;
    }

    if (filters?.search) {
      query.$or = [
        { email: { $regex: filters.search, $options: 'i' } },
        { 'profile.firstName': { $regex: filters.search, $options: 'i' } },
        { 'profile.lastName': { $regex: filters.search, $options: 'i' } },
      ];
    }

    // 执行查询
    const [users, total] = await Promise.all([
      this.userModel.find(query).skip(skip).limit(limit).sort({ createdAt: -1 }),
      this.userModel.countDocuments(query),
    ]);

    return {
      items: users,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * 更新用户信息
   */
  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid user ID');
    }

    // 如果更新邮箱，检查是否已存在
    if (updateUserDto.email) {
      const existingUser = await this.userModel.findOne({
        email: updateUserDto.email.toLowerCase(),
        _id: { $ne: id },
      });

      if (existingUser) {
        throw new ConflictException('Email already exists');
      }

      updateUserDto.email = updateUserDto.email.toLowerCase();
    }

    const user = await this.userModel.findByIdAndUpdate(
      id,
      { $set: updateUserDto },
      { new: true, runValidators: true },
    );

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  /**
   * 修改密码
   */
  async changePassword(
    id: string,
    changePasswordDto: ChangePasswordDto,
  ): Promise<void> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid user ID');
    }

    const user = await this.userModel.findById(id).select('+passwordHash');
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // 验证当前密码
    const isCurrentPasswordValid = await bcrypt.compare(
      changePasswordDto.currentPassword,
      user.passwordHash,
    );

    if (!isCurrentPasswordValid) {
      throw new UnauthorizedException('Current password is incorrect');
    }

    // 哈希新密码
    const saltRounds = this.configService.get<number>('auth.bcrypt.saltRounds');
    const newPasswordHash = await bcrypt.hash(
      changePasswordDto.newPassword,
      saltRounds,
    );

    // 更新密码
    await this.userModel.findByIdAndUpdate(id, {
      passwordHash: newPasswordHash,
    });
  }

  /**
   * 验证密码
   */
  async validatePassword(user: UserDocument, password: string): Promise<boolean> {
    return bcrypt.compare(password, user.passwordHash);
  }

  /**
   * 更新最后登录时间
   */
  async updateLastLogin(id: string): Promise<void> {
    await this.userModel.findByIdAndUpdate(id, {
      'auth.lastLogin': new Date(),
      'auth.loginAttempts': 0,
      $unset: { 'auth.lockUntil': 1 },
    });
  }

  /**
   * 增加登录尝试次数
   */
  async incrementLoginAttempts(id: string): Promise<void> {
    const maxAttempts = this.configService.get<number>('auth.session.maxLoginAttempts');
    const lockoutDuration = this.configService.get<number>('auth.session.lockoutDuration');

    const user = await this.userModel.findById(id);
    if (!user) return;

    const updates: any = { $inc: { 'auth.loginAttempts': 1 } };

    // 如果达到最大尝试次数，锁定账户
    if (user.auth.loginAttempts + 1 >= maxAttempts) {
      updates['auth.lockUntil'] = new Date(Date.now() + lockoutDuration);
    }

    await this.userModel.findByIdAndUpdate(id, updates);
  }

  /**
   * 验证邮箱
   */
  async verifyEmail(id: string): Promise<void> {
    await this.userModel.findByIdAndUpdate(id, {
      'auth.emailVerified': true,
      status: UserStatus.ACTIVE,
      $unset: { 'auth.emailVerificationToken': 1 },
    });
  }

  /**
   * 验证手机号
   */
  async verifyPhone(id: string): Promise<void> {
    await this.userModel.findByIdAndUpdate(id, {
      'auth.phoneVerified': true,
    });
  }

  /**
   * 软删除用户
   */
  async remove(id: string): Promise<void> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid user ID');
    }

    const result = await this.userModel.findByIdAndUpdate(id, {
      status: UserStatus.INACTIVE,
    });

    if (!result) {
      throw new NotFoundException('User not found');
    }
  }

  /**
   * 根据地理位置查找用户
   */
  async findByLocation(
    lat: number,
    lng: number,
    radius: number = 50, // km
    userType?: UserType,
  ): Promise<User[]> {
    const query: any = {
      'profile.address.coordinates': {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [lng, lat],
          },
          $maxDistance: radius * 1000, // 转换为米
        },
      },
    };

    if (userType) {
      query.userType = userType;
    }

    return this.userModel.find(query);
  }

  /**
   * 获取用户统计信息
   */
  async getStats(): Promise<{
    total: number;
    byType: Record<UserType, number>;
    byStatus: Record<UserStatus, number>;
    recentRegistrations: number;
  }> {
    const [total, byType, byStatus, recentRegistrations] = await Promise.all([
      this.userModel.countDocuments(),
      this.userModel.aggregate([
        { $group: { _id: '$userType', count: { $sum: 1 } } },
      ]),
      this.userModel.aggregate([
        { $group: { _id: '$status', count: { $sum: 1 } } },
      ]),
      this.userModel.countDocuments({
        createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) },
      }),
    ]);

    return {
      total,
      byType: byType.reduce((acc, item) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      byStatus: byStatus.reduce((acc, item) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      recentRegistrations,
    };
  }
}
