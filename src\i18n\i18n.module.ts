import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { I18nModule as NestI18nModule, AcceptLanguageResolver, QueryResolver, HeaderResolver } from 'nestjs-i18n';
import { join } from 'path';
import { I18nService } from './i18n.service';
import { I18nController } from './i18n.controller';
import { LanguageMiddleware } from './middleware/language.middleware';
import { I18nResponseInterceptor } from './interceptors/i18n-response.interceptor';

@Module({
  imports: [
    NestI18nModule.forRoot({
      fallbackLanguage: 'en',
      loaderOptions: {
        path: join(__dirname, '../i18n/'),
        watch: true,
      },
      resolvers: [
        { use: QueryResolver, options: ['lang'] },
        AcceptLanguageResolver,
        new HeaderResolver(['x-lang']),
      ],
      typesOutputPath: join(__dirname, '../generated/i18n.generated.ts'),
    }),
  ],
  controllers: [I18nController],
  providers: [I18nService, I18nResponseInterceptor],
  exports: [NestI18nModule, I18nService, I18nResponseInterceptor],
})
export class I18nModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(LanguageMiddleware).forRoutes('*');
  }
}
