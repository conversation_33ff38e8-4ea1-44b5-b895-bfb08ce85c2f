{"name": "string-format", "version": "2.0.0", "description": "String formatting inspired by Python's str.format()", "author": "<PERSON> <<EMAIL>>", "keywords": ["string", "formatting", "language", "util"], "homepage": "https://github.com/davidchambers/string-format", "bugs": "https://github.com/davidchambers/string-format/issues", "license": "WTFPL OR MIT", "repository": {"type": "git", "url": "git://github.com/davidchambers/string-format.git"}, "files": ["LICENSE", "README.md", "index.js", "package.json"], "dependencies": {}, "devDependencies": {"sanctuary-scripts": "1.6.x"}, "scripts": {"doctest": "sanctuary-doctest", "lint": "sanctuary-lint", "release": "sanctuary-release", "test": "npm run lint && sanctuary-test && npm run doctest"}}