{"version": 3, "file": "i18n.abstract.loader.js", "sourceRoot": "", "sources": ["../../src/loaders/i18n.abstract.loader.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,+CAA2C;AAC3C,sDAAwD;AACxD,2CAAyD;AACzD,6BAA6B;AAC7B,0CAAuC;AACvC,oCAA4D;AAE5D,+BAMc;AACd,qCAAqC;AACrC,8CAA0C;AAcnC,IAAe,kBAAkB,GAAjC,MAAe,kBACpB,SAAQ,wBAAU;IAOlB,YAEU,OAAkC;QAE1C,KAAK,EAAE,CAAC;QAFA,YAAO,GAAP,OAAO,CAA2B;QAJpC,WAAM,GAAoB,IAAI,cAAO,EAAE,CAAC;QAO9C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAE7C,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACtB,IAAI,CAAC,OAAO,GAAG,QAAQ;iBACpB,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC;iBACjD,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACnB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC;SACN;IACH,CAAC;IAED,KAAK,CAAC,eAAe;QACnB,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;SAC5B;IACH,CAAC;IAED,KAAK,CAAC,SAAS;QACb,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACtB,OAAO,IAAA,YAAe,EACpB,IAAA,SAAY,EAAC,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC,EACzC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAA,gBAAS,EAAC,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,CACzD,CAAC;SACH;QACD,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACtB,OAAO,IAAA,YAAe,EACpB,IAAA,SAAY,EAAC,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC,EAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAA,gBAAS,EAAC,GAAG,EAAE,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAC5D,CAAC;SACH;QACD,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAClC,CAAC;IAES,KAAK,CAAC,iBAAiB;QAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QAE9D,MAAM,YAAY,GAAoB,EAAE,CAAC;QAEzC,IAAI,CAAC,CAAC,MAAM,IAAA,cAAM,EAAC,QAAQ,CAAC,CAAC,EAAE;YAC7B,MAAM,IAAI,sBAAS,CAAC,cAAc,QAAQ,mBAAmB,CAAC,CAAC;SAChE;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;YACjD,MAAM,IAAI,sBAAS,CACjB,mEAAmE,CACpE,CAAC;SACH;QAED,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAE9C,MAAM,OAAO,GAAG,IAAI,MAAM,CACxB,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CACjD,CAAC;QAEF,MAAM,KAAK,GAAG,MAAM;YAClB,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YAC/C,QAAQ;SACT,CAAC,MAAM,CAAC,KAAK,EAAE,CAAoB,EAAE,CAAS,EAAE,EAAE;YACjD,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CACZ,GAAG,CAAC,MAAM,IAAA,gBAAQ,EAAC,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAChE,CAAC;YACF,OAAO,CAAC,CAAC;QACX,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QAExB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;YACxB,IAAI,MAAM,GAAG,KAAK,CAAC;YAEnB,MAAM,SAAS,GAAG,IAAI;iBACnB,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;iBACtC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACnB,MAAM,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAEzB,IAAI,GAAG,KAAK,GAAG,EAAE;gBACf,MAAM,GAAG,IAAI,CAAC;aACf;YAGD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,IAAA,mBAAQ,EAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;YAE3D,MAAM,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAE1E,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACxC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;oBACjD,YAAY,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAElE,IAAI,MAAM,EAAE;wBACV,YAAY,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;qBAC/C;yBAAM;wBACL,IAAI,CAAC,yBAAyB,CAC5B,YAAY,CAAC,IAAI,CAAC,EAClB,MAAM,EACN,QAAQ,EACR,IAAI,CAAC,QAAQ,CAAC,CACf,CAAC;qBACH;gBACH,CAAC,CAAC,CAAC;aACJ;SACF;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAES,yBAAyB,CACjC,YAAsC,EACtC,MAAgB,EAChB,QAAgB,EAChB,KAAa;QAEb,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC/C,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACzB,CAAC,CAAC,EAAE,CAAC;YACP,IAAI,CAAC,yBAAyB,CAC5B,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EACvB,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EACf,QAAQ,EACR,KAAK,CACN,CAAC;SACH;aAAM;YACL,YAAY,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;SAChC;IACH,CAAC;IAES,KAAK,CAAC,cAAc;QAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9D,OAAO,CAAC,MAAM,IAAA,sBAAc,EAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAClD,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,CAC7B,CAAC;IACJ,CAAC;IAES,eAAe,CAAC,OAAkC;QAC1D,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,iBAAiB,EAAE,EAAE,GAAG,OAAO,EAAE,CAAC;QAEtD,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QACvD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YACzC,OAAO,CAAC,WAAW,GAAG,IAAI,GAAG,OAAO,CAAC,WAAW,CAAC;SAClD;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;CAIF,CAAA;AAhKqB,kBAAkB;IASnC,WAAA,IAAA,eAAM,EAAC,oCAAmB,CAAC,CAAA;;GATV,kBAAkB,CAgKvC;AAhKqB,gDAAkB"}