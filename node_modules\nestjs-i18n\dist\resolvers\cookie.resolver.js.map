{"version": 3, "file": "cookie.resolver.js", "sourceRoot": "", "sources": ["../../src/resolvers/cookie.resolver.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,iCAAiC;AACjC,2CAA8D;AAE9D,8CAAoD;AAM7C,IAAM,cAAc,GAApB,MAAM,cAAc;IACzB,YAEmB,cAAwB,CAAC,MAAM,CAAC;QAAhC,gBAAW,GAAX,WAAW,CAAqB;IAChD,CAAC;IAEJ,OAAO,CAAC,OAAyB;QAC/B,IAAI,GAAQ,CAAC;QAEb,QAAQ,OAAO,CAAC,OAAO,EAAY,EAAE;YACnC,KAAK,MAAM;gBACT,GAAG,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;gBAC1C,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;gBAC9B,MAAM;YACR,KAAK,SAAS;gBACZ,CAAC,EAAE,AAAD,EAAG,EAAE,GAAG,EAAE,CAAC,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;gBAClC,MAAM;SACT;QAED,IAAI,GAAG,EAAE;YACP,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE;gBACtC,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAChD;YAED,IAAI,GAAG,CAAC,OAAO,EAAE;gBACf,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE;oBAChC,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;wBACpC,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;qBACzC;iBACF;aACF;SACF;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF,CAAA;AAnCY,cAAc;IAD1B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,gCAAmB,GAAE,CAAA;;GAFb,cAAc,CAmC1B;AAnCY,wCAAc"}