import { designTemplatesSeed } from './design-templates.seed';
import { designAssetsSeed } from './design-assets.seed';

function validateSeedData() {
  console.log('🔍 验证种子数据结构...');
  
  try {
    // 验证设计模板数据
    console.log('📐 验证设计模板数据...');
    console.log(`模板数量: ${designTemplatesSeed.length}`);
    
    designTemplatesSeed.forEach((template, index) => {
      console.log(`\n模板 ${index + 1}: ${template.name}`);
      console.log(`  - 类别: ${template.category}`);
      console.log(`  - 风格: ${template.style}`);
      console.log(`  - 房屋类型: ${template.houseTypes.join(', ')}`);
      console.log(`  - 房间类型: ${template.roomTypes.join(', ')}`);
      console.log(`  - 材料数量: ${template.templateData.materials.length}`);
      console.log(`  - 家具数量: ${template.templateData.furniture.length}`);
      console.log(`  - 灯光数量: ${template.templateData.lighting.lights.length}`);
      
      // 验证材料结构
      template.templateData.materials.forEach((material, mIndex) => {
        if (!material.id || !material.name || !material.type) {
          console.error(`  ❌ 材料 ${mIndex} 缺少必要字段:`, material);
        }
      });
      
      // 验证家具结构
      template.templateData.furniture.forEach((furniture, fIndex) => {
        if (!furniture.id || !furniture.name || !furniture.category) {
          console.error(`  ❌ 家具 ${fIndex} 缺少必要字段:`, furniture);
        }
      });
    });
    
    // 验证设计资产数据
    console.log('\n🎨 验证设计资产数据...');
    console.log(`资产数量: ${designAssetsSeed.length}`);
    
    designAssetsSeed.forEach((asset, index) => {
      console.log(`\n资产 ${index + 1}: ${asset.name}`);
      console.log(`  - 类型: ${asset.type}`);
      console.log(`  - 类别: ${asset.category}`);
      console.log(`  - 风格: ${asset.styles.join(', ')}`);
      console.log(`  - 文件格式: ${asset.fileInfo.format}`);
      
      if (!asset.name || !asset.type || !asset.category) {
        console.error(`  ❌ 资产 ${index} 缺少必要字段:`, asset);
      }
    });
    
    console.log('\n✅ 种子数据结构验证完成！');
    console.log('📊 数据统计:');
    console.log(`  - 设计模板: ${designTemplatesSeed.length} 个`);
    console.log(`  - 设计资产: ${designAssetsSeed.length} 个`);
    
    // 检查是否有重复的ID
    const templateIds = designTemplatesSeed.map(t => t.name);
    const duplicateTemplates = templateIds.filter((id, index) => templateIds.indexOf(id) !== index);
    if (duplicateTemplates.length > 0) {
      console.warn('⚠️  发现重复的模板名称:', duplicateTemplates);
    }
    
    const assetIds = designAssetsSeed.map(a => a.name);
    const duplicateAssets = assetIds.filter((id, index) => assetIds.indexOf(id) !== index);
    if (duplicateAssets.length > 0) {
      console.warn('⚠️  发现重复的资产名称:', duplicateAssets);
    }
    
    console.log('\n🎉 种子数据验证成功！数据结构正确，可以安全地插入数据库。');
    
  } catch (error) {
    console.error('❌ 种子数据验证失败:', error.message);
    process.exit(1);
  }
}

validateSeedData();
