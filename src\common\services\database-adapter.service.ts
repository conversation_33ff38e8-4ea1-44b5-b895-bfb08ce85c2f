import { Injectable, Optional } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { MockDatabaseService } from './mock-database.service';

/**
 * 数据库适配器服务 - 统一真实数据库和模拟数据库的接口
 */
@Injectable()
export class DatabaseAdapterService {
  private useMockDatabase: boolean;

  constructor(
    @Optional() private mockDatabaseService?: MockDatabaseService,
  ) {
    this.useMockDatabase = process.env.USE_MOCK_DATABASE === 'true';
  }

  // 通用查询方法
  async findById(collectionName: string, model: Model<any> | null, id: string): Promise<any> {
    if (this.useMockDatabase && this.mockDatabaseService) {
      return this.mockDatabaseService.findById(collectionName, id);
    }
    
    if (model) {
      return await model.findById(id).exec();
    }
    
    throw new Error('No database available');
  }

  async findOne(collectionName: string, model: Model<any> | null, query: any): Promise<any> {
    if (this.useMockDatabase && this.mockDatabaseService) {
      return this.mockDatabaseService.findOne(collectionName, query);
    }
    
    if (model) {
      return await model.findOne(query).exec();
    }
    
    throw new Error('No database available');
  }

  async find(collectionName: string, model: Model<any> | null, query: any = {}, options: any = {}): Promise<any[]> {
    if (this.useMockDatabase && this.mockDatabaseService) {
      return this.mockDatabaseService.find(collectionName, query, options);
    }
    
    if (model) {
      let mongoQuery = model.find(query);
      
      if (options.sort) {
        mongoQuery = mongoQuery.sort(options.sort);
      }
      if (options.skip) {
        mongoQuery = mongoQuery.skip(options.skip);
      }
      if (options.limit) {
        mongoQuery = mongoQuery.limit(options.limit);
      }
      
      return await mongoQuery.exec();
    }
    
    throw new Error('No database available');
  }

  async create(collectionName: string, model: Model<any> | null, data: any): Promise<any> {
    if (this.useMockDatabase && this.mockDatabaseService) {
      return this.mockDatabaseService.insert(collectionName, data);
    }
    
    if (model) {
      const document = new model(data);
      return await document.save();
    }
    
    throw new Error('No database available');
  }

  async updateById(collectionName: string, model: Model<any> | null, id: string, update: any): Promise<any> {
    if (this.useMockDatabase && this.mockDatabaseService) {
      return this.mockDatabaseService.updateById(collectionName, id, update);
    }
    
    if (model) {
      return await model.findByIdAndUpdate(id, update, { new: true }).exec();
    }
    
    throw new Error('No database available');
  }

  async deleteById(collectionName: string, model: Model<any> | null, id: string): Promise<boolean> {
    if (this.useMockDatabase && this.mockDatabaseService) {
      return this.mockDatabaseService.deleteById(collectionName, id);
    }
    
    if (model) {
      const result = await model.findByIdAndDelete(id).exec();
      return !!result;
    }
    
    throw new Error('No database available');
  }

  async count(collectionName: string, model: Model<any> | null, query: any = {}): Promise<number> {
    if (this.useMockDatabase && this.mockDatabaseService) {
      return this.mockDatabaseService.count(collectionName, query);
    }
    
    if (model) {
      return await model.countDocuments(query).exec();
    }
    
    throw new Error('No database available');
  }

  // 获取数据库状态
  getDatabaseInfo(): any {
    if (this.useMockDatabase && this.mockDatabaseService) {
      return {
        type: 'mock',
        status: 'connected',
        stats: this.mockDatabaseService.getStats()
      };
    }
    
    return {
      type: 'mongodb',
      status: 'connected'
    };
  }

  // 检查是否使用模拟数据库
  isUsingMockDatabase(): boolean {
    return this.useMockDatabase;
  }
}
