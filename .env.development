# 开发环境配置
NODE_ENV=development
PORT=3000

# 开发模式 - 设置为true使用内存数据库，false使用真实数据库
USE_MOCK_DATABASE=false

# 数据库配置 - 开发环境 (使用Docker)
MONGODB_URI=mongodb://root:<EMAIL>:3717/homereno?replicaSet=mgset-90995274
MONGODB_DATABASE=homereno

# Redis配置 - 开发环境
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis123

# JWT配置 - 开发环境 (较短的过期时间便于测试)
JWT_SECRET=dev-jwt-secret-key-not-for-production
JWT_EXPIRES_IN=1d
JWT_REFRESH_SECRET=dev-refresh-secret-key-not-for-production
JWT_REFRESH_EXPIRES_IN=7d

# 日志配置 - 开发环境
LOG_LEVEL=debug
LOG_FILE=logs/dev.log

# 开发模式特定配置
DEBUG=true
SWAGGER_ENABLED=true
CORS_ENABLED=true

# 邮件配置 - 开发环境 (可以使用测试邮箱)
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USER=your-mailtrap-user
MAIL_PASS=your-mailtrap-pass
MAIL_FROM=<EMAIL>

# 文件存储 - 开发环境 (本地存储)
UPLOAD_DEST=uploads
MAX_FILE_SIZE=5242880

# 限流配置 - 开发环境 (更宽松的限制)
THROTTLE_TTL=60
THROTTLE_LIMIT=1000
