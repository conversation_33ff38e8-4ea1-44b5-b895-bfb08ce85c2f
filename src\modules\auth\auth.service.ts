import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { UsersService } from '../users/users.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { User, UserDocument } from '../users/schemas/user.schema';
import { JwtPayload } from './interfaces/jwt-payload.interface';
import { AuthResponse } from './interfaces/auth-response.interface';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  /**
   * 用户注册
   */
  async register(registerDto: RegisterDto): Promise<AuthResponse> {
    // 检查邮箱是否已存在
    const existingUser = await this.usersService.findByEmail(registerDto.email);
    if (existingUser) {
      throw new ConflictException('Email already exists');
    }

    // 创建用户
    const user = await this.usersService.create({
      email: registerDto.email,
      password: registerDto.password,
      userType: registerDto.userType,
      profile: registerDto.profile,
      preferences: registerDto.preferences,
    });

    // 生成JWT令牌
    const tokens = await this.generateTokens(user);

    return {
      ...tokens,
      user: {
        id: user._id.toString(),
        email: user.email,
        firstName: user.profile?.firstName || '',
        lastName: user.profile?.lastName || '',
        role: user.userType,
      },
    };
  }

  /**
   * 用户登录
   */
  async login(loginDto: LoginDto): Promise<AuthResponse> {
    const { email, password } = loginDto;

    // 查找用户（包含密码）
    const user = await this.usersService.findByEmailWithPassword(email);
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // 检查账户是否被锁定
    if (user.isLocked) {
      throw new UnauthorizedException('Account is temporarily locked');
    }

    // 验证密码
    const isPasswordValid = await this.usersService.validatePassword(user, password);
    if (!isPasswordValid) {
      // 增加登录尝试次数
      await this.usersService.incrementLoginAttempts(user._id.toString());
      throw new UnauthorizedException('Invalid credentials');
    }

    // 更新最后登录时间
    await this.usersService.updateLastLogin(user._id.toString());

    // 生成JWT令牌
    const tokens = await this.generateTokens(user);

    return {
      ...tokens,
      user: {
        id: user._id.toString(),
        email: user.email,
        firstName: user.profile?.firstName || '',
        lastName: user.profile?.lastName || '',
        role: user.userType,
      },
    };
  }

  /**
   * 刷新令牌
   */
  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    try {
      const payload = this.jwtService.verify(refreshToken, {
        secret: this.configService.get<string>('auth.jwt.refreshSecret'),
      });

      const user = await this.usersService.findById(payload.sub);
      if (!user) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      const tokens = await this.generateTokens(user);

      return {
        ...tokens,
        user: {
          id: user._id.toString(),
          email: user.email,
          firstName: user.profile?.firstName || '',
          lastName: user.profile?.lastName || '',
          role: user.userType,
        },
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  /**
   * 验证JWT令牌
   */
  async validateUser(payload: JwtPayload): Promise<User> {
    const user = await this.usersService.findById(payload.sub);
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    return user;
  }

  /**
   * 生成JWT令牌
   */
  private async generateTokens(user: UserDocument | User): Promise<{
    accessToken: string;
    refreshToken: string;
    tokenType: string;
    expiresIn: number;
  }> {
    const payload: JwtPayload = {
      sub: user._id.toString(),
      email: user.email,
      userType: user.userType,
    };

    const expiresIn = this.configService.get<string>('auth.jwt.expiresIn') || '7d';
    const expiresInSeconds = this.parseExpiresIn(expiresIn);

    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload, {
        secret: this.configService.get<string>('auth.jwt.secret'),
        expiresIn: expiresIn,
      }),
      this.jwtService.signAsync(payload, {
        secret: this.configService.get<string>('auth.jwt.refreshSecret'),
        expiresIn: this.configService.get<string>('auth.jwt.refreshExpiresIn'),
      }),
    ]);

    return {
      accessToken,
      refreshToken,
      tokenType: 'Bearer',
      expiresIn: expiresInSeconds,
    };
  }

  /**
   * 解析过期时间字符串为秒数
   */
  private parseExpiresIn(expiresIn: string): number {
    const match = expiresIn.match(/^(\d+)([smhd])$/);
    if (!match) return 3600; // 默认1小时

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 3600;
      case 'd': return value * 86400;
      default: return 3600;
    }
  }

  /**
   * 发送密码重置邮件
   */
  async forgotPassword(email: string): Promise<void> {
    const user = await this.usersService.findByEmail(email);
    if (!user) {
      // 为了安全，即使用户不存在也返回成功
      return;
    }

    // 生成重置令牌
    const resetToken = this.generateResetToken();
    const resetExpires = new Date(Date.now() + 3600000); // 1小时后过期

    // 保存重置令牌
    await this.usersService.update(user._id.toString(), {
      'auth.resetPasswordToken': resetToken,
      'auth.resetPasswordExpires': resetExpires,
    } as any);

    // TODO: 发送重置邮件
    // await this.emailService.sendPasswordResetEmail(user.email, resetToken);
  }

  /**
   * 重置密码
   */
  async resetPassword(token: string, newPassword: string): Promise<void> {
    const user = await this.usersService.findByEmail(''); // TODO: 根据token查找用户
    if (!user || !user.auth.resetPasswordToken || user.auth.resetPasswordToken !== token) {
      throw new BadRequestException('Invalid or expired reset token');
    }

    if (user.auth.resetPasswordExpires < new Date()) {
      throw new BadRequestException('Reset token has expired');
    }

    // 更新密码并清除重置令牌
    await this.usersService.update(user._id.toString(), {
      password: newPassword,
      'auth.resetPasswordToken': undefined,
      'auth.resetPasswordExpires': undefined,
    } as any);
  }

  /**
   * 生成重置令牌
   */
  private generateResetToken(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  /**
   * 验证邮箱
   */
  async verifyEmail(token: string): Promise<void> {
    // TODO: 根据token查找用户并验证邮箱
    // const user = await this.usersService.findByEmailVerificationToken(token);
    // if (!user) {
    //   throw new BadRequestException('Invalid verification token');
    // }
    // 
    // await this.usersService.verifyEmail(user._id.toString());
  }

  /**
   * 重新发送验证邮件
   */
  async resendVerificationEmail(email: string): Promise<void> {
    const user = await this.usersService.findByEmail(email);
    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (user.auth.emailVerified) {
      throw new BadRequestException('Email already verified');
    }

    // 生成新的验证令牌
    const verificationToken = this.generateResetToken();
    
    await this.usersService.update(user._id.toString(), {
      'auth.emailVerificationToken': verificationToken,
    } as any);

    // TODO: 发送验证邮件
    // await this.emailService.sendVerificationEmail(user.email, verificationToken);
  }
}
