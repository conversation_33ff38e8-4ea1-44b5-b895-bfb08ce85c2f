{"version": 3, "file": "SimplifyModifier.cjs", "sources": ["../../src/modifiers/SimplifyModifier.ts"], "sourcesContent": ["import { <PERSON><PERSON>erGeo<PERSON>, Float32BufferAttribute, Vector3 } from 'three'\nimport * as BufferGeometryUtils from '../utils/BufferGeometryUtils'\n\nconst cb = /* @__PURE__ */ new Vector3()\nconst ab = /* @__PURE__ */ new Vector3()\n\nfunction pushIfUnique<TItem>(array: TItem[], object: TItem): void {\n  if (array.indexOf(object) === -1) array.push(object)\n}\n\nfunction removeFromArray<TItem>(array: TItem[], object: TItem): void {\n  const k = array.indexOf(object)\n  if (k > -1) array.splice(k, 1)\n}\n\nclass Vertex {\n  public position: Vector3\n  private id: number\n\n  public faces: Triangle[]\n  public neighbors: Vertex[]\n\n  public collapseCost: number\n  public collapseNeighbor: null | Vertex\n\n  public minCost: number = 0\n  public totalCost: number = 0\n  public costCount: number = 0\n\n  constructor(v: Vector3, id: number) {\n    this.position = v\n    this.id = id // old index id\n\n    this.faces = [] // faces vertex is connected\n    this.neighbors = [] // neighbouring vertices aka \"adjacentVertices\"\n\n    // these will be computed in computeEdgeCostAtVertex()\n    this.collapseCost = 0 // cost of collapsing this vertex, the less the better. aka objdist\n    this.collapseNeighbor = null // best candinate for collapsing\n  }\n\n  public addUniqueNeighbor(vertex: Vertex): void {\n    pushIfUnique(this.neighbors, vertex)\n  }\n\n  public removeIfNonNeighbor(n: Vertex): void {\n    const neighbors = this.neighbors\n    const faces = this.faces\n\n    const offset = neighbors.indexOf(n)\n    if (offset === -1) return\n    for (let i = 0; i < faces.length; i++) {\n      if (faces[i].hasVertex(n)) return\n    }\n\n    neighbors.splice(offset, 1)\n  }\n}\n\n// we use a triangle class to represent structure of face slightly differently\nclass Triangle {\n  private a: number\n  private b: number\n  private c: Number\n\n  public v1: Vertex\n  public v2: Vertex\n  public v3: Vertex\n\n  public normal = new Vector3()\n\n  constructor(v1: Vertex, v2: Vertex, v3: Vertex, a: number, b: number, c: number) {\n    this.a = a\n    this.b = b\n    this.c = c\n\n    this.v1 = v1\n    this.v2 = v2\n    this.v3 = v3\n\n    this.computeNormal()\n\n    v1.faces.push(this)\n    v1.addUniqueNeighbor(v2)\n    v1.addUniqueNeighbor(v3)\n\n    v2.faces.push(this)\n    v2.addUniqueNeighbor(v1)\n    v2.addUniqueNeighbor(v3)\n\n    v3.faces.push(this)\n    v3.addUniqueNeighbor(v1)\n    v3.addUniqueNeighbor(v2)\n  }\n\n  private computeNormal(): void {\n    const vA = this.v1.position\n    const vB = this.v2.position\n    const vC = this.v3.position\n\n    cb.subVectors(vC, vB)\n    ab.subVectors(vA, vB)\n    cb.cross(ab).normalize()\n\n    this.normal.copy(cb)\n  }\n\n  public hasVertex(v: Vertex): boolean {\n    return v === this.v1 || v === this.v2 || v === this.v3\n  }\n\n  public replaceVertex(oldv: Vertex, newv: Vertex): void {\n    if (oldv === this.v1) this.v1 = newv\n    else if (oldv === this.v2) this.v2 = newv\n    else if (oldv === this.v3) this.v3 = newv\n\n    removeFromArray(oldv.faces, this)\n    newv.faces.push(this)\n\n    oldv.removeIfNonNeighbor(this.v1)\n    this.v1.removeIfNonNeighbor(oldv)\n\n    oldv.removeIfNonNeighbor(this.v2)\n    this.v2.removeIfNonNeighbor(oldv)\n\n    oldv.removeIfNonNeighbor(this.v3)\n    this.v3.removeIfNonNeighbor(oldv)\n\n    this.v1.addUniqueNeighbor(this.v2)\n    this.v1.addUniqueNeighbor(this.v3)\n\n    this.v2.addUniqueNeighbor(this.v1)\n    this.v2.addUniqueNeighbor(this.v3)\n\n    this.v3.addUniqueNeighbor(this.v1)\n    this.v3.addUniqueNeighbor(this.v2)\n\n    this.computeNormal()\n  }\n}\n\n/**\n *\tSimplification Geometry Modifier\n *    - based on code and technique\n *\t  - by Stan Melax in 1998\n *\t  - Progressive Mesh type Polygon Reduction Algorithm\n *    - http://www.melax.com/polychop/\n */\n\nclass SimplifyModifier {\n  constructor() {}\n\n  private computeEdgeCollapseCost = (u: Vertex, v: Vertex): number => {\n    // if we collapse edge uv by moving u to v then how\n    // much different will the model change, i.e. the \"error\".\n\n    const edgelength = v.position.distanceTo(u.position)\n    let curvature = 0\n\n    const sideFaces = []\n    let i,\n      il = u.faces.length,\n      face,\n      sideFace\n\n    // find the \"sides\" triangles that are on the edge uv\n    for (i = 0; i < il; i++) {\n      face = u.faces[i]\n\n      if (face.hasVertex(v)) {\n        sideFaces.push(face)\n      }\n    }\n\n    // use the triangle facing most away from the sides\n    // to determine our curvature term\n    for (i = 0; i < il; i++) {\n      let minCurvature = 1\n      face = u.faces[i]\n\n      for (let j = 0; j < sideFaces.length; j++) {\n        sideFace = sideFaces[j]\n        // use dot product of face normals.\n        const dotProd = face.normal.dot(sideFace.normal)\n        minCurvature = Math.min(minCurvature, (1.001 - dotProd) / 2)\n      }\n\n      curvature = Math.max(curvature, minCurvature)\n    }\n\n    // crude approach in attempt to preserve borders\n    // though it seems not to be totally correct\n    const borders = 0\n    if (sideFaces.length < 2) {\n      // we add some arbitrary cost for borders,\n      // borders += 10;\n      curvature = 1\n    }\n\n    const amt = edgelength * curvature + borders\n\n    return amt\n  }\n\n  private removeVertex(v: Vertex, vertices: Vertex[]): void {\n    console.assert(v.faces.length === 0)\n\n    while (v.neighbors.length) {\n      const n = v.neighbors.pop() as Vertex\n      removeFromArray(n.neighbors, v)\n    }\n\n    removeFromArray(vertices, v)\n  }\n\n  private computeEdgeCostAtVertex = (v: Vertex): void => {\n    // compute the edge collapse cost for all edges that start\n    // from vertex v.  Since we are only interested in reducing\n    // the object by selecting the min cost edge at each step, we\n    // only cache the cost of the least cost edge at this vertex\n    // (in member variable collapse) as well as the value of the\n    // cost (in member variable collapseCost).\n\n    if (v.neighbors.length === 0) {\n      // collapse if no neighbors.\n      v.collapseNeighbor = null\n      v.collapseCost = -0.01\n\n      return\n    }\n\n    v.collapseCost = 100000\n    v.collapseNeighbor = null\n\n    // search all neighboring edges for \"least cost\" edge\n    for (let i = 0; i < v.neighbors.length; i++) {\n      const collapseCost = this.computeEdgeCollapseCost(v, v.neighbors[i])\n\n      if (!v.collapseNeighbor) {\n        v.collapseNeighbor = v.neighbors[i]\n        v.collapseCost = collapseCost\n        v.minCost = collapseCost\n        v.totalCost = 0\n        v.costCount = 0\n      }\n\n      v.costCount++\n      v.totalCost += collapseCost\n\n      if (collapseCost < v.minCost) {\n        v.collapseNeighbor = v.neighbors[i]\n        v.minCost = collapseCost\n      }\n    }\n\n    // we average the cost of collapsing at this vertex\n    v.collapseCost = v.totalCost / v.costCount\n    // v.collapseCost = v.minCost;\n  }\n\n  private removeFace = (f: Triangle, faces: Triangle[]): void => {\n    removeFromArray(faces, f)\n\n    if (f.v1) removeFromArray(f.v1.faces, f)\n    if (f.v2) removeFromArray(f.v2.faces, f)\n    if (f.v3) removeFromArray(f.v3.faces, f)\n\n    // TODO optimize this!\n    const vs = [f.v1, f.v2, f.v3]\n    let v1, v2\n\n    for (let i = 0; i < 3; i++) {\n      v1 = vs[i]\n      v2 = vs[(i + 1) % 3]\n\n      if (!v1 || !v2) continue\n\n      v1.removeIfNonNeighbor(v2)\n      v2.removeIfNonNeighbor(v1)\n    }\n  }\n\n  private collapse = (vertices: Vertex[], faces: Triangle[], u: Vertex, v: Vertex): void => {\n    // u and v are pointers to vertices of an edge\n\n    // Collapse the edge uv by moving vertex u onto v\n\n    if (!v) {\n      // u is a vertex all by itself so just delete it..\n      this.removeVertex(u, vertices)\n      return\n    }\n\n    let i\n    const tmpVertices = []\n\n    for (i = 0; i < u.neighbors.length; i++) {\n      tmpVertices.push(u.neighbors[i])\n    }\n\n    // delete triangles on edge uv:\n    for (i = u.faces.length - 1; i >= 0; i--) {\n      if (u.faces[i].hasVertex(v)) {\n        this.removeFace(u.faces[i], faces)\n      }\n    }\n\n    // update remaining triangles to have v instead of u\n    for (i = u.faces.length - 1; i >= 0; i--) {\n      u.faces[i].replaceVertex(u, v)\n    }\n\n    this.removeVertex(u, vertices)\n\n    // recompute the edge collapse costs in neighborhood\n    for (i = 0; i < tmpVertices.length; i++) {\n      this.computeEdgeCostAtVertex(tmpVertices[i])\n    }\n  }\n\n  private minimumCostEdge = (vertices: Vertex[]): Vertex => {\n    // O(n * n) approach. TODO optimize this\n\n    let least = vertices[0]\n\n    for (let i = 0; i < vertices.length; i++) {\n      if (vertices[i].collapseCost < least.collapseCost) {\n        least = vertices[i]\n      }\n    }\n\n    return least\n  }\n\n  public modify = (geometry: BufferGeometry, count: number): BufferGeometry => {\n    geometry = geometry.clone()\n    const attributes = geometry.attributes\n\n    // this modifier can only process indexed and non-indexed geomtries with a position attribute\n\n    for (let name in attributes) {\n      if (name !== 'position') geometry.deleteAttribute(name)\n    }\n\n    geometry = BufferGeometryUtils.mergeVertices(geometry)\n\n    //\n    // put data of original geometry in different data structures\n    //\n\n    const vertices = []\n    const faces = []\n\n    // add vertices\n\n    const positionAttribute = geometry.getAttribute('position')\n\n    for (let i = 0; i < positionAttribute.count; i++) {\n      const v = new Vector3().fromBufferAttribute(positionAttribute, i)\n\n      const vertex = new Vertex(v, i)\n      vertices.push(vertex)\n    }\n\n    // add faces\n\n    const geomIndex = geometry.getIndex()\n\n    if (geomIndex !== null) {\n      for (let i = 0; i < geomIndex.count; i += 3) {\n        const a = geomIndex.getX(i)\n        const b = geomIndex.getX(i + 1)\n        const c = geomIndex.getX(i + 2)\n\n        const triangle = new Triangle(vertices[a], vertices[b], vertices[c], a, b, c)\n        faces.push(triangle)\n      }\n    } else {\n      for (let i = 0; i < positionAttribute.count; i += 3) {\n        const a = i\n        const b = i + 1\n        const c = i + 2\n\n        const triangle = new Triangle(vertices[a], vertices[b], vertices[c], a, b, c)\n        faces.push(triangle)\n      }\n    }\n\n    // compute all edge collapse costs\n\n    for (let i = 0, il = vertices.length; i < il; i++) {\n      this.computeEdgeCostAtVertex(vertices[i])\n    }\n\n    let nextVertex\n\n    let z = count\n\n    while (z--) {\n      nextVertex = this.minimumCostEdge(vertices)\n\n      if (!nextVertex) {\n        console.log('THREE.SimplifyModifier: No next vertex')\n        break\n      } else {\n        this.collapse(vertices, faces, nextVertex, nextVertex.collapseNeighbor as Vertex)\n      }\n    }\n\n    //\n\n    const simplifiedGeometry = new BufferGeometry()\n    const position = []\n    let index = []\n\n    //\n\n    for (let i = 0; i < vertices.length; i++) {\n      const vertex = vertices[i].position\n      position.push(vertex.x, vertex.y, vertex.z)\n    }\n\n    //\n\n    for (let i = 0; i < faces.length; i++) {\n      const face = faces[i]\n\n      const a = vertices.indexOf(face.v1)\n      const b = vertices.indexOf(face.v2)\n      const c = vertices.indexOf(face.v3)\n\n      index.push(a, b, c)\n    }\n\n    //\n\n    simplifiedGeometry.setAttribute('position', new Float32BufferAttribute(position, 3))\n    simplifiedGeometry.setIndex(index)\n\n    return simplifiedGeometry\n  }\n}\n\nexport { SimplifyModifier }\n"], "names": ["Vector3", "BufferGeometryUtils.mergeVertices", "BufferGeometry", "Float32BufferAttribute"], "mappings": ";;;;;;;;;;AAGA,MAAM,yBAAyBA,MAAAA;AAC/B,MAAM,yBAAyBA,MAAAA;AAE/B,SAAS,aAAoB,OAAgB,QAAqB;AAC5D,MAAA,MAAM,QAAQ,MAAM,MAAM;AAAI,UAAM,KAAK,MAAM;AACrD;AAEA,SAAS,gBAAuB,OAAgB,QAAqB;AAC7D,QAAA,IAAI,MAAM,QAAQ,MAAM;AAC9B,MAAI,IAAI;AAAU,UAAA,OAAO,GAAG,CAAC;AAC/B;AAEA,MAAM,OAAO;AAAA,EAcX,YAAY,GAAY,IAAY;AAb7B;AACC;AAED;AACA;AAEA;AACA;AAEA,mCAAkB;AAClB,qCAAoB;AACpB,qCAAoB;AAGzB,SAAK,WAAW;AAChB,SAAK,KAAK;AAEV,SAAK,QAAQ;AACb,SAAK,YAAY;AAGjB,SAAK,eAAe;AACpB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EAEO,kBAAkB,QAAsB;AAChC,iBAAA,KAAK,WAAW,MAAM;AAAA,EACrC;AAAA,EAEO,oBAAoB,GAAiB;AAC1C,UAAM,YAAY,KAAK;AACvB,UAAM,QAAQ,KAAK;AAEb,UAAA,SAAS,UAAU,QAAQ,CAAC;AAClC,QAAI,WAAW;AAAI;AACnB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAI,MAAM,CAAC,EAAE,UAAU,CAAC;AAAG;AAAA,IAC7B;AAEU,cAAA,OAAO,QAAQ,CAAC;AAAA,EAC5B;AACF;AAGA,MAAM,SAAS;AAAA,EAWb,YAAY,IAAY,IAAY,IAAY,GAAW,GAAW,GAAW;AAVzE;AACA;AACA;AAED;AACA;AACA;AAEA,kCAAS,IAAIA,MAAAA;AAGlB,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAI;AAET,SAAK,KAAK;AACV,SAAK,KAAK;AACV,SAAK,KAAK;AAEV,SAAK,cAAc;AAEhB,OAAA,MAAM,KAAK,IAAI;AAClB,OAAG,kBAAkB,EAAE;AACvB,OAAG,kBAAkB,EAAE;AAEpB,OAAA,MAAM,KAAK,IAAI;AAClB,OAAG,kBAAkB,EAAE;AACvB,OAAG,kBAAkB,EAAE;AAEpB,OAAA,MAAM,KAAK,IAAI;AAClB,OAAG,kBAAkB,EAAE;AACvB,OAAG,kBAAkB,EAAE;AAAA,EACzB;AAAA,EAEQ,gBAAsB;AACtB,UAAA,KAAK,KAAK,GAAG;AACb,UAAA,KAAK,KAAK,GAAG;AACb,UAAA,KAAK,KAAK,GAAG;AAEhB,OAAA,WAAW,IAAI,EAAE;AACjB,OAAA,WAAW,IAAI,EAAE;AACjB,OAAA,MAAM,EAAE,EAAE,UAAU;AAElB,SAAA,OAAO,KAAK,EAAE;AAAA,EACrB;AAAA,EAEO,UAAU,GAAoB;AACnC,WAAO,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK;AAAA,EACtD;AAAA,EAEO,cAAc,MAAc,MAAoB;AACrD,QAAI,SAAS,KAAK;AAAI,WAAK,KAAK;AAAA,aACvB,SAAS,KAAK;AAAI,WAAK,KAAK;AAAA,aAC5B,SAAS,KAAK;AAAI,WAAK,KAAK;AAErB,oBAAA,KAAK,OAAO,IAAI;AAC3B,SAAA,MAAM,KAAK,IAAI;AAEf,SAAA,oBAAoB,KAAK,EAAE;AAC3B,SAAA,GAAG,oBAAoB,IAAI;AAE3B,SAAA,oBAAoB,KAAK,EAAE;AAC3B,SAAA,GAAG,oBAAoB,IAAI;AAE3B,SAAA,oBAAoB,KAAK,EAAE;AAC3B,SAAA,GAAG,oBAAoB,IAAI;AAE3B,SAAA,GAAG,kBAAkB,KAAK,EAAE;AAC5B,SAAA,GAAG,kBAAkB,KAAK,EAAE;AAE5B,SAAA,GAAG,kBAAkB,KAAK,EAAE;AAC5B,SAAA,GAAG,kBAAkB,KAAK,EAAE;AAE5B,SAAA,GAAG,kBAAkB,KAAK,EAAE;AAC5B,SAAA,GAAG,kBAAkB,KAAK,EAAE;AAEjC,SAAK,cAAc;AAAA,EACrB;AACF;AAUA,MAAM,iBAAiB;AAAA,EACrB,cAAc;AAEN,mDAA0B,CAAC,GAAW,MAAsB;AAIlE,YAAM,aAAa,EAAE,SAAS,WAAW,EAAE,QAAQ;AACnD,UAAI,YAAY;AAEhB,YAAM,YAAY,CAAA;AAClB,UAAI,GACF,KAAK,EAAE,MAAM,QACb,MACA;AAGF,WAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AAChB,eAAA,EAAE,MAAM,CAAC;AAEZ,YAAA,KAAK,UAAU,CAAC,GAAG;AACrB,oBAAU,KAAK,IAAI;AAAA,QACrB;AAAA,MACF;AAIA,WAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACvB,YAAI,eAAe;AACZ,eAAA,EAAE,MAAM,CAAC;AAEhB,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,qBAAW,UAAU,CAAC;AAEtB,gBAAM,UAAU,KAAK,OAAO,IAAI,SAAS,MAAM;AAC/C,yBAAe,KAAK,IAAI,eAAe,QAAQ,WAAW,CAAC;AAAA,QAC7D;AAEY,oBAAA,KAAK,IAAI,WAAW,YAAY;AAAA,MAC9C;AAIA,YAAM,UAAU;AACZ,UAAA,UAAU,SAAS,GAAG;AAGZ,oBAAA;AAAA,MACd;AAEM,YAAA,MAAM,aAAa,YAAY;AAE9B,aAAA;AAAA,IAAA;AAcD,mDAA0B,CAAC,MAAoB;AAQjD,UAAA,EAAE,UAAU,WAAW,GAAG;AAE5B,UAAE,mBAAmB;AACrB,UAAE,eAAe;AAEjB;AAAA,MACF;AAEA,QAAE,eAAe;AACjB,QAAE,mBAAmB;AAGrB,eAAS,IAAI,GAAG,IAAI,EAAE,UAAU,QAAQ,KAAK;AAC3C,cAAM,eAAe,KAAK,wBAAwB,GAAG,EAAE,UAAU,CAAC,CAAC;AAE/D,YAAA,CAAC,EAAE,kBAAkB;AACrB,YAAA,mBAAmB,EAAE,UAAU,CAAC;AAClC,YAAE,eAAe;AACjB,YAAE,UAAU;AACZ,YAAE,YAAY;AACd,YAAE,YAAY;AAAA,QAChB;AAEE,UAAA;AACF,UAAE,aAAa;AAEX,YAAA,eAAe,EAAE,SAAS;AAC1B,YAAA,mBAAmB,EAAE,UAAU,CAAC;AAClC,YAAE,UAAU;AAAA,QACd;AAAA,MACF;AAGE,QAAA,eAAe,EAAE,YAAY,EAAE;AAAA,IAAA;AAI3B,sCAAa,CAAC,GAAa,UAA4B;AAC7D,sBAAgB,OAAO,CAAC;AAExB,UAAI,EAAE;AAAoB,wBAAA,EAAE,GAAG,OAAO,CAAC;AACvC,UAAI,EAAE;AAAoB,wBAAA,EAAE,GAAG,OAAO,CAAC;AACvC,UAAI,EAAE;AAAoB,wBAAA,EAAE,GAAG,OAAO,CAAC;AAGvC,YAAM,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;AAC5B,UAAI,IAAI;AAER,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,aAAK,GAAG,CAAC;AACJ,aAAA,IAAI,IAAI,KAAK,CAAC;AAEf,YAAA,CAAC,MAAM,CAAC;AAAI;AAEhB,WAAG,oBAAoB,EAAE;AACzB,WAAG,oBAAoB,EAAE;AAAA,MAC3B;AAAA,IAAA;AAGM,oCAAW,CAAC,UAAoB,OAAmB,GAAW,MAAoB;AAKxF,UAAI,CAAC,GAAG;AAED,aAAA,aAAa,GAAG,QAAQ;AAC7B;AAAA,MACF;AAEI,UAAA;AACJ,YAAM,cAAc,CAAA;AAEpB,WAAK,IAAI,GAAG,IAAI,EAAE,UAAU,QAAQ,KAAK;AACvC,oBAAY,KAAK,EAAE,UAAU,CAAC,CAAC;AAAA,MACjC;AAGA,WAAK,IAAI,EAAE,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AACxC,YAAI,EAAE,MAAM,CAAC,EAAE,UAAU,CAAC,GAAG;AAC3B,eAAK,WAAW,EAAE,MAAM,CAAC,GAAG,KAAK;AAAA,QACnC;AAAA,MACF;AAGA,WAAK,IAAI,EAAE,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AACxC,UAAE,MAAM,CAAC,EAAE,cAAc,GAAG,CAAC;AAAA,MAC/B;AAEK,WAAA,aAAa,GAAG,QAAQ;AAG7B,WAAK,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAClC,aAAA,wBAAwB,YAAY,CAAC,CAAC;AAAA,MAC7C;AAAA,IAAA;AAGM,2CAAkB,CAAC,aAA+B;AAGpD,UAAA,QAAQ,SAAS,CAAC;AAEtB,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAI,SAAS,CAAC,EAAE,eAAe,MAAM,cAAc;AACjD,kBAAQ,SAAS,CAAC;AAAA,QACpB;AAAA,MACF;AAEO,aAAA;AAAA,IAAA;AAGF,kCAAS,CAAC,UAA0B,UAAkC;AAC3E,iBAAW,SAAS;AACpB,YAAM,aAAa,SAAS;AAI5B,eAAS,QAAQ,YAAY;AAC3B,YAAI,SAAS;AAAY,mBAAS,gBAAgB,IAAI;AAAA,MACxD;AAEW,iBAAAC,oBAAAA,cAAkC,QAAQ;AAMrD,YAAM,WAAW,CAAA;AACjB,YAAM,QAAQ,CAAA;AAIR,YAAA,oBAAoB,SAAS,aAAa,UAAU;AAE1D,eAAS,IAAI,GAAG,IAAI,kBAAkB,OAAO,KAAK;AAChD,cAAM,IAAI,IAAID,MAAA,QAAA,EAAU,oBAAoB,mBAAmB,CAAC;AAEhE,cAAM,SAAS,IAAI,OAAO,GAAG,CAAC;AAC9B,iBAAS,KAAK,MAAM;AAAA,MACtB;AAIM,YAAA,YAAY,SAAS;AAE3B,UAAI,cAAc,MAAM;AACtB,iBAAS,IAAI,GAAG,IAAI,UAAU,OAAO,KAAK,GAAG;AACrC,gBAAA,IAAI,UAAU,KAAK,CAAC;AAC1B,gBAAM,IAAI,UAAU,KAAK,IAAI,CAAC;AAC9B,gBAAM,IAAI,UAAU,KAAK,IAAI,CAAC;AAE9B,gBAAM,WAAW,IAAI,SAAS,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC;AAC5E,gBAAM,KAAK,QAAQ;AAAA,QACrB;AAAA,MAAA,OACK;AACL,iBAAS,IAAI,GAAG,IAAI,kBAAkB,OAAO,KAAK,GAAG;AACnD,gBAAM,IAAI;AACV,gBAAM,IAAI,IAAI;AACd,gBAAM,IAAI,IAAI;AAEd,gBAAM,WAAW,IAAI,SAAS,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC;AAC5E,gBAAM,KAAK,QAAQ;AAAA,QACrB;AAAA,MACF;AAIA,eAAS,IAAI,GAAG,KAAK,SAAS,QAAQ,IAAI,IAAI,KAAK;AAC5C,aAAA,wBAAwB,SAAS,CAAC,CAAC;AAAA,MAC1C;AAEI,UAAA;AAEJ,UAAI,IAAI;AAER,aAAO,KAAK;AACG,qBAAA,KAAK,gBAAgB,QAAQ;AAE1C,YAAI,CAAC,YAAY;AACf,kBAAQ,IAAI,wCAAwC;AACpD;AAAA,QAAA,OACK;AACL,eAAK,SAAS,UAAU,OAAO,YAAY,WAAW,gBAA0B;AAAA,QAClF;AAAA,MACF;AAIM,YAAA,qBAAqB,IAAIE,MAAAA;AAC/B,YAAM,WAAW,CAAA;AACjB,UAAI,QAAQ,CAAA;AAIZ,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AAClC,cAAA,SAAS,SAAS,CAAC,EAAE;AAC3B,iBAAS,KAAK,OAAO,GAAG,OAAO,GAAG,OAAO,CAAC;AAAA,MAC5C;AAIA,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAC/B,cAAA,OAAO,MAAM,CAAC;AAEpB,cAAM,IAAI,SAAS,QAAQ,KAAK,EAAE;AAClC,cAAM,IAAI,SAAS,QAAQ,KAAK,EAAE;AAClC,cAAM,IAAI,SAAS,QAAQ,KAAK,EAAE;AAE5B,cAAA,KAAK,GAAG,GAAG,CAAC;AAAA,MACpB;AAIA,yBAAmB,aAAa,YAAY,IAAIC,MAAuB,uBAAA,UAAU,CAAC,CAAC;AACnF,yBAAmB,SAAS,KAAK;AAE1B,aAAA;AAAA,IAAA;AAAA,EAjSM;AAAA,EAsDP,aAAa,GAAW,UAA0B;AACxD,YAAQ,OAAO,EAAE,MAAM,WAAW,CAAC;AAE5B,WAAA,EAAE,UAAU,QAAQ;AACnB,YAAA,IAAI,EAAE,UAAU,IAAI;AACV,sBAAA,EAAE,WAAW,CAAC;AAAA,IAChC;AAEA,oBAAgB,UAAU,CAAC;AAAA,EAC7B;AAoOF;;"}