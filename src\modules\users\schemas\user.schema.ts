import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type UserDocument = User & Document;

export enum UserType {
  HOMEOWNER = 'homeowner',
  SERVICE_PROVIDER = 'service_provider',
  ADMIN = 'admin',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING_VERIFICATION = 'pending_verification',
}

@Schema({ _id: false })
export class Address {
  @ApiProperty({ description: '街道地址' })
  @Prop({ required: true })
  street: string;

  @ApiProperty({ description: '城市' })
  @Prop({ required: true })
  city: string;

  @ApiProperty({ description: '省份' })
  @Prop({ required: true })
  province: string;

  @ApiProperty({ description: '邮政编码' })
  @Prop({ required: true })
  postalCode: string;

  @ApiProperty({ description: '国家', default: 'CA' })
  @Prop({ default: 'CA' })
  country: string;

  @ApiProperty({ description: '地理坐标' })
  @Prop({
    type: {
      lat: { type: Number, required: true },
      lng: { type: Number, required: true },
    },
  })
  coordinates: {
    lat: number;
    lng: number;
  };
}

@Schema({ _id: false })
export class UserProfile {
  @ApiProperty({ description: '名字' })
  @Prop({ required: true })
  firstName: string;

  @ApiProperty({ description: '姓氏' })
  @Prop({ required: true })
  lastName: string;

  @ApiProperty({ description: '电话号码' })
  @Prop()
  phone: string;

  @ApiProperty({ description: '头像URL' })
  @Prop()
  avatar: string;

  @ApiProperty({ description: '首选语言', enum: ['en', 'zh', 'fr'] })
  @Prop({ enum: ['en', 'zh', 'fr'], default: 'en' })
  language: string;

  @ApiProperty({ description: '时区' })
  @Prop({ default: 'America/Toronto' })
  timezone: string;

  @ApiProperty({ description: '地址信息', type: Address })
  @Prop({ type: Address })
  address: Address;
}

@Schema({ _id: false })
export class NotificationPreferences {
  @ApiProperty({ description: '邮件通知' })
  @Prop({ default: true })
  email: boolean;

  @ApiProperty({ description: '短信通知' })
  @Prop({ default: false })
  sms: boolean;

  @ApiProperty({ description: '推送通知' })
  @Prop({ default: true })
  push: boolean;
}

@Schema({ _id: false })
export class PrivacySettings {
  @ApiProperty({ description: '显示个人资料' })
  @Prop({ default: true })
  showProfile: boolean;

  @ApiProperty({ description: '显示位置信息' })
  @Prop({ default: true })
  showLocation: boolean;
}

@Schema({ _id: false })
export class UserPreferences {
  @ApiProperty({ description: '通知偏好', type: NotificationPreferences })
  @Prop({ type: NotificationPreferences, default: () => ({}) })
  notifications: NotificationPreferences;

  @ApiProperty({ description: '隐私设置', type: PrivacySettings })
  @Prop({ type: PrivacySettings, default: () => ({}) })
  privacy: PrivacySettings;
}

@Schema({ _id: false })
export class AuthInfo {
  @ApiProperty({ description: '邮箱是否已验证' })
  @Prop({ default: false })
  emailVerified: boolean;

  @ApiProperty({ description: '手机是否已验证' })
  @Prop({ default: false })
  phoneVerified: boolean;

  @ApiProperty({ description: '最后登录时间' })
  @Prop()
  lastLogin: Date;

  @ApiProperty({ description: '登录尝试次数' })
  @Prop({ default: 0 })
  loginAttempts: number;

  @ApiProperty({ description: '锁定到期时间' })
  @Prop()
  lockUntil: Date;

  @ApiProperty({ description: '密码重置令牌' })
  @Prop()
  resetPasswordToken: string;

  @ApiProperty({ description: '密码重置令牌过期时间' })
  @Prop()
  resetPasswordExpires: Date;

  @ApiProperty({ description: '邮箱验证令牌' })
  @Prop()
  emailVerificationToken: string;
}

@Schema({ timestamps: true })
export class User {
  @ApiProperty({ description: '用户ID' })
  _id: Types.ObjectId;

  @ApiProperty({ description: '邮箱地址' })
  @Prop({ required: true, unique: true, lowercase: true })
  email: string;

  @ApiProperty({ description: '密码哈希' })
  @Prop({ required: true, select: false })
  passwordHash: string;

  @ApiProperty({ description: '用户类型', enum: UserType })
  @Prop({ enum: UserType, required: true })
  userType: UserType;

  @ApiProperty({ description: '用户状态', enum: UserStatus })
  @Prop({ enum: UserStatus, default: UserStatus.PENDING_VERIFICATION })
  status: UserStatus;

  @ApiProperty({ description: '用户资料', type: UserProfile })
  @Prop({ type: UserProfile, required: true })
  profile: UserProfile;

  @ApiProperty({ description: '用户偏好', type: UserPreferences })
  @Prop({ type: UserPreferences, default: () => ({}) })
  preferences: UserPreferences;

  @ApiProperty({ description: '认证信息', type: AuthInfo })
  @Prop({ type: AuthInfo, default: () => ({}) })
  auth: AuthInfo;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  // 虚拟字段
  @ApiProperty({ description: '全名' })
  get fullName(): string {
    return `${this.profile.firstName} ${this.profile.lastName}`;
  }

  @ApiProperty({ description: '是否被锁定' })
  get isLocked(): boolean {
    return !!(this.auth.lockUntil && this.auth.lockUntil > new Date());
  }
}

export const UserSchema = SchemaFactory.createForClass(User);

// 索引
UserSchema.index({ email: 1 }, { unique: true });
UserSchema.index({ userType: 1, status: 1 });
UserSchema.index({ 'profile.address.city': 1, 'profile.address.province': 1 });
UserSchema.index({ 'profile.address.coordinates': '2dsphere' });
UserSchema.index({ createdAt: 1 });

// 虚拟字段
UserSchema.virtual('fullName').get(function () {
  return `${this.profile.firstName} ${this.profile.lastName}`;
});

UserSchema.virtual('isLocked').get(function () {
  return !!(this.auth.lockUntil && this.auth.lockUntil > new Date());
});

// 转换为JSON时的设置
UserSchema.set('toJSON', {
  virtuals: true,
  transform: function (doc, ret) {
    delete ret.passwordHash;
    delete ret.__v;
    return ret;
  },
});
