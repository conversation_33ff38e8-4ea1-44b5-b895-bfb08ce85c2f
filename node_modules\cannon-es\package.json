{"name": "cannon-es", "version": "0.20.0", "license": "MIT", "description": "A lightweight 3D physics engine written in JavaScript.", "homepage": "https://github.com/schteppe/cannon.js", "author": "<PERSON> <<EMAIL>> (http://steffe.se)", "contributors": ["<PERSON> <<EMAIL>> (https://github.com/codynova)", "<PERSON> <<EMAIL>> (https://github.com/marcofugaro)"], "keywords": ["cannon.js", "cannon", "physics", "engine", "3d"], "main": "./dist/cannon-es.cjs.js", "module": "./dist/cannon-es.js", "react-native": "./dist/cannon-es.js", "types": "./dist/cannon-es.d.ts", "sideEffects": false, "files": ["dist/"], "scripts": {"prepare": "husky install", "start": "npx serve", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "npm run typegen && rollup -c", "prepublishOnly": "npm run build && npm run test", "typecheck": "tsc --noEmit --emitDeclarationOnly false --strict", "typegen": "tsc --outFile dist/cannon-es.d.ts", "generate-docs": "typedoc", "test": "jest"}, "repository": {"type": "git", "url": "https://github.com/pmndrs/cannon-es.git"}, "bugs": {"url": "https://github.com/pmndrs/cannon-es/issues"}, "devDependencies": {"@babel/core": "^7.16.5", "@babel/preset-env": "^7.16.5", "@babel/preset-typescript": "^7.16.5", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.1.1", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^27.0.3", "@types/node": "^17.0.4", "husky": "^7.0.4", "jest": "^27.4.5", "lint-staged": "^12.1.4", "prettier": "^2.5.1", "rimraf": "^3.0.2", "rollup": "^2.62.0", "rollup-plugin-filesize": "^9.1.1", "ts-jest": "^27.1.2", "ts-node": "^10.4.0", "typedoc": "^0.22.10", "typescript": "^4.5.4"}, "lint-staged": {"*.{js,json,jsx,ts,tsx,md,yaml,yml}": "prettier --write"}}